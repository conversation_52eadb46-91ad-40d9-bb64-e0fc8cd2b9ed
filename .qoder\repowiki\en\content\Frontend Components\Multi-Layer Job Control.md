# Multi-Layer Job Control

<cite>
**Referenced Files in This Document**   
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [print.py](file://backend/app/api/print.py)
- [MultiLayerJobControl.test.js](file://frontend/src/components/__tests__/MultiLayerJobControl.test.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The MultiLayerJobControl component serves as the central interface for managing multi-material 3D printing workflows in the APIRecoater_Ethernet system. This document provides a comprehensive analysis of its role in orchestrating complex print jobs across three drums, detailing its interactions with backend services, state management patterns, and coordination mechanisms. The component enables operators to upload CLI files, initiate multi-material jobs, monitor progress, and handle job lifecycle events through a user-friendly interface.

## Project Structure
The APIRecoater_Ethernet project follows a clear separation between frontend and backend components, with well-defined responsibilities for each layer. The MultiLayerJobControl component resides in the frontend's components directory and interacts with backend services through API calls.

```mermaid
graph TD
subgraph "Frontend"
A[MultiLayerJobControl.vue]
B[printJobStore.js]
C[api.js]
end
subgraph "Backend"
D[multilayer_job_manager.py]
E[multilayer_job.py]
F[print.py]
end
A --> B
B --> C
C --> F
F --> D
D --> E
```

**Diagram sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [project_structure](file://workspace_path)

## Core Components
The MultiLayerJobControl component is the primary interface for managing multi-material print jobs. It coordinates file uploads for three drums, validates job prerequisites, and manages the job lifecycle through interactions with the printJobStore. The component displays real-time status information and provides controls for starting and canceling jobs.

The printJobStore serves as the central state management hub, maintaining the current job state, drum statuses, and error conditions. It acts as an intermediary between the UI components and backend API services, ensuring consistent state across the application.

```mermaid
classDiagram
class MultiLayerJobControl {
+isUploading : boolean
+isRefreshing : boolean
+uploadErrors : object
+successMessage : string
+errorMessage : string
+handleFileUpload(event, drumId)
+startJob()
+cancelJob()
+refreshStatus()
+getJobStatusClass()
+getJobStatusText()
+getDrumStatusClass(drumId)
}
class printJobStore {
+multiMaterialJob : object
+errorFlags : object
+isStartingJob : boolean
+isCancellingJob : boolean
+startMultiMaterialJob()
+cancelMultiMaterialJob()
+fetchJobStatus()
+setFileUploaded(drumId, fileData)
+clearUploadedFiles()
}
class apiService {
+uploadCliFile(file)
+startMultiMaterialJob(fileIds)
+cancelMultiMaterialJob()
+getMultiMaterialJobStatus()
}
MultiLayerJobControl --> printJobStore : "uses"
printJobStore --> apiService : "uses"
```

**Diagram sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue#L130-L364)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L0-L45)

**Section sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

## Architecture Overview
The multi-layer job control system follows a layered architecture with clear separation of concerns between UI, state management, and backend services. The frontend components interact with a centralized Pinia store that manages application state and orchestrates API calls to the backend.

```mermaid
sequenceDiagram
participant UI as "MultiLayerJobControl"
participant Store as "printJobStore"
participant API as "Backend API"
participant Manager as "multilayer_job_manager"
UI->>Store : handleFileUpload(file, drumId)
Store->>API : uploadCliFile(file)
API->>Manager : add_cli_file(file_id, cli_file)
Manager-->>API : file_id
API-->>Store : success response
Store->>Store : setFileUploaded(drumId, fileData)
UI->>Store : startMultiMaterialJob()
Store->>API : startMultiMaterialJob(fileIds)
API->>Manager : create_job(file_ids)
Manager->>Manager : create_job()
Manager->>Manager : start_job()
Manager-->>API : job_state
API-->>Store : job_id
Store->>Store : updateJobStatus(jobData)
loop Auto-refresh
Store->>API : getMultiMaterialJobStatus()
API->>Manager : get_job_status()
Manager-->>API : status_data
API-->>Store : status_response
Store->>Store : updateJobStatus(statusData)
end
```

**Diagram sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

## Detailed Component Analysis

### MultiLayerJobControl Analysis
The MultiLayerJobControl component provides a comprehensive interface for managing multi-material print jobs. It features a three-column layout for uploading CLI files to each of the three drums, with visual indicators showing the upload status for each drum.

The component implements several key methods for handling job operations:
- **handleFileUpload**: Processes CLI file uploads for individual drums, validates file types, and updates the store with upload results
- **startJob**: Initiates a multi-material job through the printJobStore when all prerequisites are met
- **cancelJob**: Terminates an active job and clears uploaded file data
- **refreshStatus**: Manually refreshes the job status from the backend

The component also implements automatic status refreshing every 5 seconds when an active job is running, ensuring the UI remains synchronized with the current job state.

```mermaid
flowchart TD
Start([Component Mounted]) --> AutoRefresh["Start Auto-refresh (5s interval)"]
AutoRefresh --> CheckActive{"Has Active Job?"}
CheckActive --> |Yes| FetchStatus["fetchJobStatus()"]
FetchStatus --> UpdateUI["Update UI with latest status"]
UpdateUI --> AutoRefresh
CheckActive --> |No| Wait["Wait for next interval"]
Wait --> AutoRefresh
subgraph "File Upload"
UploadClick["User clicks upload label"] --> FileSelected{"File Selected?"}
FileSelected --> |No| End1([End])
FileSelected --> |Yes| ValidateType{"Valid .cli file?"}
ValidateType --> |No| ShowError["Show error message"]
ValidateType --> |Yes| CallAPI["Call uploadCliFile API"]
CallAPI --> |Success| UpdateStore["Update printJobStore"]
CallAPI --> |Error| ShowUploadError["Show upload error"]
UpdateStore --> ShowSuccess["Show success message"]
end
subgraph "Job Control"
StartBtn["User clicks Start Job"] --> CanStart{"Can Start Job?"}
CanStart --> |No| ShowStartError["Show prerequisites"]
CanStart --> |Yes| CallStartAPI["Call startMultiMaterialJob API"]
CallStartAPI --> |Success| ShowStartSuccess["Show success message"]
CallStartAPI --> |Error| ShowStartFailure["Show error message"]
CancelBtn["User clicks Cancel Job"] --> HasActive{"Has Active Job?"}
HasActive --> |No| End2([End])
HasActive --> |Yes| CallCancelAPI["Call cancelMultiMaterialJob API"]
CallCancelAPI --> |Success| ClearFiles["Clear uploaded files"]
CallCancelAPI --> |Error| ShowCancelError["Show error message"]
end
```

**Diagram sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue#L130-L364)

**Section sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)

### Job State Management Analysis
The system employs a sophisticated state management pattern to track multi-material job progress across three drums. The MultiMaterialJobState class in the backend defines the complete state structure, which is mirrored in the frontend's printJobStore.

Key state properties include:
- **Job identification**: Unique job_id and file_ids mapping for each drum
- **Progress tracking**: total_layers, current_layer, and progress_percentage calculation
- **Drum coordination**: Individual drum states with ready, uploaded, and error status
- **Timing information**: start_time, last_layer_time, and estimated_completion
- **Error handling**: error_message and retry_count for fault recovery

The state synchronization between frontend and backend occurs through periodic polling and event-driven updates, ensuring all clients have a consistent view of the job status.

```mermaid
classDiagram
class MultiMaterialJobState {
+job_id : string
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+remaining_layers : Dict[int, List[LayerData]]
+header_lines : Dict[int, List[str]]
+is_active : bool
+status : JobStatus
+start_time : Optional[float]
+last_layer_time : Optional[float]
+estimated_completion : Optional[float]
+waiting_for_print_start : bool
+waiting_for_layer_complete : bool
+error_message : str
+retry_count : int
+drums : Dict[int, DrumState]
+get_progress_percentage() : float
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset()
}
class LayerData {
+layer_number : int
+cli_data : bytes
+is_empty : bool
+upload_time : Optional[float]
+completion_time : Optional[float]
}
class JobStatus {
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
MultiMaterialJobState --> DrumState : "contains"
MultiMaterialJobState --> LayerData : "contains"
MultiMaterialJobState --> JobStatus : "uses"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L67-L110)

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

### API Integration Analysis
The MultiLayerJobControl component interacts with backend services through a well-defined API interface. The key API endpoints enable complete job lifecycle management:

- **POST /api/v1/print/cli/upload**: Uploads CLI files for individual drums
- **POST /api/v1/print/multimaterial-job/start**: Initiates a multi-material job with specified CLI files
- **POST /api/v1/print/multimaterial-job/cancel**: Terminates an active job
- **GET /api/v1/print/multimaterial-job/status**: Retrieves current job status
- **POST /api/v1/print/multimaterial-job/clear-error**: Clears error flags for recovery

The API integration follows a consistent pattern where the frontend component calls store methods, which in turn make API requests and update the application state based on the responses.

```mermaid
sequenceDiagram
participant UI as "MultiLayerJobControl"
participant Store as "printJobStore"
participant API as "apiService"
participant Backend as "Backend"
UI->>Store : startJob()
Store->>Store : validate prerequisites
Store->>API : startMultiMaterialJob(fileIds)
API->>Backend : POST /multimaterial-job/start
Backend->>Backend : create_job(file_ids)
Backend->>Backend : start_job()
Backend-->>API : JobResponse
API-->>Store : response data
Store->>Store : updateJobStatus()
Store-->>UI : update UI
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py#L850-L914)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L202-L249)
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue#L254-L297)

**Section sources**
- [print.py](file://backend/app/api/print.py)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)

## Dependency Analysis
The MultiLayerJobControl component has a well-defined dependency chain that ensures separation of concerns while maintaining functionality. The component depends on the printJobStore for state management, which in turn depends on the apiService for backend communication.

```mermaid
graph TD
A[MultiLayerJobControl.vue] --> B[printJobStore.js]
B --> C[api.js]
C --> D[print.py]
D --> E[multilayer_job_manager.py]
E --> F[multilayer_job.py]
B --> G[status.js]
D --> H[cli_parser.py]
E --> I[opcua_coordinator.py]
```

**Diagram sources**
- [MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [project_structure](file://workspace_path)

## Performance Considerations
The multi-layer job control system incorporates several performance optimizations to handle large job sequences efficiently:

1. **Optimized Data Transfer**: The system minimizes data transfer between frontend and backend by only sending essential job status information. The full CLI file data remains on the server, with the frontend only receiving metadata like file_id, layer count, and status.

2. **Efficient Polling Strategy**: The auto-refresh mechanism uses a 5-second interval for job status updates, balancing real-time feedback with network efficiency. The polling only occurs when an active job is running, reducing unnecessary requests during idle periods.

3. **State Management**: The Pinia store implementation ensures that state changes are efficiently propagated to components only when necessary, minimizing re-renders and improving UI responsiveness.

4. **Error Handling**: The system implements robust error handling with retry mechanisms and error flag management, reducing the need for manual intervention and improving overall system reliability.

5. **Scalability**: The architecture supports multi-material printing with 1-3 drums, allowing for flexible configuration while maintaining consistent performance characteristics.

## Troubleshooting Guide
When encountering issues with the MultiLayerJobControl component, consider the following common problems and solutions:

**Section sources**
- [MultiLayerJobControl.test.js](file://frontend/src/components/__tests__/MultiLayerJobControl.test.js)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

### File Upload Issues
- **Problem**: CLI file upload fails with "Upload failed" message
- **Solution**: Verify the file has a .cli extension and is not empty. Check backend logs for parsing errors.

### Job Start Failures
- **Problem**: "Cannot start job" button is disabled
- **Solution**: Ensure all three CLI files are uploaded and there are no system errors. Verify the recoater connection is active.

### Status Synchronization Problems
- **Problem**: Job status not updating in real-time
- **Solution**: Check the auto-refresh interval (5 seconds) and verify the backend API is responding to status requests. Inspect browser console for network errors.

### Error Recovery
- **Problem**: System shows error state after job cancellation
- **Solution**: Use the "Clear Error Flags" functionality to reset error conditions and return to a ready state.

## Conclusion
The MultiLayerJobControl component serves as a critical interface for managing complex multi-material 3D printing workflows in the APIRecoater_Ethernet system. Through its integration with the printJobStore and backend services, it provides a robust and user-friendly way to orchestrate print jobs across three drums. The component's design emphasizes clear status visualization, comprehensive error handling, and efficient state synchronization, making it a reliable tool for operators managing advanced printing operations. The well-structured architecture and clear separation of concerns between frontend and backend components ensure maintainability and scalability for future enhancements.