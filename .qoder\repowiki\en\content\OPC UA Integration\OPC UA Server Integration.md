# OPC UA Server Integration

<cite>
**Referenced Files in This Document**   
- [opcua_server.py](file://backend\app\services\opcua_server.py) - *Updated with type coercion for OPC UA variables*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Configuration definitions for OPC UA server and variables*
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py) - *High-level interface for OPC UA operations*
- [multilayer_job_manager.py](file://backend\app\services\multilayer_job_manager.py) - *Job status updates via OPC UA coordinator*
- [status_poller.py](file://backend\app\services\status_poller.py) - *System status monitoring component*
</cite>

## Update Summary
**Changes Made**   
- Updated **Performance Considerations** section to include type coercion implementation details
- Added detailed explanation of type coercion mechanism in **Dynamic Node Creation** section
- Enhanced **Real-Time Data Publication** section with updated data flow accuracy
- Added specific source references for all updated sections
- Updated code examples to reflect actual implementation with type safety

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The OPC UA Server Integration component in APIRecoater_Ethernet enables industrial-grade communication between the backend system and external monitoring systems such as SCADA and historian databases. This document details the implementation of an OPC UA server that exposes real-time state information from the multilayer printing process. The server is built using the `asyncua` library and follows OPC UA standards for secure, reliable, and interoperable data exchange. It provides dynamic node creation, variable management, and method exposure for remote diagnostics, supporting integration with TwinCAT PLCs and other OPC UA clients.

## Project Structure
The project follows a modular structure with clear separation between configuration, services, and application logic. The OPC UA functionality is primarily located in the `backend/app/services` and `backend/app/config` directories.

``mermaid
graph TD
backend[backend/] --> app[app/]
app --> services[services/]
app --> config[config/]
services --> opcua_server[opcua_server.py]
services --> opcua_coordinator[opcua_coordinator.py]
services --> multilayer_job_manager[multilayer_job_manager.py]
services --> status_poller[status_poller.py]
config --> opcua_config[opcua_config.py]
style opcua_server fill:#f9f,stroke:#333
style opcua_coordinator fill:#f9f,stroke:#333
style opcua_config fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

## Core Components
The OPC UA integration consists of three core components: the server manager, the coordinator, and the configuration system. The `OPCUAServerManager` handles low-level OPC UA protocol operations, while the `OPCUACoordinator` provides high-level business logic methods. Configuration is managed through `OPCUAServerConfig` and `CoordinationVariable` dataclasses, allowing environment-based overrides.

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L589)
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L293)

## Architecture Overview
The OPC UA server acts as a bridge between the internal backend state and external industrial systems. It exposes coordination variables that reflect the current state of print jobs, recoater status, and system health.

``mermaid
graph TB
subgraph "Backend Application"
FastAPI[FastAPI Server<br>Port 8000]
OPCUA[OPC UA Server<br>Port 4843]
end
subgraph "External Systems"
PLC[TwinCAT PLC<br>OPC UA Client]
SCADA[SCADA System]
Historian[Historian Database]
end
FastAPI < --> |write_variable/read_variable| OPCUA
OPCUA < --> |OPC UA Protocol| PLC
OPCUA < --> |OPC UA Protocol| SCADA
OPCUA < --> |OPC UA Protocol| Historian
style OPCUA fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L589)

## Detailed Component Analysis

### OPC UA Server Manager
The `OPCUAServerManager` class is responsible for initializing and managing the OPC UA server instance.

#### Class Diagram
``mermaid
classDiagram
class OPCUAServerManager {
+config : OPCUAServerConfig
+server : Optional[Server]
+namespace_idx : int
+variable_nodes : Dict[str, Any]
+_running : bool
+_restart_count : int
+_heartbeat_task : Optional[Task]
+start_server() bool
+stop_server() None
+write_variable(name, val) bool
+read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
-_create_coordination_variables() None
-_get_ua_data_type(str) ua.Type
-_heartbeat_loop() None
-_cleanup() None
-_handle_server_error(Exception) None
}
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)

#### Server Initialization and Namespace Configuration
The server is initialized through the `start_server()` method, which performs the following steps:
1. Creates an `asyncua.Server` instance
2. Sets the endpoint and server name from configuration
3. Registers a custom namespace
4. Creates coordination variables
5. Starts the server and heartbeat loop

```python
async def start_server(self) -> bool:
    self.server = Server()
    await self.server.init()
    self.server.set_endpoint(self.config.endpoint)
    self.server.set_server_name(self.config.server_name)
    self.namespace_idx = await self.server.register_namespace(self.config.namespace_uri)
    await self._create_coordination_variables()
    await self.server.start()
    self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
```

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L228-L264)

#### Dynamic Node Creation
Coordination variables are dynamically created from the `COORDINATION_VARIABLES` list defined in `opcua_config.py`. Each variable is created within a "RecoaterCoordination" folder in the OPC UA address space. The system now includes type coercion to prevent BadTypeMismatch errors.

``mermaid
flowchart TD
A[Start] --> B[Load COORDINATION_VARIABLES]
B --> C[Create Coordination Folder]
C --> D{For each variable}
D --> E[Create OPC UA Variable]
E --> F[Set Writable if needed]
F --> G[Store Node Reference]
G --> H{More variables?}
H --> |Yes| D
H --> |No| I[Complete]
```

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L150-L225)

### OPC UA Coordinator
The `OPCUACoordinator` provides a high-level interface for interacting with the OPC UA server, abstracting away low-level details.

#### Abstraction Layers
``mermaid
graph TD
A[Application Layer] --> |FastAPI Endpoints| B[Business Logic Layer]
B --> |OPCUACoordinator| C[Protocol Layer]
C --> |OPCUAServerManager| D[asyncua.Server]
style B fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L589)

#### High-Level Methods for Job Management
The coordinator exposes methods like `set_job_active()`, `update_layer_progress()`, and `set_recoater_ready_to_print()` that map directly to backend operations.

```python
async def set_job_active(self, total_layers: int) -> bool:
    success = True
    success &= await self.write_variable("job_active", True)
    success &= await self.write_variable("total_layers", total_layers)
    success &= await self.write_variable("current_layer", 0)
    return success
```

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L250-L270)

### Real-Time Data Publication
Real-time data from `MultilayerJobManager` and `StatusPoller` is published through OPC UA variables via the coordinator.

#### Data Flow from Job Manager
``mermaid
sequenceDiagram
participant JobManager as MultilayerJobManager
participant Coordinator as OPCUACoordinator
participant Server as OPCUAServerManager
JobManager->>Coordinator : set_job_active(total_layers)
Coordinator->>Server : write_variable("job_active", True)
Coordinator->>Server : write_variable("total_layers", value)
Coordinator->>Server : write_variable("current_layer", 0)
JobManager->>Coordinator : update_layer_progress(current)
Coordinator->>Server : write_variable("current_layer", current)
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend\app\services\multilayer_job_manager.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)

### Custom Data Types and Method Exposure
The system uses standard OPC UA data types (Boolean, Int32, String) as defined in the `CoordinationVariable` dataclass. While no custom complex data types are currently implemented, the architecture supports them through OPC UA structures.

```python
@dataclass
class CoordinationVariable:
    name: str
    node_id: str
    data_type: str  # "Boolean", "Int32", "String", etc.
    initial_value: Any
    writable: bool = True
    description: str = ""
```

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L60-L100)

### Security Implementation
The server supports configurable security policies, though the default configuration uses no security for internal networks.

#### Security Configuration
```python
@dataclass
class OPCUAServerConfig:
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
```

Security can be enabled via environment variables:
- `OPCUA_SECURITY_POLICY`: Basic256Sha256
- `OPCUA_SECURITY_MODE`: SignAndEncrypt
- `OPCUA_CERTIFICATE_PATH`: Path to certificate file
- `OPCUA_PRIVATE_KEY_PATH`: Path to private key file

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L101-L136)

### Configuration Options
The server configuration is highly customizable through environment variables.

#### Configuration Parameters
**Server Settings:**
- `OPCUA_SERVER_ENDPOINT`: Server endpoint URL
- `OPCUA_SERVER_NAME`: Human-readable server name
- `OPCUA_NAMESPACE_URI`: Namespace identifier
- `OPCUA_NAMESPACE_IDX`: Namespace numeric index

**Security Settings:**
- `OPCUA_SECURITY_POLICY`: Security policy
- `OPCUA_SECURITY_MODE`: Security mode
- `OPCUA_CERTIFICATE_PATH`: Certificate file path
- `OPCUA_PRIVATE_KEY_PATH`: Private key file path

**Connection Settings:**
- `OPCUA_CONNECTION_TIMEOUT`: Connection timeout in seconds
- `OPCUA_SESSION_TIMEOUT`: Session timeout in seconds

**Server Management:**
- `OPCUA_AUTO_RESTART`: Enable automatic restart
- `OPCUA_RESTART_DELAY`: Restart delay in seconds
- `OPCUA_MAX_RESTART_ATTEMPTS`: Maximum restart attempts

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L235-L256)

## Dependency Analysis
The OPC UA components have well-defined dependencies and interactions.

``mermaid
graph TD
OPCUACoordinator --> OPCUAServerManager
OPCUAServerManager --> asyncua.Server
OPCUACoordinator --> opcua_config
OPCUAServerManager --> opcua_config
MultilayerJobManager --> OPCUACoordinator
StatusPoller --> OPCUACoordinator
style OPCUACoordinator fill:#f9f,stroke:#333
style OPCUAServerManager fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)

## Performance Considerations
The server uses asynchronous programming with asyncio to handle multiple clients efficiently. The heartbeat loop runs every 5 seconds to maintain server responsiveness. 

The system now includes robust type coercion in the `write_variable` method to prevent BadTypeMismatch errors. When writing a variable, the system determines the expected data type from the configuration and coerces the value accordingly:

```python
# Determine expected data type from configuration to avoid BadTypeMismatch
expected_type = None
for var_def in COORDINATION_VARIABLES:
    if var_def.name == name:
        expected_type = var_def.data_type
        break

coerced_value = value
ua_variant = None

try:
    if expected_type == "Int32" and isinstance(value, (int, bool)):
        coerced_value = int(value)
        ua_variant = ua.Variant(coerced_value, ua.VariantType.Int32)
    elif expected_type == "Boolean":
        coerced_value = bool(value)
        ua_variant = ua.Variant(coerced_value, ua.VariantType.Boolean)
    elif expected_type == "String":
        coerced_value = "" if value is None else str(value)
        ua_variant = ua.Variant(coerced_value, ua.VariantType.String)
    # Additional type handling...
```

This type coercion ensures that values are properly converted to the expected OPC UA data types before being written to the server, improving integration reliability with OPC UA clients.

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L393-L455)

## Troubleshooting Guide
Common issues and solutions:

**Server Fails to Start:**
- Check if port 4843 is available
- Verify endpoint format is correct
- Ensure no other process is using the same namespace

**Variable Write Fails:**
- Check server is running
- Verify variable name exists in COORDINATION_VARIABLES
- Ensure data type matches expected type

**Connection Issues:**
- Verify firewall allows traffic on port 4843
- Check OPC UA client configuration
- Validate network connectivity

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L350-L400)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L200-L240)

## Conclusion
The OPC UA Server Integration provides a robust, standards-compliant interface for exposing backend state to industrial monitoring systems. Its modular design separates concerns between low-level protocol handling and high-level business logic, making it maintainable and extensible. The configuration system allows easy adaptation to different deployment environments, and the security model can be strengthened for production use. This integration enables seamless connectivity with SCADA systems and historian databases, facilitating real-time monitoring and data analysis for the recoater system.