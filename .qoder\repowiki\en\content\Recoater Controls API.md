# Recoater Controls API

<cite>
**Referenced Files in This Document**   
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [api.js](file://frontend/src/services/api.js)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides a comprehensive overview of the Recoater Controls API, which manages critical hardware components including blade, hopper, drum, and leveler operations in the Aerosint SPD Recoater system. The API enables precise control over recoater functions essential for additive manufacturing processes. It is implemented using FastAPI in the backend and consumed by a Vue.js frontend through a service wrapper. The system interfaces with physical hardware via OPC UA and HTTP protocols through the `recoater_client.py` module. This documentation details all endpoints, their parameters, error conditions, and integration points across the stack.

## Project Structure
The project follows a standard layered architecture with distinct backend and frontend directories. The backend is built with FastAPI and contains API routers, services, and client implementations for hardware communication. The frontend uses Vue.js with a component-based structure and centralized API service management.

```mermaid
graph TD
subgraph "Backend"
A[app/api/recoater_controls.py]
B[services/recoater_client.py]
C[app/main.py]
end
subgraph "Frontend"
D[src/services/api.js]
E[src/views/RecoaterView.vue]
F[src/components/HopperControl.vue]
end
A --> B
D --> A
E --> D
F --> D
style A fill:#4CAF50,stroke:#388E3C
style B fill:#2196F3,stroke:#1976D2
style D fill:#FF9800,stroke:#F57C00
style E fill:#9C27B0,stroke:#7B1FA2
style F fill:#E91E63,stroke:#C2185B
```

**Diagram sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [api.js](file://frontend/src/services/api.js)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)

**Section sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)

## Core Components
The core components of the Recoater Controls system include the API endpoints in `recoater_controls.py`, the hardware communication client in `recoater_client.py`, and the frontend service wrapper in `api.js`. These components work together to provide a complete control interface for the recoater hardware. The API uses Pydantic models for request validation and implements comprehensive error handling for hardware communication failures. The client abstracts HTTP communication with retry logic, while the frontend service provides a clean interface for Vue components.

**Section sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L1-L849)
- [recoater_client.py](file://backend/services/recoater_client.py#L1-L725)
- [api.js](file://frontend/src/services/api.js#L1-L587)

## Architecture Overview
The system architecture follows a three-tier pattern with a Vue.js frontend, FastAPI backend, and physical hardware layer. The frontend communicates with the backend via REST API calls through the `api.js` service wrapper. The backend routes requests to appropriate handlers in `recoater_controls.py`, which delegate to the `RecoaterClient` for hardware communication. The client makes HTTP requests to the recoater hardware with built-in retry logic and error handling.

```mermaid
sequenceDiagram
participant Frontend as "Frontend (Vue)"
participant ApiService as "api.js Service"
participant Backend as "FastAPI Backend"
participant Client as "RecoaterClient"
participant Hardware as "Recoater Hardware"
Frontend->>ApiService : setBladeScrewsMotion()
ApiService->>Backend : POST /recoater/drums/{id}/blade/screws/motion
Backend->>Client : set_blade_screws_motion()
Client->>Hardware : HTTP POST /drums/{id}/blade/screws/motion
Hardware-->>Client : 200 OK + response
Client-->>Backend : Response dict
Backend-->>ApiService : JSON response
ApiService-->>Frontend : Promise resolution
Note over Client,Hardware : Retry logic on connection errors<br/>Error translation to HTTP status codes
```

**Diagram sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [api.js](file://frontend/src/services/api.js)

## Detailed Component Analysis

### API Endpoints Analysis
The `recoater_controls.py` file implements REST endpoints for controlling all recoater components. Each endpoint follows a consistent pattern with proper error handling and logging.

#### Drum Control Endpoints
The drum control endpoints manage drum motion, ejection pressure, and suction pressure.

**POST /api/recoater/drums/{drum_id}/motion**
- **Description**: Creates a motion command for a drum
- **Parameters**:
  - `drum_id`: Integer (0-2) identifying the drum
  - `mode`: String ("absolute", "relative", "turns", "speed", "homing")
  - `speed`: Float > 0 (mm/s)
  - `distance`: Optional float (mm) for absolute/relative modes
  - `turns`: Optional float (number of turns)
- **Response**: 
```json
{
  "drum_id": 0,
  "motion_command": {"mode": "absolute", "speed": 5.0, "distance": 10.0},
  "response": {"status": "success"},
  "connected": true
}
```
- **Error Conditions**:
  - 400: Invalid parameters or API error
  - 503: Connection failure to recoater
  - 500: Internal server error

**Example curl request**:
```bash
curl -X POST http://localhost:8000/api/recoater/drums/0/motion \
  -H "Content-Type: application/json" \
  -d '{"mode": "absolute", "speed": 5.0, "distance": 10.0}'
```

#### Blade Control Endpoints
The blade control endpoints manage the scraping blade screws on each drum.

**POST /api/recoater/drums/{drum_id}/blade/screws/motion**
- **Description**: Creates a motion command for both blade screws
- **Parameters**:
  - `drum_id`: Integer (0+) identifying the drum
  - `mode`: String ("absolute", "relative", "homing")
  - `distance`: Optional float (µm) for absolute/relative modes
- **Response**:
```json
{
  "drum_id": 0,
  "motion_command": {"mode": "absolute", "distance": 500.0},
  "response": {"status": "executing"},
  "connected": true
}
```

**POST /api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion**
- **Description**: Creates a motion command for an individual blade screw
- **Parameters**:
  - `drum_id`: Integer identifying the drum
  - `screw_id`: Integer (0+) identifying the screw
  - `distance`: Float (µm) relative motion distance
- **Response**:
```json
{
  "drum_id": 0,
  "screw_id": 1,
  "motion_command": {"distance": 250.0},
  "response": {"status": "completed"},
  "connected": true
}
```

#### Leveler Control Endpoints
The leveler control endpoints manage the leveler pressure.

**PUT /api/recoater/leveler/pressure**
- **Description**: Sets the target pressure for the leveler
- **Parameters**:
  - `target`: Float ≥ 0 (pressure in Pa)
- **Response**:
```json
{
  "leveler_command": {"target": 15000},
  "response": {"current": 14800, "target": 15000},
  "connected": true
}
```

**GET /api/recoater/leveler/pressure**
- **Description**: Retrieves current leveler pressure information
- **Response**:
```json
{
  "leveler_pressure": {
    "maximum": 20000,
    "target": 15000,
    "value": 14800
  },
  "connected": true
}
```

**Section sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L150-L300)
- [recoater_client.py](file://backend/services/recoater_client.py#L200-L250)

### Hardware Client Implementation
The `RecoaterClient` class in `recoater_client.py` provides the interface between the API and the physical hardware.

```mermaid
classDiagram
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
+__init__(base_url : str, timeout : float)
+_make_request(method : str, endpoint : str, **kwargs) Dict[str, Any]
+get_drum_motion(drum_id : int) Dict[str, Any]
+set_drum_motion(drum_id : int, mode : str, speed : float, distance : float, turns : float) Dict[str, Any]
+cancel_drum_motion(drum_id : int) Dict[str, Any]
+get_blade_screws_motion(drum_id : int) Dict[str, Any]
+set_blade_screws_motion(drum_id : int, mode : str, distance : float) Dict[str, Any]
+cancel_blade_screws_motion(drum_id : int) Dict[str, Any]
+get_leveler_pressure() Dict[str, Any]
+set_leveler_pressure(target : float) Dict[str, Any]
}
class RecoaterConnectionError {
+__init__(message : str)
}
class RecoaterAPIError {
+__init__(message : str)
}
RecoaterClient --> RecoaterConnectionError : "raises"
RecoaterClient --> RecoaterAPIError : "raises"
RecoaterClient --> requests.Session : "uses"
```

**Diagram sources**
- [recoater_client.py](file://backend/services/recoater_client.py#L50-L400)

The client implements several key features:
- **Retry Logic**: Two attempts with 500ms delay between retries for connection errors
- **Error Translation**: Converts network exceptions to domain-specific exceptions
- **HTTP Abstraction**: Wraps requests library with consistent error handling
- **JSON Handling**: Automatically parses JSON responses and handles non-JSON responses gracefully

**Section sources**
- [recoater_client.py](file://backend/services/recoater_client.py#L1-L725)

### Frontend Integration
The frontend components consume the API through the `api.js` service wrapper, which provides a clean interface for Vue components.

#### API Service Wrapper
The `api.js` file exports an `apiService` object with methods corresponding to API endpoints.

```javascript
const apiService = {
  // Drum control methods
  getDrumMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/motion`)
  },
  
  setDrumMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/motion`, motionData)
  },
  
  // Blade control methods
  getBladeScrewsMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/motion`)
  },
  
  setBladeScrewsMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/motion`, motionData)
  },
  
  // Leveler control methods
  getLevelerPressure() {
    return apiClient.get('/recoater/leveler/pressure')
  },
  
  setLevelerPressure(target) {
    return apiClient.put('/recoater/leveler/pressure', { target })
  }
}
```

The service includes:
- **Interceptors**: Request and response logging
- **Consistent Error Handling**: Centralized error reporting
- **Type Safety**: JSDoc comments for parameter types
- **Promise-based**: Async operations return promises

#### Frontend Components
The `RecoaterView.vue` component serves as the main dashboard, orchestrating multiple control components:

```mermaid
flowchart TD
A[RecoaterView.vue] --> B[DrumControl]
A --> C[HopperControl]
A --> D[LevelerControl]
A --> E[statusStore]
B --> F[apiService.getDrumMotion]
B --> G[apiService.setDrumMotion]
C --> H[apiService.getBladeScrewsMotion]
C --> I[apiService.setBladeScrewsMotion]
D --> J[apiService.getLevelerPressure]
D --> K[apiService.setLevelerPressure]
E --> L[WebSocket updates]
style A fill:#9C27B0,stroke:#7B1FA2
style B fill:#4CAF50,stroke:#388E3C
style C fill:#E91E63,stroke:#C2185B
style D fill:#2196F3,stroke:#1976D2
```

**Diagram sources**
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [api.js](file://frontend/src/services/api.js)

The `HopperControl.vue` component specifically handles blade operations:

```javascript
// In HopperControl.vue template
<HopperControl
  :drum-id="parseInt(drumId)"
  :blade-screws="drumInfo?.blade_screws || []"
  :blade-motion="drumInfo?.blade_motion || {}"
  @motion-started="handleMotionStarted"
  @motion-cancelled="handleMotionCancelled"
/>
```

It emits events for state changes and errors, which are handled by the parent `RecoaterView.vue` component for global notification.

**Section sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue#L1-L350)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)

## Dependency Analysis
The system has a clear dependency hierarchy with well-defined interfaces between layers.

```mermaid
graph TD
A[RecoaterView.vue] --> B[api.js]
B --> C[recoater_controls.py]
C --> D[recoater_client.py]
D --> E[Recoater Hardware]
F[HopperControl.vue] --> B
G[LevelerControl.vue] --> B
H[statusStore.js] --> I[WebSocket]
A --> H
style A fill:#9C27B0,stroke:#7B1FA2
style B fill:#FF9800,stroke:#F57C00
style C fill:#4CAF50,stroke:#388E3C
style D fill:#2196F3,stroke:#1976D2
style E fill:#607D8B,stroke:#455A64
style F fill:#E91E63,stroke:#C2185B
style G fill:#2196F3,stroke:#1976D2
style H fill:#795548,stroke:#5D4037
style I fill:#00BCD4,stroke:#00ACC1
```

**Diagram sources**
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [api.js](file://frontend/src/services/api.js)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)

Key dependency characteristics:
- **Unidirectional Flow**: Dependencies flow from frontend to hardware without cycles
- **Abstraction Layers**: Each layer abstracts the complexity of the layer below
- **Error Propagation**: Errors are translated appropriately at each layer
- **Configuration**: Base URLs and timeouts are configurable at the client level

## Performance Considerations
The system includes several performance optimizations:

1. **HTTP Connection Reuse**: The `RecoaterClient` uses a requests Session to reuse TCP connections
2. **Request Timeout**: Configurable timeout (default 5 seconds) prevents hanging requests
3. **Retry Logic**: Two attempts with 500ms delay balance reliability and responsiveness
4. **Frontend Caching**: Status store maintains state to reduce API calls
5. **WebSocket Integration**: Real-time updates reduce polling frequency

Potential improvements:
- Implement request batching for multiple operations
- Add client-side caching with expiration
- Optimize polling intervals based on system state
- Implement compression for large responses

## Troubleshooting Guide
Common issues and their solutions:

**Connection Errors (503)**
- **Cause**: Network issues or recoater hardware offline
- **Solution**: Verify network connectivity, check hardware power status, validate base URL configuration
- **Debug Steps**: 
  ```bash
  ping *************
  curl http://*************:8080/state
  ```

**Validation Errors (400)**
- **Cause**: Invalid parameters in request
- **Solution**: Check parameter types and ranges (e.g., speed > 0, drum_id 0-2)
- **Example Fix**:
  ```json
  // Wrong
  {"mode": "absolute", "speed": -5.0}
  // Correct
  {"mode": "absolute", "speed": 5.0}
  ```

**Hardware Timeouts (504)**
- **Note**: The current implementation uses 503 for connection issues rather than 504
- **Cause**: Hardware taking too long to respond
- **Solution**: Increase client timeout, check hardware for mechanical issues

**Authentication Issues**
- **Note**: Current implementation does not include authentication
- **Future Consideration**: If added, ensure tokens are properly managed in frontend

**Frontend Not Updating**
- **Cause**: WebSocket connection issues or status store problems
- **Solution**: Check browser console for errors, verify WebSocket endpoint

## Conclusion
The Recoater Controls API provides a robust interface for managing all aspects of the recoater system. Its layered architecture separates concerns effectively, with clear boundaries between frontend, backend, and hardware layers. The API design follows REST principles with consistent error handling and comprehensive documentation. The client implementation includes important reliability features like retry logic and proper error translation. Frontend integration is streamlined through the service wrapper pattern, making it easy for components to access API functionality. The system is well-structured for maintenance and extension, with opportunities for performance improvements in areas like caching and request optimization.