# Getting Started

<cite>
**Referenced Files in This Document**   
- [README.md](file://README.md)
- [config.py](file://config.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [install_deps.bat](file://install_deps.bat)
- [run.bat](file://run.bat)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/package.json](file://frontend/package.json)
- [opcua_simulator.py](file://opcua_simulator.py)
- [simple_connection_test.py](file://simple_connection_test.py)
</cite>

## Table of Contents
1. [System Overview](#system-overview)
2. [Prerequisites](#prerequisites)
3. [Installation Process](#installation-process)
4. [Configuration Basics](#configuration-basics)
5. [Running the Application](#running-the-application)
6. [Basic Usage Example](#basic-usage-example)
7. [OPC UA Simulator for Testing](#opc-ua-simulator-for-testing)
8. [Troubleshooting Common Issues](#troubleshooting-common-issues)
9. [Performance Considerations](#performance-considerations)

## System Overview

The APIRecoater_Ethernet project is a modern web-based Human-Machine Interface (HMI) designed for the Aerosint SPD Recoater system used in additive manufacturing processes. This system replaces the default SwaggerUI interface with a more intuitive and user-friendly frontend while providing a robust backend infrastructure.

The architecture follows a clear separation of concerns with a Python-based backend using FastAPI and a Vue.js 3 frontend powered by Vite. The system enables operators to control and monitor the recoater hardware through a clean, responsive interface suitable for both industrial and laboratory environments.

Key features of the system include:
- **Intuitive User Interface**: Designed based on the Aerosint User Manual for ease of operation
- **Real-time Monitoring**: Live status updates via WebSockets for system state, axis positions, and process parameters
- **Complete Control Functionality**: Full implementation of recoater controls including axis motion, drum/hopper/leveler management, and print job execution
- **OPC UA Integration**: Backend-PLC communication for multi-material print job coordination
- **Component-Based Architecture**: Modular design for maintainability and future expansion

The backend acts as a proxy between the frontend and the recoater hardware, handling all communication complexity while providing a simplified and stable API interface.

**Section sources**
- [README.md](file://README.md)

## Prerequisites

Before installing and running the APIRecoater_Ethernet system, ensure your development environment meets the following requirements:

### Software Requirements
- **Python 3.9+**: Required for the FastAPI backend services
- **Node.js v16+**: Required for the Vue.js frontend development and build tools
- **Aerosint Recoater System**: A running and accessible recoater hardware system for production use

### Development Environment Setup
The system is designed to work on Windows, Linux, and macOS platforms. For Windows users, the provided batch scripts (`.bat` files) simplify the installation and execution process.

### Network Requirements
- Ensure network connectivity between your development machine and the recoater hardware
- Verify that the IP address and port configuration in `config.py` match your recoater system setup
- For OPC UA communication, ensure ports 4843 (default OPC UA port) are accessible if using real hardware

These prerequisites are essential for both development and production deployments of the system.

**Section sources**
- [README.md](file://README.md)
- [config.py](file://config.py)

## Installation Process

The installation process for the APIRecoater_Ethernet project is streamlined through batch scripts that handle dependency installation for both backend and frontend components.

### Automated Installation with Batch Script

The recommended installation method uses the provided `install_deps.bat` script, which automates the entire dependency installation process:

```batch
@echo off
echo ============================================
echo  Recoater HMI - Installing Dependencies
echo ============================================
echo.

:: Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Python is not installed or not in PATH.
    echo Please install Python 3.7 or higher and try again.
    pause
    exit /b 1
)

:: Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed or not in PATH.
    echo Please install Node.js and try again.
    pause
    exit /b 1
)

echo [1/2] Installing Python dependencies...
cd backend
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install Python dependencies
    pause
    exit /b 1
)
cd ..

echo [2/2] Installing Node.js dependencies...
cd frontend
npm install
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install Node.js dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo ============================================
echo  Dependencies installed successfully!
echo  You can now run the application using run.bat
echo ============================================
echo.
pause
```

### Manual Installation Steps

If you prefer to install dependencies manually, follow these steps:

1. **Backend Installation**:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env and set the RECOATER_API_ADDRESS
```

2. **Frontend Installation**:
```bash
cd frontend
npm install
```

The automated batch script performs these same operations with additional error checking and user feedback, making it the preferred installation method.

**Section sources**
- [install_deps.bat](file://install_deps.bat)
- [README.md](file://README.md)

## Configuration Basics

The APIRecoater_Ethernet system uses multiple configuration files to manage settings for different components. Understanding these configuration files is essential for proper system setup.

### Main Configuration (config.py)

The primary configuration file `config.py` contains basic settings for connecting to the recoater hardware:

```python
# Recoater API Configuration
API_BASE_URL = "http://*************:8080"
API_TIMEOUT = 10.0  # seconds
```

Key configuration parameters:
- **API_BASE_URL**: The endpoint URL for the recoater hardware API
- **API_TIMEOUT**: Connection timeout in seconds for API requests

During development, you can modify these values:
```python
# For development/testing:
API_BASE_URL = "http://localhost:8000"  # For testing with mock backend
API_TIMEOUT = 5.0
```

### OPC UA Configuration (opcua_config.py)

The OPC UA configuration file defines settings for the OPC UA server that facilitates communication between the backend and PLC systems:

```python
@dataclass
class OPCUAServerConfig:
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    security_policy: str = "None"
    security_mode: str = "None"
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

### Environment Variables

All OPC UA configuration settings can be overridden using environment variables in a `.env` file:

- **OPCUA_SERVER_ENDPOINT**: Override the server endpoint URL
- **OPCUA_SERVER_NAME**: Override the server display name
- **OPCUA_NAMESPACE_URI**: Override the namespace identifier
- **OPCUA_CONNECTION_TIMEOUT**: Override connection timeout (seconds)
- **OPCUA_SESSION_TIMEOUT**: Override session timeout (seconds)

### Coordination Variables

The system defines several coordination variables that serve as shared data points between the FastAPI backend and PLC:

```python
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    # Job Control
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    # Additional variables...
]
```

These variables enable real-time communication without polling delays, with the backend hosting the variables as an OPC UA server and the PLC connecting as a client.

**Section sources**
- [config.py](file://config.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

## Running the Application

The APIRecoater_Ethernet system can be started using the provided `run.bat` script, which launches both the backend and frontend services in separate command windows.

### Automated Startup with Batch Script

The `run.bat` script automates the startup process:

```batch
@echo off
echo Starting Recoater Application...

:: Start Backend in a new window
start "Recoater Backend" cmd /k "cd backend && python -m uvicorn app.main:app --reload"

:: Wait for backend to start
timeout /t 5 /nobreak >nul

:: Start Frontend in a new window
start "Recoater Frontend" cmd /k "cd frontend && npm run dev"

:: Open browser after a short delay
timeout /t 5 /nobreak >nul
start "" http://localhost:5173

echo.
echo Application should be running at http://localhost:5173
echo If you see any errors, check the command windows that opened.
echo.
pause
```

### Manual Startup Process

If you prefer to start the services manually, follow these steps:

1. **Start the Backend**:
```bash
cd backend
uvicorn app.main:app --reload
```

2. **Start the Frontend**:
```bash
cd frontend
npm run dev
```

3. **Access the Application**:
Open your web browser and navigate to `http://localhost:5173`

### Backend Service Details

The backend is built with FastAPI and uses Uvicorn as the ASGI server. Key features include:
- **Lifespan Events**: The `lifespan` function in `main.py` handles startup and shutdown procedures
- **Service Initialization**: During startup, the system initializes the recoater client, OPC UA coordinator, and multilayer job manager
- **Background Tasks**: The system starts status polling and heartbeat tasks to maintain hardware connectivity

### Frontend Service Details

The frontend is a Vue.js 3 application built with Vite, providing:
- **Hot Module Replacement**: Automatic reloading during development
- **Optimized Build**: Production-ready builds with code splitting
- **WebSocket Integration**: Real-time communication with the backend

**Section sources**
- [run.bat](file://run.bat)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/package.json](file://frontend/package.json)

## Basic Usage Example

This section provides a step-by-step example of launching the server and connecting via the frontend interface.

### Step 1: Verify System Connectivity

Before starting the full application, test connectivity to the recoater hardware using the simple connection test script:

```python
#!/usr/bin/env python3
"""
Simple Recoater Connection Test
"""
import requests
import json
from config import API_BASE_URL, API_TIMEOUT

def test_connection():
    print(f"Trying to connect to: {API_BASE_URL}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/state", timeout=API_TIMEOUT)
        
        if response.status_code == 200:
            state_data = response.json()
            print("✅ SUCCESS! Connection to recoater established!")
            print(f"System State: {state_data.get('state', 'Unknown')}")
            return True
        else:
            print(f"❌ FAILED! Got error status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION FAILED!")
        print("Could not connect to the recoater system.")
        return False
```

Run this test with:
```bash
python simple_connection_test.py
```

### Step 2: Start the Application

Execute the run script to launch both services:
```bash
run.bat
```

This will open two command windows:
- **Recoater Backend**: Running the FastAPI server on port 8000
- **Recoater Frontend**: Running the Vite development server

### Step 3: Access the HMI Interface

The system will automatically open your default web browser to `http://localhost:5173`. If it doesn't open automatically, navigate to this address manually.

### Step 4: Verify System Status

Once the interface loads, you should see:
- Current system state (e.g., "Ready", "Idle", "Running")
- Axis positions and status indicators
- Drum, hopper, and leveler status

### Step 5: Perform Basic Operations

The interface provides access to various controls:
- **Axis Controls**: Move X, Y, Z axes to specific positions
- **Drum Controls**: Manage drum rotation and positioning
- **Hopper Controls**: Control material dispensing
- **Leveler Controls**: Adjust leveling mechanism
- **Print Controls**: Start, pause, and stop print jobs

The system uses WebSockets to provide real-time updates, so any changes in hardware state will be immediately reflected in the interface.

**Section sources**
- [simple_connection_test.py](file://simple_connection_test.py)
- [backend/app/main.py](file://backend/app/main.py)

## OPC UA Simulator for Testing

The `opcua_simulator.py` script provides a simulated OPC UA server environment for testing without requiring physical hardware. This is particularly useful for development and learning purposes.

### Purpose and Benefits

The OPC UA simulator allows developers to:
- Test OPC UA client functionality without real hardware
- Learn OPC UA server structure and data organization
- Develop and debug client applications in isolated environments
- Simulate various machine types and scenarios

### Supported Machine Types

The simulator can emulate different types of industrial machines:
- **3D Printer**: Simulates temperature, pressure, humidity, and build progress
- **CNC Machine**: Simulates spindle speed, vibration, tool wear, and status
- **Power Meter**: Simulates voltage, current, and power readings

### Configuration and Usage

The simulator is configured through a YAML file (`config.yaml`) that defines the servers to start:

```python
async def start_multiple_simulators(config):
    """
    Start multiple OPC UA simulators based on configuration.
    """
    simulators = []
    
    for server_config in config['development']['opcua_simulation']['servers']:
        simulator = OPCUAMachineSimulator(
            machine_type=server_config['machine_type'],
            port=server_config['port']
        )
        simulators.append(simulator)
        
    for simulator in simulators:
        await simulator.start_server()
```

To use the simulator:
1. Create a `config.yaml` file with your desired server configuration
2. Run the simulator script: `python opcua_simulator.py`
3. Connect your OPC UA client to the specified endpoints

The simulator will output connection information:
```
🔌 All OPC UA simulators running. Press Ctrl+C to stop.
📊 Connect your client to:
   - 3d_printer: opc.tcp://localhost:4840
   - cnc_machine: opc.tcp://localhost:4841
```

### Data Simulation

The simulator generates realistic data patterns for each machine type:
- **3D Printer**: Simulates temperature fluctuations and build progress
- **CNC Machine**: Simulates spindle speed variations and tool wear
- **Power Meter**: Simulates electrical readings with natural variations

This allows for comprehensive testing of client applications under various conditions without requiring physical equipment.

**Section sources**
- [opcua_simulator.py](file://opcua_simulator.py)

## Troubleshooting Common Issues

This section addresses common setup issues and provides troubleshooting tips for the APIRecoater_Ethernet system.

### Dependency Conflicts

**Issue**: Python or Node.js dependencies fail to install
**Solutions**:
- Verify Python and Node.js are properly installed and in your system PATH
- Use virtual environments for Python dependencies:
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # On Windows
pip install -r requirements.txt
```
- Clear npm cache if frontend installation fails:
```bash
npm cache clean --force
cd frontend
npm install
```

### Connection Timeouts

**Issue**: Connection to recoater hardware times out
**Solutions**:
- Verify the IP address in `config.py` matches your recoater system
- Check network connectivity between your machine and the recoater
- Ensure the USB3-to-RJ45 adapter is functioning properly
- Test with a shorter timeout value during development:
```python
API_TIMEOUT = 5.0  # Reduce from default 10.0 seconds
```

### Backend Startup Failures

**Issue**: Backend fails to start or crashes immediately
**Solutions**:
- Check that all required Python packages are installed
- Verify the `.env` file is properly configured
- Ensure no other service is using port 8000 (backend) or 5173 (frontend)
- Run the backend manually to see detailed error messages:
```bash
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Connection Issues

**Issue**: Frontend cannot connect to backend
**Solutions**:
- Verify the backend is running before starting the frontend
- Check CORS configuration in `main.py` includes your frontend origin:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```
- Ensure the backend is accessible from the frontend's network context

### OPC UA Communication Problems

**Issue**: OPC UA client cannot connect to the server
**Solutions**:
- Verify the OPC UA endpoint configuration:
```python
endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
```
- Check that port 4843 is not blocked by firewalls
- Ensure the namespace configuration matches between client and server
- Verify security settings are compatible (both set to "None" for development)

### General Troubleshooting Tips

1. **Check Log Output**: Monitor both backend and frontend console outputs for error messages
2. **Verify Configuration Files**: Double-check all configuration values in `.env`, `config.py`, and OPC UA settings
3. **Test Connectivity**: Use the `simple_connection_test.py` script to verify basic connectivity
4. **Restart Services**: Sometimes a complete restart of both backend and frontend resolves transient issues
5. **Update Dependencies**: Ensure all packages are up to date, especially OPC UA libraries

**Section sources**
- [config.py](file://config.py)
- [backend/app/main.py](file://backend/app/main.py)
- [simple_connection_test.py](file://simple_connection_test.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

## Performance Considerations

Understanding the performance characteristics of the APIRecoater_Ethernet system is crucial for optimizing both development and production deployments.

### Development Environment Performance

In development mode, the system prioritizes developer convenience over performance:

**Backend Characteristics**:
- **Hot Reload**: Enabled with `--reload` flag, which monitors file changes and restarts the server
- **Verbose Logging**: Detailed log output for debugging purposes
- **No Code Optimization**: Python code runs without optimization flags

**Frontend Characteristics**:
- **Development Server**: Vite development server with hot module replacement
- **Unminified Code**: JavaScript bundles are not minified for easier debugging
- **Source Maps**: Full source maps available for debugging

**Development Best Practices**:
- Use the provided batch scripts for consistent environment setup
- Keep the development database separate from production data
- Use the OPC UA simulator for testing when hardware is not available
- Monitor memory usage as development servers can consume significant resources

### Production Environment Performance

For production deployments, the system should be optimized for stability and efficiency:

**Backend Optimization**:
- **Disable Reload**: Remove `--reload` flag for production deployment
- **Production Server**: Consider using Gunicorn with Uvicorn workers for better performance
- **Logging Level**: Set to WARNING or ERROR to reduce log volume
- **Connection Pooling**: Implement database connection pooling if applicable

**Frontend Optimization**:
- **Production Build**: Use `npm run build` to create optimized static files
- **Static File Serving**: Serve frontend files through a dedicated web server (Nginx, Apache)
- **Compression**: Enable Gzip compression for static assets
- **Caching**: Configure proper cache headers for static resources

**Production Deployment Example**:
```bash
# Backend (production)
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

# Frontend (build and serve)
npm run build
# Serve dist/ folder with web server
```

### Resource Management

**Memory Usage**:
- Backend: Approximately 100-200MB baseline, scaling with connected clients
- Frontend: 50-100MB per browser tab, depending on data complexity

**CPU Usage**:
- Backend: Low to moderate, spikes during intensive operations
- Frontend: Moderate during data updates, low during idle periods

**Network Considerations**:
- WebSocket connections for real-time updates
- REST API calls for command and control operations
- OPC UA traffic between backend and PLC systems

### Scalability Recommendations

For high-availability or high-load scenarios:
- **Horizontal Scaling**: Deploy multiple backend instances behind a load balancer
- **Database Optimization**: Use a production-grade database with proper indexing
- **Caching Layer**: Implement Redis or similar for frequently accessed data
- **Monitoring**: Set up system monitoring for CPU, memory, and network usage

The system is designed to be efficient in resource usage while providing responsive performance for operator interfaces in both development and production environments.

**Section sources**
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/package.json](file://frontend/package.json)