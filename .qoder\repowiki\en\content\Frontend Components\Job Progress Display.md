# Job Progress Display

<cite>
**Referenced Files in This Document**   
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [print.py](file://backend/app/api/print.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The **JobProgressDisplay** component in the APIRecoater_Ethernet system provides a real-time visualization of active multi-material print jobs. It displays key metrics such as percentage completion, elapsed time, and estimated time remaining through a progress bar and textual indicators. This component is central to the user experience during print operations, offering immediate feedback on job status and system health. It integrates with the backend via a Pinia store (`printJobStore.js`) that subscribes to real-time updates from the server through periodic polling and WebSocket-like mechanisms. The component reflects the state of a complex 3-drum coordination system, where each drum may have different readiness, upload, and processing statuses.

## Project Structure
The project follows a standard Vue 3 + Pinia + Vite frontend architecture with a FastAPI backend. The **JobProgressDisplay** component resides in the `frontend/src/components` directory and is consumed by views such as `PrintView.vue`. It relies on the `printJobStore.js` Pinia store for state management, which in turn communicates with the backend via `api.js`. The backend's job management logic is implemented in `multilayer_job_manager.py`, which uses data models defined in `multilayer_job.py`. The API endpoints for job status and control are exposed in `print.py`.

```mermaid
graph TD
subgraph "Frontend"
A[JobProgressDisplay.vue]
B[printJobStore.js]
C[api.js]
end
subgraph "Backend"
D[print.py]
E[multilayer_job_manager.py]
F[multilayer_job.py]
end
A --> B
B --> C
C --> D
D --> E
E --> F
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)

## Core Components
The **JobProgressDisplay** component is a Vue 3 single-file component that visualizes the progress of a multi-material print job. It imports the `usePrintJobStore` Pinia store to access real-time job status, including `currentLayer`, `totalLayers`, `progressPercentage`, and `estimatedTimeRemaining`. The component renders a progress bar using the `jobProgress` computed property from the store and displays textual metrics such as elapsed time and layer status. It also handles error states by showing alerts when `backendError` or `plcError` flags are set in the store.

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

## Architecture Overview
The system architecture is a client-server model where the frontend Vue application communicates with a FastAPI backend over HTTP. The **JobProgressDisplay** component is part of the presentation layer, which binds to the state managed by the `printJobStore`. This store acts as a facade to the backend API, abstracting HTTP calls and providing a reactive interface. The backend's `multilayer_job_manager.py` service maintains the current job state and coordinates layer uploads across three drums. Status updates are pushed to the frontend through periodic polling of the `/print/multimaterial-job/status` endpoint.

```mermaid
graph TD
A[JobProgressDisplay.vue] --> B[printJobStore.js]
B --> C[api.js]
C --> D[/print/multimaterial-job/status]
D --> E[multilayer_job_manager.py]
E --> F[MultiMaterialJobState]
F --> G[DrumState]
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

## Detailed Component Analysis

### JobProgressDisplay.vue Analysis
The **JobProgressDisplay** component is a stateless UI element that renders job progress data from the `printJobStore`. It uses the `jobProgress` computed property to determine the width of the progress bar and displays the percentage in a badge. The component also shows the current layer and total layers in a textual format (e.g., "Layer 15/42"). For time metrics, it calculates elapsed time from `startTime` and displays `estimatedTimeRemaining` if available. The component conditionally renders error messages when the store's `hasCriticalError` computed property is true.

```vue
<template>
  <div class="job-progress-container">
    <div class="progress-header">
      <h3>Print Job Progress</h3>
      <span class="job-status">{{ multiMaterialJob.status }}</span>
    </div>
    
    <div class="progress-bar-outer">
      <div 
        class="progress-bar-inner" 
        :style="{ width: `${jobProgress}%` }"
      ></div>
      <span class="progress-text">{{ jobProgress }}%</span>
    </div>
    
    <div class="progress-metrics">
      <div>Layer {{ multiMaterialJob.currentLayer }} / {{ multiMaterialJob.totalLayers }}</div>
      <div v-if="multiMaterialJob.startTime">
        Elapsed: {{ elapsedMinutes }} min
      </div>
      <div v-if="multiMaterialJob.estimatedTimeRemaining">
        Remaining: {{ Math.round(multiMaterialJob.estimatedTimeRemaining / 60) }} min
      </div>
    </div>
    
    <div v-if="hasCriticalError" class="error-alert">
      {{ errorFlags.errorMessage }}
    </div>
  </div>
</template>
```

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)

### printJobStore.js Analysis
The `printJobStore.js` file defines a Pinia store that manages the state of multi-material print jobs. The store's state includes `multiMaterialJob`, which mirrors the `MultiMaterialJobState` from the backend, and `errorFlags` for system-level errors. Key computed properties include `jobProgress`, which calculates the completion percentage, and `canStartJob`, which determines if the job can be initiated based on file uploads and system status. The store provides actions like `fetchJobStatus` that call the backend API and update the state, triggering reactivity in components like **JobProgressDisplay**.

```mermaid
classDiagram
class printJobStore {
+multiMaterialJob : Object
+errorFlags : Object
+isLoading : boolean
+isStartingJob : boolean
+isCancellingJob : boolean
+isClearingErrors : boolean
+uploadedFiles : Object
+lastUploadedFiles : Object
+isJobActive : Computed<boolean>
+hasActiveJob : Computed<boolean>
+hasErrors : Computed<boolean>
+hasCriticalError : Computed<boolean>
+allDrumsReady : Computed<boolean>
+allFilesUploaded : Computed<boolean>
+hasMinimumFiles : Computed<boolean>
+canStartJob : Computed<boolean>
+jobProgress : Computed<number>
+updateJobStatus(statusData)
+updateDrumStatus(drumId, drumData)
+setErrorFlags(backendError, plcError, message)
+clearErrorFlags()
+closeCriticalModal()
+setFileUploaded(drumId, fileData)
+clearUploadedFiles()
+setLastUploadedFile(drumId, fileName, source)
+clearLastUploadedFiles()
+getLastUploadedFileName(drumId)
+resetJobState()
+startMultiMaterialJob()
+cancelMultiMaterialJob()
+clearErrorFlagsAPI()
+fetchJobStatus()
+fetchDrumStatus(drumId)
}
```

**Diagram sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

### Backend Job Management Analysis
The backend's job management is handled by the `MultiMaterialJobManager` class in `multilayer_job_manager.py`. This service creates and manages `MultiMaterialJobState` objects, which contain the current layer, total layers, and progress percentage. The `get_job_status` method returns a dictionary with these values, which is serialized and sent to the frontend via the `/print/multimaterial-job/status` endpoint. The manager also handles layer progression, updating `current_layer` and recalculating `progress_percentage` after each layer is processed.

```mermaid
classDiagram
class MultiMaterialJobState {
+job_id : str
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+is_active : bool
+status : JobStatus
+start_time : Optional[float]
+last_layer_time : Optional[float]
+estimated_completion : Optional[float]
+error_message : str
+retry_count : int
+drums : Dict[int, DrumState]
+get_progress_percentage() : float
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset()
}
class JobStatus {
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
MultiMaterialJobState --> DrumState : "contains"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

### API Integration Analysis
The frontend communicates with the backend through the `api.js` service, which wraps axios calls to the FastAPI endpoints. The `getMultiMaterialJobStatus` method calls the `/print/multimaterial-job/status` endpoint, which is implemented in `print.py`. This endpoint returns a `MultiMaterialJobStatusResponse` containing the job's progress percentage, current layer, and total layers. The response is mapped to the Pinia store's state, enabling the **JobProgressDisplay** component to update reactively.

```mermaid
sequenceDiagram
participant A as JobProgressDisplay.vue
participant B as printJobStore.js
participant C as api.js
participant D as print.py
participant E as multilayer_job_manager.py
A->>B : Access jobProgress computed property
B->>C : fetchJobStatus()
C->>D : GET /print/multimaterial-job/status
D->>E : get_job_status()
E-->>D : Job status data
D-->>C : HTTP 200 + JSON
C-->>B : Response data
B->>B : updateJobStatus(data)
B-->>A : State updated (reactive)
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)

## Dependency Analysis
The **JobProgressDisplay** component has a direct dependency on the `printJobStore.js` Pinia store, which depends on `api.js` for backend communication. The `api.js` service depends on axios for HTTP requests. On the backend, `print.py` depends on `multilayer_job_manager.py`, which depends on `multilayer_job.py` for data models. There are no circular dependencies in this chain.

```mermaid
graph TD
A[JobProgressDisplay.vue] --> B[printJobStore.js]
B --> C[api.js]
C --> D[axios]
C --> E[print.py]
E --> F[multilayer_job_manager.py]
F --> G[multilayer_job.py]
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

## Performance Considerations
The **JobProgressDisplay** component uses computed properties (`jobProgress`, `isJobActive`) which are cached and only re-evaluated when their dependencies change, ensuring efficient rendering. The `fetchJobStatus` action in `printJobStore.js` is typically called periodically (e.g., every 2 seconds) to update the progress. To prevent excessive server load, this polling interval should be optimized based on the layer processing time. For very short layers, debouncing or throttling the updates may be necessary to avoid progress "stuttering." The estimated time remaining is calculated based on the average time per layer, but this can be inaccurate if layer processing times vary significantly. A moving average or exponential smoothing could improve prediction accuracy.

## Troubleshooting Guide
Common issues with the **JobProgressDisplay** component include:
- **Progress not updating**: Verify that `fetchJobStatus` is being called regularly and that the backend endpoint returns valid data.
- **Incorrect percentage**: Check that `currentLayer` and `totalLayers` in the store are correct and that `jobProgress` is calculated as `(currentLayer / totalLayers) * 100`.
- **Stale display**: Ensure the component is properly subscribed to the Pinia store and that reactivity is not broken by direct state mutation.
- **Time prediction errors**: The estimated time remaining may be inaccurate for jobs with highly variable layer processing times. Consider implementing a more sophisticated prediction algorithm.

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

## Conclusion
The **JobProgressDisplay** component effectively visualizes the progress of multi-material print jobs by integrating with a well-structured backend system through a reactive Pinia store. Its design follows modern Vue 3 practices, using computed properties for efficient rendering and a clear separation of concerns between presentation, state management, and API communication. The component provides users with essential feedback on job status, enabling effective monitoring of the printing process.