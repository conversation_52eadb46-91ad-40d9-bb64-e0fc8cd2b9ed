# Data Models

<cite>
**Referenced Files in This Document**   
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [MultilayerJob Entity Overview](#multilayerjob-entity-overview)
3. [Field Definitions](#field-definitions)
4. [Job Status Lifecycle](#job-status-lifecycle)
5. [Entity Relationships](#entity-relationships)
6. [Data Access and State Management](#data-access-and-state-management)
7. [Performance and Real-Time Communication](#performance-and-real-time-communication)
8. [Data Lifecycle and Error Recovery](#data-lifecycle-and-error-recovery)
9. [Sample Data](#sample-data)
10. [Schema Diagram](#schema-diagram)

## Introduction
This document provides comprehensive documentation for the MultilayerJob entity in the APIRecoater_Ethernet system. The MultilayerJob model manages multi-material print jobs with 3-drum coordination, handling job lifecycle, drum synchronization, and layer progression tracking. This documentation details the data model structure, field definitions, validation rules, business logic, entity relationships, data access patterns, and performance considerations.

## MultilayerJob Entity Overview
The MultilayerJob entity, implemented as `MultiMaterialJobState` in the system, represents a multi-material print job with 3-drum coordination. It manages the complete state of a print job, including identification, scope, progress tracking, status, timing, process state, error handling, and drum coordination. The entity is central to the recoater operation, coordinating between multiple drums and managing the layer-by-layer printing process.

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L110)

## Field Definitions
The MultilayerJob entity contains several fields organized into logical groups:

### Job Identification
- **job_id**: Unique identifier for the job, generated using UUID4
- **file_ids**: Dictionary mapping drum_id (0,1,2) to file_id

### Job Scope and Progress
- **total_layers**: Total number of layers in the job (maximum across all drums)
- **current_layer**: Current layer being processed (1-based)
- **remaining_layers**: Dictionary mapping drum_id to list of LayerData objects
- **header_lines**: Dictionary mapping drum_id to list of CLI header lines

### Job Status
- **is_active**: Boolean indicating if the job is currently active
- **status**: JobStatus enum indicating the current state of the job

### Timing Information
- **start_time**: Timestamp when the job started
- **last_layer_time**: Timestamp of the last layer completion
- **estimated_completion**: Estimated time of job completion

### Process State Tracking
- **waiting_for_print_start**: Flag indicating if waiting for print start
- **waiting_for_layer_complete**: Flag indicating if waiting for layer completion

### Error Handling
- **error_message**: Error message if job is in error state
- **retry_count**: Number of retry attempts for failed operations

### Drum Coordination
- **drums**: Dictionary mapping drum_id (0,1,2) to DrumState objects

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L110)

## Job Status Lifecycle
The MultilayerJob entity implements a comprehensive state machine through the JobStatus enum, which defines the following states:

- **IDLE**: Initial state, no job active
- **INITIALIZING**: Job is being set up
- **WAITING_FOR_PRINT_START**: Ready to start printing
- **RECOATER_ACTIVE**: Recoater is actively operating
- **WAITING_FOR_DEPOSITION**: Waiting for material deposition
- **LAYER_COMPLETE**: Current layer completed successfully
- **JOB_COMPLETE**: All layers completed
- **ERROR**: Job encountered an error
- **CANCELLED**: Job was cancelled by user
- **RUNNING**: Job is currently running

The state transitions are managed by the MultilayerJobManager service, which handles the progression from IDLE to RUNNING when a job starts, through the layer processing states, and finally to JOB_COMPLETE, ERROR, or CANCELLED.

```mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> INITIALIZING : create_job()
INITIALIZING --> WAITING_FOR_PRINT_START : validation_success
WAITING_FOR_PRINT_START --> RECOATER_ACTIVE : start_job()
RECOATER_ACTIVE --> WAITING_FOR_DEPOSITION : layer_uploaded
WAITING_FOR_DEPOSITION --> LAYER_COMPLETE : deposition_complete
LAYER_COMPLETE --> RECOATER_ACTIVE : next_layer
LAYER_COMPLETE --> JOB_COMPLETE : all_layers_done
RECOATER_ACTIVE --> ERROR : operation_failed
WAITING_FOR_DEPOSITION --> ERROR : timeout
IDLE --> CANCELLED : cancel_job()
RECOATER_ACTIVE --> CANCELLED : cancel_job()
ERROR --> IDLE : clear_errors()
CANCELLED --> IDLE : reset()
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L15-L35)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L150-L200)

## Entity Relationships
The MultilayerJob entity has several key relationships with other components in the system:

### Configuration Parameters
The job relates to configuration parameters through the CLI files that are parsed and stored in the system. Each drum can have its own CLI file, which contains the specific parameters for that material. The system also supports empty templates for drums that are not in use.

### OPC UA Node Mappings
The job state is synchronized with OPC UA nodes through the OPCUACoordinator service, which maps job properties to OPC UA variables:
- job_active → OPC UA job_active variable
- job_id → OPC UA job_id variable
- total_layers → OPC UA total_layers variable
- current_layer → OPC UA current_layer variable
- coord_status → OPC UA coord_status variable

### DrumState Relationship
Each MultilayerJob contains three DrumState objects (for drums 0, 1, and 2), each tracking:
- Drum-specific status and readiness
- Layer progression for that drum
- Error state for that drum
- File association for that drum

```mermaid
classDiagram
class MultiMaterialJobState {
+job_id : str
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+is_active : bool
+status : JobStatus
+start_time : Optional[float]
+error_message : str
+drums : Dict[int, DrumState]
+get_progress_percentage() float
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset() void
}
class JobStatus {
+IDLE
+INITIALIZING
+WAITING_FOR_PRINT_START
+RECOATER_ACTIVE
+WAITING_FOR_DEPOSITION
+LAYER_COMPLETE
+JOB_COMPLETE
+ERROR
+CANCELLED
+RUNNING
}
class LayerData {
+layer_number : int
+cli_data : bytes
+is_empty : bool
+upload_time : Optional[float]
+completion_time : Optional[float]
}
MultiMaterialJobState --> DrumState : "has 3"
MultiMaterialJobState --> LayerData : "contains layers"
MultiMaterialJobState --> JobStatus : "has status"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L110)

## Data Access and State Management
The MultilayerJob entity is managed through a coordinated system of backend services and frontend stores.

### Backend Management
The MultilayerJobManager service handles all job operations:
- **Creation**: `create_job()` validates input, processes CLI files, and initializes job state
- **Start**: `start_job()` transitions job to RUNNING state and updates OPC UA variables
- **Cancel**: `cancel_job()` transitions job to CANCELLED state
- **Status**: `get_job_status()` returns current job state as dictionary

```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant OPCUA
Frontend->>Backend : startMultiMaterialJob(fileIds)
Backend->>Backend : validate files
Backend->>Backend : create_job()
Backend->>Backend : start_job()
Backend->>OPCUA : set_job_active(total_layers)
Backend->>OPCUA : update_layer_progress(current_layer)
Backend-->>Frontend : job started response
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L150-L300)

### Frontend Synchronization
The frontend printJobStore synchronizes state with the backend through API calls and WebSocket updates:

```mermaid
flowchart TD
A[User Action] --> B{Action Type}
B --> |Start Job| C[API: startMultiMaterialJob]
B --> |Cancel Job| D[API: cancelMultiMaterialJob]
B --> |Clear Errors| E[API: clearErrorFlags]
C --> F[Backend Updates Job State]
D --> F
E --> F
F --> G[WebSocket Broadcast]
G --> H[Frontend Store Update]
H --> I[UI Update]
```

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L320)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L150-L300)

## Performance and Real-Time Communication
The system implements WebSocket-based real-time communication to ensure immediate state synchronization between backend and frontend.

### WebSocket Broadcasting
The WebSocketConnectionManager handles real-time updates:
- Manages active connections
- Handles subscription filtering
- Broadcasts job state updates
- Filters messages based on client subscriptions

```mermaid
sequenceDiagram
participant Backend
participant WebSocketManager
participant Frontend
Backend->>WebSocketManager : broadcast(job_status)
WebSocketManager->>WebSocketManager : filter_message_for_connection()
loop All Active Connections
WebSocketManager->>Frontend : send_json(filtered_message)
Frontend->>Frontend : updateJobStatus()
end
Frontend->>Frontend : update UI
```

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

## Data Lifecycle and Error Recovery
The MultilayerJob entity follows a complete lifecycle from creation through completion or termination.

### Data Lifecycle
1. **Creation**: Job is created with CLI files for one or more drums
2. **Initialization**: Empty templates are added for missing drums if needed
3. **Execution**: Job progresses through layers, with coordination between drums
4. **Completion**: Job reaches JOB_COMPLETE state when all layers are processed
5. **Termination**: Job is removed from active state, either through completion, cancellation, or error

### Error Recovery
The system implements several error recovery mechanisms:
- **Error Flags**: Both backend and frontend maintain error flags
- **Clear Errors**: `clear_error_flags()` resets error states
- **Retry Logic**: Retry count is tracked for failed operations
- **Rollback**: Cancelled jobs are properly reset to IDLE state

```mermaid
flowchart TD
A[Error Occurs] --> B{Error Type}
B --> |Backend| C[Set error_message]
B --> |PLC| D[Set PLC error flag]
C --> E[Update OPC UA error variables]
D --> E
E --> F[WebSocket Broadcast]
F --> G[Frontend Shows Error Modal]
G --> H{User Action}
H --> |Clear Errors| I[API: clearErrorFlags]
I --> J[Reset error states]
J --> K[Return to IDLE]
```

**Section sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L600-L650)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L150-L200)

## Sample Data
Example of a typical multi-layer job:

```json
{
  "job_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "file_ids": {
    "0": "cli_file_001",
    "1": "cli_file_002",
    "2": "empty_template_123"
  },
  "total_layers": 150,
  "current_layer": 75,
  "is_active": true,
  "status": "recoater_active",
  "start_time": 1701234567.89,
  "error_message": "",
  "drums": {
    "0": {
      "drum_id": 0,
      "status": "ready",
      "ready": true,
      "uploaded": true,
      "current_layer": 75,
      "total_layers": 150,
      "error_message": "",
      "file_id": "cli_file_001"
    },
    "1": {
      "drum_id": 1,
      "status": "ready",
      "ready": true,
      "uploaded": true,
      "current_layer": 75,
      "total_layers": 150,
      "error_message": "",
      "file_id": "cli_file_002"
    },
    "2": {
      "drum_id": 2,
      "status": "idle",
      "ready": false,
      "uploaded": false,
      "current_layer": 0,
      "total_layers": 0,
      "error_message": "",
      "file_id": "empty_template_123"
    }
  }
}
```

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L110)

## Schema Diagram
The following diagram shows the complete schema of the MultilayerJob entity and its relationships:

```mermaid
erDiagram
MULTILAYER_JOB {
string job_id PK
json file_ids
int total_layers
int current_layer
boolean is_active
string status
float start_time
string error_message
}
DRUM_STATE {
int drum_id PK
string status
boolean ready
boolean uploaded
int current_layer
int total_layers
string error_message
string file_id
float last_update_time
}
LAYER_DATA {
int layer_number PK
bytes cli_data
boolean is_empty
float upload_time
float completion_time
}
JOB_STATUS {
string status_value PK
}
MULTILAYER_JOB ||--o{ DRUM_STATE : "contains"
MULTILAYER_JOB ||--o{ LAYER_DATA : "has layers"
MULTILAYER_JOB }|--|| JOB_STATUS : "has status"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L110)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L668)