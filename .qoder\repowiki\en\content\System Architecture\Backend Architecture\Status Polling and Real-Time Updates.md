# Status Polling and Real-Time Updates

<cite>
**Referenced Files in This Document**   
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status.py](file://backend/app/api/status.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [main.py](file://backend/app/main.py)
- [websockets.py](file://backend/app/websockets.py) - *Extracted from main.py in commit 8c43f1b*
- [status.js](file://frontend/src/stores/status.js)
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the refactoring of WebSocket functionality from main.py to websockets.py
- Added new section on WebSocketHandler class and its integration
- Updated System Integration and Data Flow section to reflect the new architecture
- Added sources for the new websockets.py file
- Updated sequence diagram to show the new WebSocketHandler component

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components Overview](#core-components-overview)
3. [Status Polling Service](#status-polling-service)
4. [WebSocket Connection Management](#websocket-connection-management)
5. [WebSocket Handler Implementation](#websocket-handler-implementation)
6. [Status API Endpoint](#status-api-endpoint)
7. [Data Gathering and Aggregation](#data-gathering-and-aggregation)
8. [Frontend Status Management](#frontend-status-management)
9. [System Integration and Data Flow](#system-integration-and-data-flow)
10. [Error Handling and Resilience](#error-handling-and-resilience)
11. [Scalability and Performance](#scalability-and-performance)
12. [Sequence Diagram](#sequence-diagram)

## Introduction
This document provides a comprehensive analysis of the status monitoring and real-time communication subsystem in the Recoater HMI application. The system enables continuous monitoring of device state through periodic polling of OPC UA clients and internal services, with real-time updates delivered to the frontend via WebSocket connections. The architecture is designed to provide consistent state representation across the application, with efficient data aggregation, health checks, and error handling. This documentation details the implementation of key components including the status poller, WebSocket manager, and their integration with the frontend client.

## Core Components Overview
The status monitoring subsystem consists of several interconnected components that work together to provide real-time system status updates. The backend implements a polling service that periodically collects device state from OPC UA clients and internal services, while the WebSocket manager handles real-time communication with connected frontend clients. The system is designed with scalability in mind, supporting multiple concurrent connections with subscription-based data filtering to minimize network overhead.

``mermaid
graph TD
A[StatusPollingService] --> B[DataGatherer]
B --> C[RecoaterClient]
A --> D[WebSocketManager]
D --> E[Frontend Clients]
F[Status API] --> C
F --> D
G[Frontend Status Store] --> D
H[WebSocketHandler] --> D
style A fill:#f9f,stroke:#333
style B fill:#f9f,stroke:#333
style D fill:#f9f,stroke:#333
style F fill:#f9f,stroke:#333
style G fill:#f9f,stroke:#333
style H fill:#f9f,stroke:#333
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status.py](file://backend/app/api/status.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [websockets.py](file://backend/app/websockets.py)
- [status.js](file://frontend/src/stores/status.js)

## Status Polling Service
The StatusPollingService is responsible for periodically collecting device state from OPC UA clients and internal services. It implements a background polling loop that executes at configurable intervals, gathering system status and broadcasting updates to connected clients.

### Polling Configuration
The polling interval is configurable through the WEBSOCKET_POLL_INTERVAL environment variable, with a default value of 1.0 seconds. This allows system administrators to adjust the polling frequency based on performance requirements and network conditions.

```python
self.poll_interval = float(os.getenv("WEBSOCKET_POLL_INTERVAL", "1.0"))
```

### Lifecycle Management
The service provides explicit start and stop methods to control the polling lifecycle. When started, it creates an asynchronous task that runs the polling loop until explicitly stopped.

```python
async def start(self) -> None:
    """Start the status polling task."""
    if self._running:
        logger.warning("Status polling is already running")
        return
        
    self._running = True
    self.polling_task = asyncio.create_task(self._polling_loop())
    logger.info("Status polling task started")
```

### Polling Loop Implementation
The polling loop continuously executes the _poll_and_broadcast method, with error handling to manage connection issues and other exceptions. The loop sleeps for the configured interval between iterations.

```python
async def _polling_loop(self) -> None:
    """Main polling loop that runs in the background."""
    while self._running:
        try:
            await self._poll_and_broadcast()
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.error(f"Status polling connection error: {e}")
            await self._broadcast_connection_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in status polling: {e}", exc_info=True)

        await asyncio.sleep(self.poll_interval)
```

### Data Collection and Broadcasting
The service collects data based on client subscriptions, ensuring that only relevant information is gathered and transmitted. This optimization reduces unnecessary API calls to the recoater hardware.

```python
async def _poll_and_broadcast(self) -> None:
    """Poll recoater status and broadcast updates."""
    recoater_client = get_recoater_client_instance()

    if not recoater_client or not self.websocket_manager.has_connections:
        return

    # Get required data types from all active connections
    required_data_types = self.websocket_manager.get_required_data_types()
    logger.debug(f"Required data types: {list(required_data_types)}")

    # Gather all required data
    gathered_data = await self.data_gatherer.gather_all_data(
        recoater_client, required_data_types
    )

    # Construct and broadcast message
    message = self.data_gatherer.construct_status_message(gathered_data)
    await self.websocket_manager.broadcast(message)
```

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-124)

## WebSocket Connection Management
The WebSocketConnectionManager handles real-time communication between the backend and connected frontend clients. It manages the connection lifecycle, subscription preferences, and message broadcasting.

### Connection Lifecycle
The manager handles the complete WebSocket connection lifecycle, from connection establishment to disconnection and cleanup.

```python
async def connect(self, websocket: WebSocket) -> None:
    """
    Accept a new WebSocket connection.
    
    Args:
        websocket: The WebSocket connection to accept
    """
    await websocket.accept()
    self.active_connections.append(websocket)
    # Initialize with default subscription (status only)
    self.connection_subscriptions[websocket] = {'status'}
    logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
```

### Subscription Management
Clients can update their subscription preferences to receive specific types of data. The system supports subscriptions for status, axis, drum, leveler, and print data.

```python
def update_subscription(self, websocket: WebSocket, data_types: List[str]) -> None:
    """
    Update subscription preferences for a specific connection.
    
    Args:
        websocket: The WebSocket connection to update
        data_types: List of data types to subscribe to
    """
    if websocket in self.connection_subscriptions:
        self.connection_subscriptions[websocket] = set(data_types)
        logger.info(f"Updated subscription for connection: {data_types}")
```

### Message Broadcasting
The broadcast method sends messages to all connected clients, with filtering based on each client's subscription preferences. It also handles client disconnections gracefully.

```python
async def broadcast(self, message: Dict[str, Any]) -> None:
    """
    Broadcast a message to all connected clients.
    
    Args:
        message: The message to broadcast
    """
    if not self.active_connections:
        return

    # Remove disconnected clients
    disconnected = []
    for connection in self.active_connections:
        try:
            # Filter message based on connection's subscriptions
            filtered_message = self._filter_message_for_connection(connection, message)
            if filtered_message:  # Only send if there's relevant data
                await connection.send_json(filtered_message)
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket: {e}")
            disconnected.append(connection)

    # Clean up disconnected clients
    for connection in disconnected:
        self.disconnect(connection)
```

### Message Filtering
Messages are filtered based on client subscriptions to ensure that clients only receive data they have requested, minimizing network overhead.

```python
def _filter_message_for_connection(self, websocket: WebSocket, message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter message content based on connection's subscriptions.
    
    Args:
        websocket: The WebSocket connection to filter for
        message: The original message
        
    Returns:
        Filtered message containing only subscribed data
    """
    if websocket not in self.connection_subscriptions:
        return message  # Send everything if no subscription info

    subscriptions = self.connection_subscriptions[websocket]
    filtered_message = {
        "type": message.get("type"),
        "timestamp": message.get("timestamp")
    }

    # Always include basic status data
    if "data" in message:
        filtered_message["data"] = message["data"]

    # Include optional data based on subscriptions
    if "axis" in subscriptions and "axis_data" in message:
        filtered_message["axis_data"] = message["axis_data"]
    if "drum" in subscriptions and "drum_data" in message:
        filtered_message["drum_data"] = message["drum_data"]
    if "leveler" in subscriptions and "leveler_data" in message:
        filtered_message["leveler_data"] = message["leveler_data"]
    if "print" in subscriptions and "print_data" in message:
        filtered_message["print_data"] = message["print_data"]

    return filtered_message
```

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-146)

## WebSocket Handler Implementation
The WebSocketHandler class was introduced to separate WebSocket endpoint logic from the main application file, improving code organization and maintainability.

### Handler Architecture
The WebSocketHandler encapsulates the WebSocket endpoint functionality, delegating connection management to the WebSocketConnectionManager. This separation of concerns allows for better testability and modularity.

```python
class WebSocketHandler:
    """Handles WebSocket connections and message processing."""
    
    def __init__(self, websocket_manager: WebSocketConnectionManager):
        self.websocket_manager = websocket_manager
```

### Endpoint Implementation
The handler provides a clean WebSocket endpoint that manages the connection lifecycle and processes incoming messages for subscription updates.

```python
async def websocket_endpoint(self, websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    await self.websocket_manager.connect(websocket)
    try:
        while True:
            message_text = await websocket.receive_text()
            await self.handle_websocket_message(websocket, message_text)
    except WebSocketDisconnect:
        self.websocket_manager.disconnect(websocket)
```

### Message Processing
The handler processes incoming WebSocket messages, particularly subscription update requests from clients.

```python
async def handle_websocket_message(self, websocket: WebSocket, message_text: str):
    """Process incoming WebSocket messages for subscription updates."""
    try:
        message = json.loads(message_text)
        if message.get("type") == "subscribe" and "data_types" in message:
            self.websocket_manager.update_subscription(websocket, message["data_types"])
            logger.info(f"Updated subscription: {message['data_types']}")
    except json.JSONDecodeError:
        logger.warning(f"Invalid JSON received from WebSocket: {message_text}")
    except Exception as e:
        logger.error(f"Error processing WebSocket message: {e}")
```

**Section sources**
- [websockets.py](file://backend/app/websockets.py#L1-34) - *New file extracted from main.py*

## Status API Endpoint
The status API endpoint provides HTTP access to system status information and integrates with the WebSocket system for consistent state representation.

### Status Retrieval
The GET /status endpoint returns the current status of the recoater system, including connection status and operational state.

```python
@router.get("/")
async def get_status(client: RecoaterClient = Depends(get_recoater_client)) -> Dict[str, Any]:
    """
    Get the current status of the recoater system.
    
    Returns:
        Dictionary containing the recoater status information
    """
    try:
        logger.info("Getting recoater status")
        status_data = client.get_state()
        
        # Add connection status to the response
        response = {
            "connected": True,
            "recoater_status": status_data,
            "backend_status": "operational"
        }
        
        logger.debug(f"Status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting status: {e}")
        # Return partial status indicating connection failure
        return {
            "connected": False,
            "recoater_status": None,
            "backend_status": "operational",
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }
```

### Health Check
The health check endpoint provides a simple way to verify the system's operational status.

```python
@router.get("/health")
async def health_check(client: RecoaterClient = Depends(get_recoater_client)) -> Dict[str, Any]:
    """
    Perform a health check of the recoater connection.
    
    Returns:
        Dictionary containing health status information
    """
    try:
        logger.info("Performing health check")
        is_healthy = client.health_check()
        
        response = {
            "backend_healthy": True,
            "recoater_healthy": is_healthy,
            "overall_healthy": is_healthy
        }
        
        logger.debug(f"Health check completed: {response}")
        return response
        
    except Exception as e:
        logger.error(f"Error during health check: {e}")
        return {
            "backend_healthy": True,
            "recoater_healthy": False,
            "overall_healthy": False,
            "error": str(e)
        }
```

### Server State Management
The endpoint also supports server state changes such as restart and shutdown.

```python
@router.post("/state")
async def set_server_state(
    action: str,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the recoater server state (restart or shutdown).

    Args:
        action: The action to perform ('restart' or 'shutdown')

    Returns:
        Dictionary containing the operation status
    """
    if action not in ["restart", "shutdown"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid action. Must be 'restart' or 'shutdown'"
        )
```

**Section sources**
- [status.py](file://backend/app/api/status.py#L1-154)

## Data Gathering and Aggregation
The DataGatherer component is responsible for collecting and aggregating status data from various sources for broadcasting to clients.

### Concurrent Data Collection
The system gathers data concurrently for efficiency, using asyncio.gather to execute multiple data collection tasks simultaneously.

```python
async def gather_all_data(self, client: Any, required_data_types: Set[str]) -> Dict[str, Any]:
    """
    Gather all required data types concurrently.
    
    Args:
        client: The recoater client instance
        required_data_types: Set of data types to gather
        
    Returns:
        Dictionary containing all gathered data
    """
    # Always get basic status
    logger.debug("Calling: get_state()")
    main_status = await asyncio.to_thread(client.get_state)

    # Conditionally gather other data types based on subscriptions
    gather_tasks = []

    if "axis" in required_data_types:
        logger.debug("Gathering AXIS data: get_axis_*() calls")
        gather_tasks.append(self.gather_axis_data(client))
    else:
        gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

    if "drum" in required_data_types:
        logger.debug("Gathering DRUM data: get_drums(), get_drum(), get_drum_motion(), get_drum_ejection(), get_drum_suction(), get_blade_screws_info(), get_blade_screws_motion() for each drum")
        gather_tasks.append(self.gather_all_drum_data(client))
    else:
        gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

    if "leveler" in required_data_types:
        logger.debug("Gathering LEVELER data: get_leveler_pressure(), get_leveler_sensor()")
        gather_tasks.append(self.gather_leveler_data(client))
    else:
        gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

    if "print" in required_data_types:
        logger.debug("Gathering PRINT data: get_layer_parameters(), get_print_job_status()")
        gather_tasks.append(self.gather_print_data(client))
    else:
        gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

    # Execute only the required data gathering tasks
    axis_data, drum_data, leveler_data, print_data = await asyncio.gather(*gather_tasks)

    return {
        "status": main_status,
        "axis_data": axis_data,
        "drum_data": drum_data,
        "leveler_data": leveler_data,
        "print_data": print_data
    }
```

### Error-Resilient Data Collection
The system uses a safe API call wrapper to handle errors during data collection, ensuring that failures in one data source don't prevent the collection of other data.

```python
async def _safe_api_call(self, method, *args) -> Optional[Any]:
    """
    Safely execute an API call with error handling.
    
    Args:
        method: The API method to call
        *args: Arguments to pass to the method
        
    Returns:
        API response or None if failed
    """
    try:
        return await asyncio.to_thread(method, *args)
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        logger.debug(f"API call failed: {e}")
        return None
```

### Message Construction
Collected data is formatted into a standardized message structure for transmission to clients.

```python
def construct_status_message(self, gathered_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Construct a status message to be sent over WebSocket.
    
    Args:
        gathered_data: Dictionary containing all gathered data
        
    Returns:
        Formatted status message
    """
    return {
        "type": "status_update",
        "data": gathered_data.get("status"),
        "axis_data": gathered_data.get("axis_data"),
        "drum_data": gathered_data.get("drum_data"),
        "leveler_data": gathered_data.get("leveler_data"),
        "print_data": gathered_data.get("print_data"),
        "timestamp": asyncio.get_event_loop().time()
    }
```

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-229)

## Frontend Status Management
The frontend implements a Pinia store to manage connection status and real-time updates from the backend via WebSocket.

### Status Store Implementation
The status store maintains the application's connection state and provides computed properties for derived status information.

```javascript
export const useStatusStore = defineStore('status', () => {
  // State
  const isConnected = ref(false)
  const recoaterStatus = ref(null)
  const axisData = ref(null)
  const drumData = ref(null)
  const levelerData = ref(null)
  const printData = ref(null)
  const lastError = ref(null)
  const lastUpdate = ref(null)
  const websocket = ref(null)
  const currentPage = ref('status')
  const subscribedDataTypes = ref(new Set(['status']))
  
  // Computed
  const isHealthy = computed(() => {
    return isConnected.value && recoaterStatus.value && !lastError.value
  })

  const connected = computed(() => isConnected.value)
  const backendHealthy = computed(() => isConnected.value && !lastError.value)
```

### WebSocket Integration
The store handles WebSocket connection lifecycle and message processing.

```javascript
function connectWebSocket() {
    if (websocket.value) {
      return // Already connected
    }
    
    const wsUrl = `ws://localhost:8000/ws`
    console.log('Connecting to WebSocket:', wsUrl)
    
    try {
      websocket.value = new WebSocket(wsUrl)
      
      websocket.value.onopen = () => {
        console.log('WebSocket connected')
        setConnectionStatus(true)
        // Send initial subscription based on current page
        updateDataSubscriptions()
      }
      
      websocket.value.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('WebSocket message received:', message)
          
          if (message.type === 'status_update') {
            updateStatus(message.data)
            if (message.axis_data) {
              updateAxisData(message.axis_data)
            }
            if (message.drum_data) {
              updateDrumData(message.drum_data)
            }
            if (message.leveler_data) {
              updateLevelerData(message.leveler_data)
            }
            if (message.print_data) {
              updatePrintData(message.print_data)
            }
          } else if (message.type === 'connection_error') {
            setError(message.error)
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
```

### Page-Aware Subscriptions
The system implements page-aware data subscriptions, adjusting the data types requested based on the current view.

```javascript
function updateDataSubscriptions() {
    const newSubscriptions = new Set(['status']) // Always include basic status

    // Add page-specific data types
    switch (currentPage.value) {
      case 'axis':
        // newSubscriptions.add('axis')
        break
      case 'recoater':
        newSubscriptions.add('drum')
        newSubscriptions.add('leveler')
        break
      case 'print':
        newSubscriptions.add('print')
        newSubscriptions.add('drum') // Print page also shows drum info
        break
      case 'config':
        // Config page might need all data for comprehensive view
        //newSubscriptions.add('axis')
        //newSubscriptions.add('drum')
        //newSubscriptions.add('leveler')
        //newSubscriptions.add('print')
        break
      case 'status':
        // Example: Add drum data to status page
        // newSubscriptions.add('drum')
        break
      // Default: only basic status
    }

    subscribedDataTypes.value = newSubscriptions

    // If WebSocket is connected, send subscription update
    if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
      websocket.value.send(JSON.stringify({
        type: 'subscribe',
        data_types: Array.from(newSubscriptions)
      }))
    }
  }
```

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L1-259)

## System Integration and Data Flow
The status monitoring subsystem integrates multiple components to provide a cohesive real-time monitoring solution.

### Application Initialization
The main application initializes the status polling service and WebSocket manager during startup.

```python
# Create global service instances
websocket_manager = WebSocketConnectionManager()
websocket_handler = WebSocketHandler(websocket_manager)
status_poller = StatusPollingService(websocket_manager)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    """
    logger.info("Starting Recoater HMI Backend...")

    initialize_recoater_client()
    initialize_multilayer_job_manager()
    await initialize_opcua_coordinator()
    await status_poller.start()
    heartbeat_task = start_heartbeat_task()
    logger.info("Heartbeat task started")
    logger.info("Backend startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Recoater HMI Backend...")
    await status_poller.stop()
    try:
        await stop_heartbeat_task()
    except Exception as e:
        logger.error(f"Error stopping heartbeat task: {e}")
    else:
        logger.info("Heartbeat task stopped")
    logger.info("Backend shutdown complete")
```

### WebSocket Endpoint Integration
The WebSocket endpoint is now handled by the WebSocketHandler class, which is registered with the FastAPI application.

```python
# FastAPI decorator that registers a WebSocket endpoint at /ws. Enables full-duplex, real-time communication over a single TCP connection
# Handles incoming connections, messages and disconnections asynchronously
# The endpoint at (ws://localhost)/ws is where the Vue app served by the Vite dev server (localhost:5173) connects to.
@app.websocket("/ws")(websocket_handler.websocket_endpoint)
```

**Section sources**
- [main.py](file://backend/app/main.py#L1-161)
- [websockets.py](file://backend/app/websockets.py#L1-34) - *New file extracted from main.py*

## Error Handling and Resilience
The system implements comprehensive error handling to ensure reliability and resilience.

### Backend Error Handling
The status polling service handles various error types, including connection errors and API errors.

```python
async def _polling_loop(self) -> None:
    """Main polling loop that runs in the background."""
    while self._running:
        try:
            await self._poll_and_broadcast()
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.error(f"Status polling connection error: {e}")
            await self._broadcast_connection_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in status polling: {e}", exc_info=True)

        await asyncio.sleep(self.poll_interval)
```

### Connection Error Broadcasting
Connection errors are broadcast to all connected clients to ensure consistent error state representation.

```python
async def _broadcast_connection_error(self, error: Exception) -> None:
    """
    Broadcast a connection error to all connected clients.
    
    Args:
        error: The error that occurred
    """
    error_message = {
        "type": "connection_error",
        "error": str(error),
        "timestamp": asyncio.get_event_loop().time()
    }
    await self.websocket_manager.broadcast(error_message)
```

### Frontend Error Management
The frontend status store manages error state and provides appropriate user feedback.

```javascript
function setError(error) {
    lastError.value = error
    lastUpdate.value = new Date()
}

const isHealthy = computed(() => {
    return isConnected.value && recoaterStatus.value && !lastError.value
})
```

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-124)
- [status.js](file://frontend/src/stores/status.js#L1-259)

## Scalability and Performance
The system is designed with scalability and performance in mind, supporting multiple concurrent WebSocket connections with efficient data handling.

### Connection Management
The WebSocket manager efficiently handles multiple concurrent connections with connection cleanup.

```python
def disconnect(self, websocket: WebSocket) -> None:
    """
    Remove a WebSocket connection.
    
    Args:
        websocket: The WebSocket connection to remove
    """
    if websocket in self.active_connections:
        self.active_connections.remove(websocket)
    # Clean up subscription tracking
    if websocket in self.connection_subscriptions:
        del self.connection_subscriptions[websocket]
    logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
```

### Network Optimization
The system minimizes network overhead through subscription-based data filtering and conditional data gathering.

```python
def get_required_data_types(self) -> Set[str]:
    """
    Get all data types that are currently subscribed to by any connection.
    
    Returns:
        Set of all subscribed data types
    """
    all_subscriptions = set()
    for subscriptions in self.connection_subscriptions.values():
        all_subscriptions.update(subscriptions)
    return all_subscriptions
```

### Message Payload Examples
The system transmits status updates with a standardized message structure.

**Status Update Message:**
```json
{
  "type": "status_update",
  "data": {
    "state": "ready",
    "timestamp": "2025-07-09T14:30:00Z"
  },
  "axis_data": null,
  "drum_data": {
    "1": {
      "info": {"id": 1, "state": "idle"},
      "motion": {"running": false},
      "ejection": {"enabled": true},
      "suction": {"pressure": 85.5},
      "blade_screws": {"position": 12.3},
      "blade_motion": {"running": false}
    }
  },
  "leveler_data": {
    "pressure": 75.2,
    "sensor": {"value": 0.85}
  },
  "print_data": {
    "layer_parameters": {"thickness": 0.1},
    "job_status": {"current_layer": 42, "total_layers": 100}
  },
  "timestamp": 1720526400.123
}
```

**Connection Error Message:**
```json
{
  "type": "connection_error",
  "error": "Failed to connect to recoater hardware",
  "timestamp": 1720526400.123
}
```

## Sequence Diagram
The following sequence diagram illustrates the flow from device polling to frontend update, including the new WebSocketHandler component.

``mermaid
sequenceDiagram
participant StatusPoller as StatusPollingService
participant DataGatherer as DataGatherer
participant OPCUAClient as OPCUA Client
participant WebSocketManager as WebSocketManager
participant WebSocketHandler as WebSocketHandler
participant Frontend as Frontend Client
StatusPoller->>StatusPoller : Start polling loop
loop Every poll_interval seconds
StatusPoller->>WebSocketManager : get_required_data_types()
WebSocketManager-->>StatusPoller : Set of subscribed data types
StatusPoller->>DataGatherer : gather_all_data(client, required_types)
DataGatherer->>OPCUAClient : get_state()
DataGatherer->>OPCUAClient : get_drums()
DataGatherer->>OPCUAClient : get_drum_motion()
DataGatherer->>OPCUAClient : get_leveler_pressure()
DataGatherer->>OPCUAClient : get_print_job_status()
DataGatherer-->>StatusPoller : Gathered data
StatusPoller->>DataGatherer : construct_status_message(data)
DataGatherer-->>StatusPoller : Status message
StatusPoller->>WebSocketManager : broadcast(message)
WebSocketManager->>WebSocketManager : _filter_message_for_connection()
WebSocketManager->>WebSocketHandler : send_json(filtered_message)
WebSocketHandler->>Frontend : Send message to client
Frontend->>Frontend : Process status update
end
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [websockets.py](file://backend/app/websockets.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)