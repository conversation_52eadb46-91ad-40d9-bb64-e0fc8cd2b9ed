# FastAPI Application Structure

<cite>
**Referenced Files in This Document**   
- [main.py](file://backend/app/main.py) - *Updated with environment variable configuration and WebSocket handler extraction*
- [dependencies.py](file://backend/app/dependencies.py)
- [status.py](file://backend/app/api/status.py)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [websockets.py](file://backend/app/websockets.py) - *New WebSocket handler module extracted from main.py*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect environment variable configuration changes in main.py
- Added details about .env file usage and configuration settings
- Enhanced middleware configuration section with environment variable details
- Updated server startup section with environment-based host and port configuration
- Added logging configuration details from environment variables
- Documented the extraction of WebSocket functionality to websockets.py
- Updated WebSocket endpoint section to reflect modular implementation
- Added new section for WebSocket handler module
- Maintained all existing architectural documentation while incorporating new configuration details

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive architectural documentation for the FastAPI application in the APIRecoater_Ethernet repository. The application serves as a backend for a recoater hardware interface, acting as a proxy between the frontend HMI (Human-Machine Interface) and industrial hardware systems. It implements REST APIs, WebSocket communication, and OPC UA protocol integration for industrial automation coordination. The architecture emphasizes separation of concerns, dependency injection, and asynchronous processing to handle real-time hardware communication reliably. Recent updates have enhanced the configuration system with environment variable support through a .env file, improving deployment flexibility. Additionally, WebSocket functionality has been modularized into a separate websockets.py module to improve code organization and maintainability.

## Project Structure
The project follows a modular, layered architecture with clear separation between frontend and backend components. The backend is organized into distinct directories for APIs, configuration, models, services, and utilities, promoting maintainability and testability.

```
mermaid
graph TD
backend[backend/] --> app[app/]
backend --> services[services/]
backend --> tests[tests/]
app --> api[api/]
app --> config[config/]
app --> models[models/]
app --> services[services/]
app --> utils[utils/]
app --> dependencies[dependencies.py]
app --> main[main.py]
app --> websockets[websockets.py]
api --> axis[axis.py]
api --> configuration[configuration.py]
api --> print[print.py]
api --> recoater_controls[recoater_controls.py]
api --> status[status.py]
config --> opcua_config[opcua_config.py]
services --> coordination_engine[coordination_engine.py]
services --> data_gatherer[data_gatherer.py]
services --> multilayer_job_manager[multilayer_job_manager.py]
services --> opcua_coordinator[opcua_coordinator.py]
services --> opcua_server[opcua_server.py]
services --> status_poller[status_poller.py]
services --> websocket_manager[websocket_manager.py]
utils --> heartbeat[heartbeat.py]
frontend[frontend/] --> src[src/]
frontend --> tests[tests/]
src --> components[components/]
src --> router[router/]
src --> services[services/]
src --> stores[stores/]
src --> views[views/]
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [websockets.py](file://backend/app/websockets.py)

**Section sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)

## Core Components
The application's core components include the FastAPI entry point (main.py), a dependency injection system (dependencies.py), modular API routers, and specialized services for hardware coordination. The main.py file initializes the FastAPI application, configures middleware, and mounts API routers. The dependencies.py module provides a centralized dependency injection system for shared components like the OPC UA coordinator and recoater client. API endpoints are organized into separate router modules (status, axis, recoater_controls, print, configuration) that handle specific functional domains. The application uses asynchronous programming throughout to handle concurrent hardware communication efficiently. The recent addition of environment variable configuration through a .env file enhances deployment flexibility and configuration management. Additionally, WebSocket handling has been extracted to a dedicated websockets.py module to improve separation of concerns and code maintainability.

**Section sources**
- [main.py](file://backend/app/main.py#L0-L161)
- [dependencies.py](file://backend/app/dependencies.py#L0-L170)
- [websockets.py](file://backend/app/websockets.py#L0-L100)

## Architecture Overview
The application follows a layered architecture with clear separation between presentation, business logic, and hardware integration layers. The FastAPI framework handles HTTP requests and WebSocket connections, routing them to appropriate API endpoints. These endpoints use dependency injection to access shared services and clients. The business logic layer coordinates between the frontend requests and hardware systems through specialized services. The OPC UA coordination layer enables real-time communication with PLC systems using the OPC UA protocol.

```
mermaid
graph TB
subgraph "Frontend"
UI[Vue.js UI]
WS[WebSocket]
end
subgraph "Backend"
API[FastAPI Server]
Routers[API Routers]
DI[Dependency Injection]
Services[Services]
Hardware[Hardware Clients]
end
subgraph "External Systems"
PLC[TwinCAT PLC]
Recoater[Recoater Hardware]
end
UI --> API
WS --> API
API --> Routers
Routers --> DI
DI --> Services
Services --> Hardware
Hardware --> Recoater
Services --> OPCUA[OPC UA Server]
OPCUA < --> PLC
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

## Detailed Component Analysis

### Main Application Analysis
The main.py file serves as the entry point for the FastAPI application, handling initialization, configuration, and startup/shutdown lifecycle management.

#### Application Initialization
The application is initialized with metadata and a custom lifespan function that manages startup and shutdown processes:

```python
app = FastAPI(
    title="Recoater HMI Backend",
    description="Backend API for the Aerosint SPD Recoater Custom HMI",
    version="1.0.0",
    lifespan=lifespan
)
```

**Section sources**
- [main.py](file://backend/app/main.py#L85-L95)

#### Lifespan Management
The lifespan context manager handles application startup and shutdown, initializing critical services and cleaning up resources:

```
mermaid
graph TD
Start[Application Start] --> InitClients[Initialize Clients]
InitClients --> InitJobManager[Initialize Job Manager]
InitClients --> InitOPCUA[Initialize OPC UA Coordinator]
InitJobManager --> StartPoller[Start Status Poller]
StartPoller --> StartHeartbeat[Start Heartbeat Task]
StartHeartbeat --> Ready[Application Ready]
Ready --> Running[Handle Requests]
Running --> Shutdown[Application Shutdown]
Shutdown --> StopPoller[Stop Status Poller]
StopPoller --> StopHeartbeat[Stop Heartbeat Task]
StopHeartbeat --> Cleanup[Cleanup Complete]
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L54-L89)

**Section sources**
- [main.py](file://backend/app/main.py#L54-L89)

#### Middleware Configuration
The application configures CORS middleware to allow cross-origin requests from the frontend development server, with configuration loaded from environment variables:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:5173").split(","),  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**Section sources**
- [main.py](file://backend/app/main.py#L100-L108)

#### Router Mounting
The application mounts multiple API routers with a common prefix for versioning:

```python
app.include_router(status_router, prefix="/api/v1")
app.include_router(axis_router, prefix="/api/v1")
app.include_router(recoater_router, prefix="/api/v1")
app.include_router(print_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")
```

**Section sources**
- [main.py](file://backend/app/main.py#L111-L115)

#### WebSocket Endpoint
The application now imports WebSocket functionality from a dedicated module, maintaining clean separation of concerns:

```python
from app.websockets import websocket_endpoint
app.websocket("/ws")(websocket_endpoint)
```

**Section sources**
- [main.py](file://backend/app/main.py#L116-L118)
- [websockets.py](file://backend/app/websockets.py#L1-L20)

### WebSocket Handler Module
The websockets.py module now contains the WebSocket endpoint implementation, extracted from main.py for better code organization:

```python
from fastapi import WebSocket
from app.services.websocket_manager import WebSocketConnectionManager

websocket_manager = WebSocketConnectionManager()

async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    await websocket_manager.connect(websocket)
    try:
        while True:
            message_text = await websocket.receive_text()
            try:
                message = json.loads(message_text)
                if message.get("type") == "subscribe" and "data_types" in message:
                    websocket_manager.update_subscription(websocket, message["data_types"])
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {message_text}")
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
```

**Section sources**
- [websockets.py](file://backend/app/websockets.py#L1-L50)

#### Environment Configuration
The application now uses environment variables for configuration, loaded from a .env file:

```python
# Load environment variables from .env file
load_dotenv()

# Configure logging from environment variable
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Server configuration from environment variables
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=os.getenv("UVICORN_HOST", "0.0.0.0"),
        port=os.getenv("UVICORN_PORT", 8000),
        reload=True,
        log_level="info"
    )
```

**Section sources**
- [main.py](file://backend/app/main.py#L25-L56)

### Dependency Injection System Analysis
The dependencies.py module implements a dependency injection pattern for shared application components, avoiding circular imports and providing centralized access to critical services.

#### Global Instance Management
The module maintains global instances of key services using module-level variables:

```python
# Global recoater client instance
_recoater_client: Optional[RecoaterClient] = None

# Global OPC UA coordinator instance
_opcua_coordinator = None

# Global multilayer job manager instance
_multilayer_job_manager = None
```

**Section sources**
- [dependencies.py](file://backend/app/dependencies.py#L20-L28)

#### Initialization Functions
The module provides initialization functions that are called during application startup:

```python
def initialize_recoater_client() -> None:
    """Initialize the global recoater client instance."""
    global _recoater_client
    base_url = os.getenv("RECOATER_API_BASE_URL", "http://172.16.17.224:8080")
    development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
    
    if development_mode:
        logger.info("Initializing mock recoater client for development mode")
        _recoater_client = MockRecoaterClient(base_url)
    else:
        logger.info("Initializing real recoater client for production mode")
        _recoater_client = RecoaterClient(base_url)
```

**Section sources**
- [dependencies.py](file://backend/app/dependencies.py#L30-L48)

#### Dependency Functions
The module exposes dependency functions that can be used with FastAPI's Depends() for injection into route handlers:

```python
def get_recoater_client() -> RecoaterClient:
    """
    Dependency function to get the recoater client instance.
    This function can be used with FastAPI's Depends() to inject
    the recoater client into API endpoints.
    """
    if _recoater_client is None:
        logger.error("Recoater client not initialized")
        raise HTTPException(
            status_code=503,
            detail="Recoater client not initialized"
        )
    return _recoater_client
```

**Section sources**
- [dependencies.py](file://backend/app/dependencies.py#L85-L98)

### API Router Analysis
The API routers are organized by functional domain, with each router handling a specific aspect of the recoater system.

#### Status API Router
The status.py router provides endpoints for system status and health checks:

```python
@router.get("/")
async def get_status(client: RecoaterClient = Depends(get_recoater_client)) -> Dict[str, Any]:
    """
    Get the current status of the recoater system.
    Returns dictionary containing the recoater status information
    """
    try:
        status_data = client.get_state()
        response = {
            "connected": True,
            "recoater_status": status_data,
            "backend_status": "operational"
        }
        return response
    except RecoaterConnectionError as e:
        return {
            "connected": False,
            "recoater_status": None,
            "backend_status": "operational",
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }
```

**Section sources**
- [status.py](file://backend/app/api/status.py#L25-L75)

#### Recoater Controls API Router
The recoater_controls.py router handles commands for drum motion, pressure control, and other recoater operations:

```python
class DrumMotionRequest(BaseModel):
    """Request model for drum motion commands."""
    mode: Literal["absolute", "relative", "turns", "speed", "homing"] = Field(..., description="Motion mode")
    speed: float = Field(..., gt=0, description="Movement speed in mm/s")
    distance: Optional[float] = Field(None, description="Distance to move in mm")
    turns: Optional[float] = Field(None, description="Number of turns")

@router.post("/drums/{drum_id}/motion")
async def set_drum_motion(
    motion_request: DrumMotionRequest,
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """Create a motion command for a drum."""
    try:
        response_data = client.set_drum_motion(
            drum_id=drum_id,
            mode=motion_request.mode,
            speed=motion_request.speed,
            distance=motion_request.distance,
            turns=motion_request.turns
        )
        response = {
            "drum_id": drum_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }
        return response
    except RecoaterConnectionError as e:
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
```

**Section sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L50-L100)

### OPC UA Coordination Analysis
The OPC UA coordination system enables real-time communication between the backend and PLC systems for multi-material print job coordination.

#### OPC UA Server Configuration
The opcua_config.py file defines the server configuration and coordination variables:

```python
@dataclass
class OPCUAServerConfig:
    """Configuration for OPC UA server hosting."""
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    security_policy: str = "None"
    security_mode: str = "None"
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3

@dataclass
class CoordinationVariable:
    """Definition for a coordination variable hosted by OPC UA server."""
    name: str
    node_id: str
    data_type: str
    initial_value: Any
    writable: bool = True
    description: str = ""

COORDINATION_VARIABLES: List[CoordinationVariable] = [
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    CoordinationVariable(
        name="current_layer",
        node_id="ns=2;s=current_layer",
        data_type="Int32",
        initial_value=0,
        description="Backend manages, PLC reads"
    ),
]
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L112-L200)

#### OPC UA Server Manager
The OPCUAServerManager class manages the OPC UA server lifecycle and variable access:

```
mermaid
classDiagram
class OPCUAServerManager {
+config : OPCUAServerConfig
+server : Optional[Server]
+namespace_idx : int
+variable_nodes : Dict[str, Any]
+_running : bool
+_restart_count : int
+_heartbeat_task : Optional[Task]
+async start_server() bool
+async stop_server() None
+async write_variable(name, val) None
+async read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
+_create_coordination_variables() None
+_get_ua_data_type(str) ua.Type
+_heartbeat_loop() None
+_cleanup() None
+_handle_server_error(Exception) None
}
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L189-L525)

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L189-L525)

#### OPC UA Coordinator
The OPCUACoordinator class provides a high-level interface for job coordination with the PLC:

```
mermaid
classDiagram
class OPCUACoordinator {
+config : OPCUAServerConfig
+server_manager : OPCUAServerManager
+_connected : bool
+_monitoring_task : Optional[Task]
+_event_handlers : Dict[str, List]
+async connect() bool
+async disconnect() bool
+is_connected() bool
+get_server_status() Dict
+async write_variable(name, val) None
+async read_variable(name) Any
+async subscribe_to_changes(...) None
+async set_job_active(id, layers) None
+async set_job_inactive() None
+async update_layer_progress(num) None
+async set_drums_ready(states) None
+async _reset_drum_status() None
+async set_error_state(...) None
+async clear_error_flags() None
+async _reset_process_signals() None
+async _monitoring_loop() None
+async _trigger_event_handlers() None
}
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L590)

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L590)

## Dependency Analysis
The application's dependency structure follows a clear hierarchy with the main application depending on routers and services, which in turn depend on shared components through dependency injection.

```
mermaid
graph TD
main[main.py] --> dependencies[dependencies.py]
main --> status_router[status.py]
main --> axis_router[axis.py]
main --> recoater_router[recoater_controls.py]
main --> print_router[print.py]
main --> config_router[configuration.py]
main --> websockets[websockets.py]
main --> status_poller[status_poller.py]
dependencies --> recoater_client[recoater_client.py]
dependencies --> mock_recoater_client[mock_recoater_client.py]
dependencies --> opcua_coordinator[opcua_coordinator.py]
status_router --> dependencies
recoater_router --> dependencies
print_router --> dependencies
config_router --> dependencies
opcua_coordinator --> opcua_server[opcua_server.py]
opcua_server --> opcua_config[opcua_config.py]
status_poller --> websocket_manager[websocket_manager.py]
websockets --> websocket_manager[websocket_manager.py]
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [websockets.py](file://backend/app/websockets.py)

**Section sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)

## Performance Considerations
The application is designed with performance and reliability in mind for industrial automation scenarios:

1. **Asynchronous Processing**: The entire application uses async/await pattern to handle concurrent requests without blocking, essential for real-time hardware communication.

2. **Connection Pooling**: The OPC UA server maintains persistent connections with the PLC, reducing connection overhead.

3. **Heartbeat Mechanism**: A heartbeat task prevents connection timeouts with hardware systems.

4. **Status Polling**: A dedicated status polling service periodically updates system status without requiring client requests.

5. **WebSocket Efficiency**: Real-time updates are pushed to clients via WebSockets, eliminating the need for polling.

6. **Error Resilience**: The OPC UA server includes auto-restart capabilities with configurable retry parameters.

7. **Resource Management**: The lifespan context manager ensures proper cleanup of resources during shutdown.

8. **Environment Configuration**: Using environment variables allows for flexible deployment configurations without code changes.

9. **Modular Design**: Extracting WebSocket handling to a separate module improves code organization and maintainability.

**Section sources**
- [main.py](file://backend/app/main.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [websockets.py](file://backend/app/websockets.py)

## Troubleshooting Guide
This section provides guidance for common issues and debugging approaches:

### Connection Issues
- **Recoater Connection**: Check if the RECOATER_API_BASE_URL environment variable points to the correct IP address and port.
- **OPC UA Connection**: Verify that the OPCUA_SERVER_ENDPOINT matches the expected address and that the PLC can reach the backend server.
- **Development Mode**: Set DEVELOPMENT_MODE=true to use the mock client when the physical hardware is unavailable.

### OPC UA Configuration
- **Namespace Issues**: Ensure namespace_idx matches between server and client configurations.
- **Security Settings**: For internal networks, use security_policy="None" and security_mode="None".
- **Variable Access**: Verify that variable node IDs follow the ns=X;s=variable_name format.

### Error Handling
The application implements comprehensive error handling with appropriate HTTP status codes:
- 503 Service Unavailable: When dependencies like the recoater client are not initialized
- 502 Bad Gateway: When communication with the recoater hardware fails
- 400 Bad Request: When API requests contain invalid parameters
- 500 Internal Server Error: For unexpected exceptions

### Debugging Tips
- Enable DEBUG logging level to see detailed operation logs
- Use the /ws WebSocket endpoint to monitor real-time status updates
- Check the health check endpoint (/api/v1/status/health) to verify system connectivity
- Review the OPC UA server logs for connection and variable access issues
- Verify environment variables are properly set in the .env file

**Section sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [status.py](file://backend/app/api/status.py)

## Conclusion
The FastAPI application in APIRecoater_Ethernet demonstrates a well-architected approach to industrial automation software. It effectively separates concerns through modular API routers, implements dependency injection for shared components, and uses asynchronous programming for efficient hardware communication. The OPC UA coordination system provides a robust mechanism for real-time communication with PLC systems, enabling complex multi-material print job coordination. The architecture prioritizes reliability, maintainability, and testability, with clear separation between business logic and hardware integration. This design allows for flexible development and testing, including mock implementations for offline development. The recent addition of environment variable configuration through a .env file enhances deployment flexibility and configuration management. The extraction of WebSocket functionality to a dedicated websockets.py module further improves code organization and maintainability. The application serves as a solid foundation for industrial HMI systems requiring real-time data exchange and control capabilities.