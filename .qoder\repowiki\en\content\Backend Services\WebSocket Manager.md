# WebSocket Manager

<cite>
**Referenced Files in This Document**   
- [main.py](file://backend/app/main.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [status.py](file://backend/app/api/status.py)
- [status.js](file://frontend/src/stores/status.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The WebSocket Manager service in APIRecoater_Ethernet enables real-time bidirectional communication between the backend and frontend. It plays a critical role in pushing status updates, job progress notifications, and error alerts to connected clients. This document provides a comprehensive analysis of the WebSocketManager implementation, its integration with FastAPI, and its interaction with other core services such as StatusPoller and MultilayerJobManager. The service implements an event-driven architecture with efficient connection lifecycle management and JSON-based message serialization. The documentation covers code examples, integration patterns, scalability considerations, and best practices for frontend implementation.

## Project Structure
The project follows a layered architecture with clear separation between frontend and backend components. The backend is implemented using FastAPI and organized into modules for API endpoints, services, models, and configuration. The WebSocketManager is located in the services layer and integrates with the main application through dependency injection.

```mermaid
graph TB
subgraph "Frontend"
UI[Vue.js Application]
Store[Pinia Store]
WebSocketClient[WebSocket Client]
end
subgraph "Backend"
FastAPI[FastAPI Application]
WebSocketManager[WebSocketConnectionManager]
StatusPoller[StatusPollingService]
DataGatherer[RecoaterDataGatherer]
JobManager[MultiMaterialJobManager]
end
UI --> WebSocketClient
WebSocketClient --> FastAPI
FastAPI --> WebSocketManager
WebSocketManager --> StatusPoller
StatusPoller --> DataGatherer
WebSocketManager --> JobManager
style WebSocketManager fill:#f9f,stroke:#333
style StatusPoller fill:#f9f,stroke:#333
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L1-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

**Section sources**
- [main.py](file://backend/app/main.py#L1-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

## Core Components
The WebSocketManager service consists of several core components that work together to enable real-time communication. The WebSocketConnectionManager handles connection lifecycle and message broadcasting, while the StatusPollingService periodically polls the recoater hardware and pushes updates through the WebSocketManager. The DataGatherer service collects status information from various hardware components and formats it for transmission.

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

## Architecture Overview
The WebSocket architecture follows an event-driven pattern where status updates are pushed from the backend to all connected frontend clients. The system uses a publish-subscribe model with subscription-based filtering to optimize network traffic. The main application initializes the WebSocketManager and StatusPoller services during startup, establishing a continuous flow of status updates.

```mermaid
sequenceDiagram
participant Frontend as "Frontend (Vue.js)"
participant Backend as "FastAPI Backend"
participant Manager as "WebSocketManager"
participant Poller as "StatusPoller"
participant Hardware as "Recoater Hardware"
Frontend->>Backend : Connect to /ws endpoint
Backend->>Manager : websocket_endpoint()
Manager->>Manager : connect(websocket)
Manager-->>Frontend : Connection established
loop Polling Interval
Poller->>Poller : _polling_loop()
Poller->>Poller : _poll_and_broadcast()
Poller->>Hardware : get_state(), get_drum(), etc.
Hardware-->>Poller : Status data
Poller->>Poller : construct_status_message()
Poller->>Manager : broadcast(message)
Manager->>Manager : _filter_message_for_connection()
Manager->>Frontend : send_json(filtered_message)
end
Frontend->>Backend : Send subscription update
Backend->>Manager : update_subscription()
Manager-->>Poller : get_required_data_types()
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L1-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

## Detailed Component Analysis

### WebSocketConnectionManager Analysis
The WebSocketConnectionManager class is responsible for managing WebSocket connections, handling subscriptions, and broadcasting messages to connected clients. It maintains a list of active connections and tracks subscription preferences for each client.

```mermaid
classDiagram
class WebSocketConnectionManager {
+active_connections : List[WebSocket]
+connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket : WebSocket) void
+disconnect(websocket : WebSocket) void
+update_subscription(websocket : WebSocket, data_types : List[str]) void
+broadcast(message : Dict[str, Any]) void
+_filter_message_for_connection(websocket : WebSocket, message : Dict[str, Any]) Dict[str, Any]
+get_required_data_types() Set[str]
+connection_count : int
+has_connections : bool
}
WebSocketConnectionManager --> WebSocket : "manages"
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

### StatusPollingService Analysis
The StatusPollingService works in conjunction with the WebSocketManager to provide periodic status updates from the recoater hardware. It runs a background task that polls the hardware at a configurable interval and broadcasts status updates to all connected clients.

```mermaid
classDiagram
class StatusPollingService {
+websocket_manager : WebSocketConnectionManager
+data_gatherer : RecoaterDataGatherer
+poll_interval : float
+polling_task : Optional[asyncio.Task]
+_running : bool
+start() void
+stop() void
+_polling_loop() void
+_poll_and_broadcast() void
+_broadcast_connection_error(error : Exception) void
+is_running : bool
+update_poll_interval(interval : float) void
}
StatusPollingService --> WebSocketConnectionManager : "uses"
StatusPollingService --> RecoaterDataGatherer : "uses"
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

### Data Flow Analysis
The data flow for status updates follows a well-defined pattern from hardware polling to frontend display. The system efficiently gathers only the data types that are currently subscribed to by active clients, minimizing unnecessary hardware queries.

```mermaid
flowchart TD
A["Start: _polling_loop()"] --> B{"Is _running?"}
B --> |Yes| C["_poll_and_broadcast()"]
B --> |No| D["End"]
C --> E["get_recoater_client_instance()"]
E --> F{"Client available and connections exist?"}
F --> |No| D
F --> |Yes| G["get_required_data_types()"]
G --> H["gather_all_data(client, required_data_types)"]
H --> I["construct_status_message(gathered_data)"]
I --> J["websocket_manager.broadcast(message)"]
J --> K["Handle exceptions"]
K --> L["Wait poll_interval"]
L --> A
M["Exception"] --> N["_broadcast_connection_error()"]
N --> L
style A fill:#f9f,stroke:#333
style D fill:#f9f,stroke:#333
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L70-L110)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

### Frontend Integration Analysis
The frontend implementation uses a Pinia store to manage WebSocket connections and process incoming messages. It implements automatic reconnection logic and page-aware subscription management to optimize data flow based on the current view.

```mermaid
sequenceDiagram
participant Vue as "Vue Component"
participant Store as "Pinia Store"
participant WebSocket as "WebSocket"
participant Backend as "Backend"
Vue->>Store : setCurrentPage('recoater')
Store->>Store : updateDataSubscriptions()
Store->>WebSocket : send subscription message
WebSocket->>Backend : {"type" : "subscribe", "data_types" : ["status", "drum", "leveler"]}
Backend->>WebSocket : status_update message
WebSocket->>Store : onmessage(event)
Store->>Store : parse JSON message
Store->>Store : updateDrumData(), updateLevelerData()
Store-->>Vue : Update reactive state
Vue-->>User : Render updated UI
WebSocket->>WebSocket : onclose()
WebSocket->>Store : Reconnect after 3 seconds
Store->>WebSocket : connectWebSocket()
```

**Diagram sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)

## Dependency Analysis
The WebSocketManager service has well-defined dependencies on other components in the system. It depends on FastAPI for WebSocket handling, and other services depend on it for real-time communication capabilities.

```mermaid
graph TD
backend.app.main[main.py] --> backend.app.services.websocket_manager[websocket_manager.py]
backend.app.main[main.py] --> backend.app.services.status_poller[status_poller.py]
backend.app.services.status_poller[status_poller.py] --> backend.app.services.websocket_manager[websocket_manager.py]
backend.app.services.status_poller[status_poller.py] --> backend.app.services.data_gatherer[data_gatherer.py]
backend.app.api.status[status.py] --> backend.app.services.websocket_manager[websocket_manager.py]
frontend.src.stores.status[status.js] --> backend.app.main[main.py]
style backend.app.services.websocket_manager[websocket_manager.py] fill:#f9f,stroke:#333
style backend.app.services.status_poller[status_poller.py] fill:#f9f,stroke:#333
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L1-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status.py](file://backend/app/api/status.py#L1-L155)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

**Section sources**
- [main.py](file://backend/app/main.py#L1-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status.py](file://backend/app/api/status.py#L1-L155)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

## Performance Considerations
The WebSocketManager implementation includes several performance optimizations to handle multiple concurrent clients and high-frequency updates efficiently. The service uses subscription-based filtering to minimize network traffic by only sending data that clients have subscribed to. The StatusPoller service optimizes hardware queries by gathering only the data types that are currently required by active clients.

The message broadcasting mechanism includes error handling for disconnected clients, automatically cleaning up connections that fail to receive messages. This prevents memory leaks and ensures that the active connections list remains accurate. The system also implements a polling interval that can be configured via environment variables, allowing adjustment based on network conditions and hardware capabilities.

For scalability, the architecture supports multiple concurrent clients without significant performance degradation. The connection management is designed to handle connection and disconnection events efficiently, with O(1) operations for most common operations. The use of asynchronous programming with asyncio ensures that I/O operations do not block the event loop, maintaining responsiveness even under heavy load.

## Troubleshooting Guide
When troubleshooting WebSocket connectivity issues, several common problems and their solutions should be considered:

**Connection Issues:**
- Verify that the backend server is running and accessible at the expected address (localhost:8000)
- Check CORS configuration in main.py to ensure the frontend origin is allowed
- Confirm that the WebSocket endpoint (/ws) is properly configured

**Message Flow Problems:**
- Enable debug logging to trace message broadcasting and filtering
- Verify subscription updates are being sent from the frontend
- Check that the StatusPoller service is running and polling at the expected interval

**Error Handling:**
The system implements comprehensive error handling for various failure scenarios:
- Hardware connection errors are broadcast to all clients as "connection_error" messages
- Failed message delivery to individual clients results in automatic disconnection
- Unhandled exceptions in the polling loop are logged with full stack traces

To debug message flow, browser developer tools can be used to monitor WebSocket traffic. The console will display connection events, incoming messages, and any errors. The Pinia store in the frontend also logs subscription updates and message processing.

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

## Conclusion
The WebSocketManager service in APIRecoater_Ethernet provides a robust foundation for real-time communication between the backend and frontend. Its event-driven architecture enables efficient push-based updates of status information, job progress, and error alerts. The service integrates seamlessly with FastAPI's WebSocket endpoints and works in conjunction with the StatusPoller to provide periodic hardware status updates.

Key strengths of the implementation include subscription-based filtering to optimize network traffic, automatic client cleanup for disconnected clients, and comprehensive error handling. The frontend integration through the Pinia store provides a clean abstraction for managing WebSocket connections and processing incoming messages.

For future improvements, consider implementing message queuing for high-frequency updates to prevent overwhelming clients, adding authentication to WebSocket connections, and providing more granular subscription options. The current implementation provides a solid foundation that can be extended to support additional real-time features as needed.