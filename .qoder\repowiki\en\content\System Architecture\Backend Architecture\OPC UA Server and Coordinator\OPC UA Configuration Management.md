# OPC UA Configuration Management

<cite>
**Referenced Files in This Document**   
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L590)
- [config.py](file://config.py#L1-L18)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Configuration Architecture](#configuration-architecture)
3. [Server Configuration Parameters](#server-configuration-parameters)
4. [Coordination Variables](#coordination-variables)
5. [Environment Variable Overrides](#environment-variable-overrides)
6. [Configuration Integration](#configuration-integration)
7. [Environment-Specific Patterns](#environment-specific-patterns)
8. [Best Practices](#best-practices)
9. [Validation and Error Handling](#validation-and-error-handling)

## Introduction
The OPC UA configuration system provides a centralized mechanism for managing communication between the backend application and PLC (Programmable Logic Controller) in a multi-material 3D printing system. The configuration is primarily defined in `opcua_config.py`, which establishes the parameters for the OPC UA server that facilitates real-time coordination between the FastAPI backend and TwinCAT PLC. This document details all configurable parameters, their default values, override mechanisms, and integration points with `opcua_server.py` and `opcua_coordinator.py`. The system uses a layered architecture where configuration settings control server behavior, variable definitions, security, and error handling, enabling reliable communication for print job coordination.

## Configuration Architecture

```mermaid
graph TD
subgraph "Configuration Layer"
opcua_config[opcua_config.py]
env_vars[Environment Variables]
end
subgraph "Server Layer"
opcua_server[opcua_server.py]
server_instance[OPCUAServerManager]
end
subgraph "Coordinator Layer"
opcua_coordinator[opcua_coordinator.py]
coordinator_instance[OPCUACoordinator]
end
subgraph "Client Layer"
plc[TwinCAT PLC]
end
env_vars --> |Overrides| opcua_config
opcua_config --> |Provides config| opcua_server
opcua_config --> |Provides config| opcua_coordinator
opcua_server --> |Hosts variables| plc
opcua_coordinator --> |Manages| opcua_server
coordinator_instance --> |Uses| server_instance
style opcua_config fill:#f9f,stroke:#333
style opcua_server fill:#bbf,stroke:#333
style opcua_coordinator fill:#bbf,stroke:#333
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L590)

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L590)

## Server Configuration Parameters

The `OPCUAServerConfig` dataclass in `opcua_config.py` defines all server-level parameters with sensible defaults for development and production use.

### Server Endpoint Configuration
The server endpoint determines how clients connect to the OPC UA server.

**Configuration Parameters:**
- **endpoint**: Server endpoint URL in `opc.tcp://host:port/path` format
  - Default: `opc.tcp://0.0.0.0:4843/recoater/server/`
  - Binds to all interfaces on port 4843
- **server_name**: Human-readable server identifier
  - Default: `Recoater Multi-Material Coordination Server`

```python
@dataclass
class OPCUAServerConfig:
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
```

### Namespace Configuration
Namespaces provide a way to organize OPC UA variables and prevent naming conflicts.

**Configuration Parameters:**
- **namespace_uri**: Unique identifier for the variable namespace
  - Default: `http://recoater.backend.server`
  - Follows URI format for global uniqueness
- **namespace_idx**: Numeric index assigned to the namespace
  - Default: `2`
  - Index 0 and 1 are reserved for OPC UA standard namespaces

```python
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
```

### Security Settings
Security configuration controls authentication and encryption for OPC UA connections.

**Configuration Parameters:**
- **security_policy**: Security policy for the connection
  - Default: `None`
  - Options: `None`, `Basic256Sha256`, etc.
- **security_mode**: Security mode for data transmission
  - Default: `None`
  - Options: `None`, `Sign`, `SignAndEncrypt`
- **certificate_path**: Path to the server's X.509 certificate file
  - Default: Empty string (no certificate)
- **private_key_path**: Path to the server's private key file
  - Default: Empty string (no private key)

```python
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
```

### Connection Settings
Connection parameters control client connection behavior and timeouts.

**Configuration Parameters:**
- **connection_timeout**: Client connection timeout in seconds
  - Default: `5.0` seconds
  - Time to establish a connection before timing out
- **session_timeout**: OPC UA session timeout in seconds
  - Default: `60.0` seconds
  - Duration of inactivity before session termination

```python
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
```

### Server Management
Server management parameters control automatic recovery and restart behavior.

**Configuration Parameters:**
- **auto_restart**: Enable automatic server restart on failure
  - Default: `True`
  - Allows self-healing in production environments
- **restart_delay**: Delay between restart attempts in seconds
  - Default: `5.0` seconds
  - Prevents rapid restart loops
- **max_restart_attempts**: Maximum number of restart attempts
  - Default: `3`
  - Limits retry attempts to prevent infinite loops

```python
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L112-L136)

## Coordination Variables

The `COORDINATION_VARIABLES` list defines the shared data points between the backend and PLC, serving as the primary communication mechanism.

### Job Control Variables
Variables that manage the overall print job state.

**Variable Definitions:**
- **job_active**: Indicates whether a print job is currently active
  - Node ID: `ns=2;s=job_active`
  - Data Type: `Boolean`
  - Initial Value: `False`
  - Writable: Backend only
  - Description: "Backend sets TRUE at start, FALSE at end"
- **total_layers**: Total number of layers in the current job
  - Node ID: `ns=2;s=total_layers`
  - Data Type: `Int32`
  - Initial Value: `0`
  - Writable: Backend only
  - Description: "Backend sets once at job start"
- **current_layer**: Current layer being processed
  - Node ID: `ns=2;s=current_layer`
  - Data Type: `Int32`
  - Initial Value: `0`
  - Writable: Backend only
  - Description: "Backend manages, PLC reads"

### Recoater Coordination Variables
Variables that coordinate the recoater mechanism in multi-material printing.

**Variable Definitions:**
- **recoater_ready_to_print**: Indicates when the Aerosint system is ready for printing
  - Node ID: `ns=2;s=recoater_ready_to_print`
  - Data Type: `Boolean`
  - Initial Value: `False`
  - Writable: Backend only
  - Description: "Backend writes when Aerosint is ready"
- **recoater_layer_complete**: Indicates when layer deposition is complete
  - Node ID: `ns=2;s=recoater_layer_complete`
  - Data Type: `Boolean`
  - Initial Value: `False`
  - Writable: Backend only
  - Description: "Backend writes when deposition complete"

### System Status Variables
Variables that handle error reporting and system status.

**Variable Definitions:**
- **backend_error**: Error flag set by the backend
  - Node ID: `ns=2;s=backend_error`
  - Data Type: `Boolean`
  - Initial Value: `False`
  - Writable: Backend only
  - Description: "Backend writes if any issue arises"
- **plc_error**: Error flag set by the PLC
  - Node ID: `ns=2;s=plc_error`
  - Data Type: `Boolean`
  - Initial Value: `False`
  - Writable: Bidirectional
  - Description: "PLC writes if any issues"

```python
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    # Job Control
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    CoordinationVariable(
        name="current_layer",
        node_id="ns=2;s=current_layer",
        data_type="Int32",
        initial_value=0,
        description="Backend manages, PLC reads"
    ),
    # Recoater Coordination
    CoordinationVariable(
        name="recoater_ready_to_print",
        node_id="ns=2;s=recoater_ready_to_print",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when Aerosint is ready"
    ),
    CoordinationVariable(
        name="recoater_layer_complete",
        node_id="ns=2;s=recoater_layer_complete",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when deposition complete"
    ),
    # System Status
    CoordinationVariable(
        name="backend_error",
        node_id="ns=2;s=backend_error",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes if any issue arises"
    ),
    CoordinationVariable(
        name="plc_error",
        node_id="ns=2;s=plc_error",
        data_type="Boolean",
        initial_value=False,
        description="PLC writes if any issues"
    )
]
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L177-L232)

## Environment Variable Overrides

The configuration system supports environment variable overrides, allowing different settings for development, testing, and production environments.

### Environment Variable Mapping
All configuration parameters can be overridden via environment variables, typically defined in a `.env` file.

**Server Configuration Variables:**
- **OPCUA_SERVER_ENDPOINT**: Overrides the server endpoint URL
- **OPCUA_SERVER_NAME**: Overrides the server display name
- **OPCUA_NAMESPACE_URI**: Overrides the namespace URI
- **OPCUA_NAMESPACE_IDX**: Overrides the namespace numeric index

**Security Configuration Variables:**
- **OPCUA_SECURITY_POLICY**: Overrides the security policy
- **OPCUA_SECURITY_MODE**: Overrides the security mode
- **OPCUA_CERTIFICATE_PATH**: Overrides the certificate file path
- **OPCUA_PRIVATE_KEY_PATH**: Overrides the private key file path

**Connection Configuration Variables:**
- **OPCUA_CONNECTION_TIMEOUT**: Overrides the connection timeout
- **OPCUA_SESSION_TIMEOUT**: Overrides the session timeout

**Server Management Variables:**
- **OPCUA_AUTO_RESTART**: Enables/disables automatic restart
- **OPCUA_RESTART_DELAY**: Overrides the restart delay
- **OPCUA_MAX_RESTART_ATTEMPTS**: Overrides the maximum restart attempts

```python
def get_opcua_config() -> OPCUAServerConfig:
    return OPCUAServerConfig(
        endpoint=os.getenv("OPCUA_SERVER_ENDPOINT", "opc.tcp://0.0.0.0:4843/recoater/server/"),
        server_name=os.getenv("OPCUA_SERVER_NAME", "Recoater Multi-Material Coordination Server"),
        namespace_uri=os.getenv("OPCUA_NAMESPACE_URI", "http://recoater.backend.server"),
        namespace_idx=int(os.getenv("OPCUA_NAMESPACE_IDX", "2")),
        security_policy=os.getenv("OPCUA_SECURITY_POLICY", "None"),
        security_mode=os.getenv("OPCUA_SECURITY_MODE", "None"),
        certificate_path=os.getenv("OPCUA_CERTIFICATE_PATH", ""),
        private_key_path=os.getenv("OPCUA_PRIVATE_KEY_PATH", ""),
        connection_timeout=float(os.getenv("OPCUA_CONNECTION_TIMEOUT", "5.0")),
        session_timeout=float(os.getenv("OPCUA_SESSION_TIMEOUT", "60.0")),
        auto_restart=os.getenv("OPCUA_AUTO_RESTART", "true").lower() == "true",
        restart_delay=float(os.getenv("OPCUA_RESTART_DELAY", "5.0")),
        max_restart_attempts=int(os.getenv("OPCUA_MAX_RESTART_ATTEMPTS", "3"))
    )
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L235-L256)

## Configuration Integration

The OPC UA configuration is integrated into the server and coordinator components through a well-defined dependency chain.

### Server Initialization Process
The `OPCUAServerManager` class in `opcua_server.py` uses the configuration to initialize and start the OPC UA server.

```mermaid
sequenceDiagram
participant App as Application
participant Config as get_opcua_config()
participant Server as OPCUAServerManager
participant AsyncUA as asyncua.Server
App->>Config : Call get_opcua_config()
Config-->>Server : Return OPCUAServerConfig
Server->>Server : Initialize with config
Server->>AsyncUA : Create Server instance
Server->>AsyncUA : Set endpoint from config.endpoint
Server->>AsyncUA : Set server name from config.server_name
Server->>AsyncUA : Register namespace from config.namespace_uri
Server->>AsyncUA : Start server
AsyncUA-->>Server : Server started
Server-->>App : Return success
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L235-L256)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L270-L285)

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L270-L315)

### Coordinator Integration
The `OPCUACoordinator` class in `opcua_coordinator.py` uses both the configuration and server manager to provide a high-level interface.

```mermaid
classDiagram
class OPCUACoordinator {
-config : OPCUAServerConfig
-server_manager : OPCUAServerManager
-_connected : bool
+connect() bool
+disconnect() bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
}
class OPCUAServerManager {
-config : OPCUAServerConfig
-server : Server
-_running : bool
+start_server() bool
+stop_server() None
+write_variable(name, value) bool
+read_variable(name) Any
}
class OPCUAServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+certificate_path : str
+private_key_path : str
+connection_timeout : float
+session_timeout : float
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
OPCUACoordinator --> OPCUAServerManager : "uses"
OPCUACoordinator --> OPCUAServerConfig : "uses"
OPCUAServerManager --> OPCUAServerConfig : "uses"
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L200-L215)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L200-L215)

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L200-L590)

## Environment-Specific Patterns

The configuration system supports different deployment patterns for development, testing, and production environments.

### Development Environment
In development, the configuration prioritizes ease of use and debugging over security.

**Recommended Settings:**
- **OPCUA_SECURITY_POLICY**: `None`
- **OPCUA_SECURITY_MODE**: `None`
- **OPCUA_SERVER_ENDPOINT**: `opc.tcp://localhost:4843/recoater/server/`
- **OPCUA_AUTO_RESTART**: `true`

These settings allow developers to quickly start and test the system without certificate management.

### Testing Environment
In testing, the configuration balances realism with reproducibility.

**Recommended Settings:**
- **OPCUA_CONNECTION_TIMEOUT**: `10.0` (longer for test stability)
- **OPCUA_SESSION_TIMEOUT**: `120.0` (longer for test execution)
- **OPCUA_AUTO_RESTART**: `false` (to catch startup issues)
- Use mock PLC clients for integration testing

### Production Environment
In production, the configuration prioritizes security, reliability, and monitoring.

**Recommended Settings:**
- **OPCUA_SECURITY_POLICY**: `Basic256Sha256`
- **OPCUA_SECURITY_MODE**: `SignAndEncrypt`
- **OPCUA_CERTIFICATE_PATH**: `/etc/ssl/certs/opcua_server.crt`
- **OPCUA_PRIVATE_KEY_PATH**: `/etc/ssl/private/opcua_server.key`
- **OPCUA_AUTO_RESTART**: `true`
- **OPCUA_MAX_RESTART_ATTEMPTS**: `5`

These settings ensure secure communication and automatic recovery from failures.

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)

## Best Practices

### Securing Credentials and Certificates
When deploying in production, follow these security best practices:

**Certificate Management:**
- Store certificates and private keys in secure locations with restricted permissions
- Use environment variables or secret management systems for certificate paths
- Regularly rotate certificates according to security policies
- Validate certificate chains and expiration dates

**Security Configuration:**
- Never use `None` security policy in production environments
- Use `Basic256Sha256` with `SignAndEncrypt` for production deployments
- Implement proper certificate validation on the PLC side
- Regularly audit security configurations

### Performance Tuning
Optimize configuration parameters for performance and reliability:

**Connection Parameters:**
- Adjust `connection_timeout` based on network conditions
  - High-latency networks: Increase to 10-15 seconds
  - Local networks: Can reduce to 2-3 seconds
- Set `session_timeout` based on expected idle periods
  - Long print jobs: Increase to 300+ seconds
  - Interactive systems: Keep at 60 seconds

**Server Management:**
- Configure `auto_restart` for production reliability
- Set appropriate `restart_delay` to prevent thundering herd
- Limit `max_restart_attempts` to avoid infinite restart loops

### Configuration Validation
Implement validation checks to ensure configuration integrity:

**Startup Validation:**
- Verify that all coordination variables are created successfully
- Check that the namespace is properly registered
- Validate that the server endpoint is reachable

**Runtime Validation:**
- Monitor server health and restart attempts
- Log configuration changes for audit purposes
- Validate variable types during read/write operations

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)

## Validation and Error Handling

The configuration system includes robust validation and error handling mechanisms.

### Configuration Validation
The `get_opcua_config()` function performs type conversion and validation:

```python
def get_opcua_config() -> OPCUAServerConfig:
    return OPCUAServerConfig(
        endpoint=os.getenv("OPCUA_SERVER_ENDPOINT", "opc.tcp://0.0.0.0:4843/recoater/server/"),
        server_name=os.getenv("OPCUA_SERVER_NAME", "Recoater Multi-Material Coordination Server"),
        namespace_uri=os.getenv("OPCUA_NAMESPACE_URI", "http://recoater.backend.server"),
        namespace_idx=int(os.getenv("OPCUA_NAMESPACE_IDX", "2")),  # Converts string to int
        connection_timeout=float(os.getenv("OPCUA_CONNECTION_TIMEOUT", "5.0")),  # Converts to float
        session_timeout=float(os.getenv("OPCUA_SESSION_TIMEOUT", "60.0")),
        auto_restart=os.getenv("OPCUA_AUTO_RESTART", "true").lower() == "true",  # Converts to boolean
        restart_delay=float(os.getenv("OPCUA_RESTART_DELAY", "5.0")),
        max_restart_attempts=int(os.getenv("OPCUA_MAX_RESTART_ATTEMPTS", "3"))
    )
```

### Server Error Handling
The `OPCUAServerManager` implements automatic restart with exponential backoff:

```python
async def _handle_server_error(self, error: Exception) -> None:
    logger.error(f"OPC UA server error: {error}")
    
    if self.config.auto_restart and self._restart_count < self.config.max_restart_attempts:
        self._restart_count += 1
        logger.info(f"Attempting server restart ({self._restart_count}/{self.config.max_restart_attempts})")
        
        await asyncio.sleep(self.config.restart_delay)
        
        try:
            await self.start_server()
        except Exception as restart_error:
            logger.error(f"Server restart failed: {restart_error}")
    else:
        logger.error("Max restart attempts reached or auto-restart disabled")
```

### Variable Type Validation
The system validates variable types during read/write operations:

```python
async def write_variable(self, name: str, value: Any) -> bool:
    # Determine expected data type from configuration
    expected_type = None
    for var_def in COORDINATION_VARIABLES:
        if var_def.name == name:
            expected_type = var_def.data_type
            break

    # Coerce value to expected type
    if expected_type == "Int32" and isinstance(value, (int, bool)):
        coerced_value = int(value)
        ua_variant = ua.Variant(coerced_value, ua.VariantType.Int32)
    elif expected_type == "Boolean":
        coerced_value = bool(value)
        ua_variant = ua.Variant(coerced_value, ua.VariantType.Boolean)
    # ... other type coercions
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L235-L256)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L470-L495)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L340-L380)