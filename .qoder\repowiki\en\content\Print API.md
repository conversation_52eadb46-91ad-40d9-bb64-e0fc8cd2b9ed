# Print API

<cite>
**Referenced Files in This Document**   
- [print.py](file://backend/app/api/print.py#L1-L1012)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L668)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L111)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L200)
- [PrintView.vue](file://frontend/src/views/PrintView.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Endpoints](#core-endpoints)
3. [Request Parameters](#request-parameters)
4. [Response Schemas](#response-schemas)
5. [Error Handling](#error-handling)
6. [Job Lifecycle Workflow](#job-lifecycle-workflow)
7. [Integration with Multilayer Job System](#integration-with-multilayer-job-system)
8. [Frontend Integration](#frontend-integration)
9. [WebSocket Real-Time Updates](#websocket-real-time-updates)
10. [Usage Examples](#usage-examples)

## Introduction

The Print API provides RESTful endpoints for managing the lifecycle of print jobs in a multi-material 3D printing system. It enables users to start, pause, resume, and stop print operations, while coordinating with multiple drums (0-2) for complex multi-layer printing sequences. The API integrates with backend services for job orchestration and frontend components for user interaction.

This documentation details the available endpoints, request/response formats, error conditions, and integration points across the system architecture.

**Section sources**
- [print.py](file://backend/app/api/print.py#L1-L50)

## Core Endpoints

The Print API exposes the following primary endpoints for print job control:

```mermaid
flowchart TD
A["POST /api/print/start"] --> B["Starts a new print job"]
C["POST /api/print/pause"] --> D["Pauses an active print job"]
E["POST /api/print/resume"] --> F["Resumes a paused print job"]
G["POST /api/print/stop"] --> H["Stops and cancels a print job"]
I["GET /api/print/status"] --> J["Retrieves current job status"]
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py#L300-L400)

### Start Print Job
**Endpoint**: `POST /api/print/start`  
Initiates a new print job. The system validates readiness and begins the printing procedure, waiting for synchronization signals from the recoater hardware.

### Pause Print Job
**Endpoint**: `POST /api/print/pause`  
Temporarily halts an active print job, preserving the current state for resumption.

### Resume Print Job
**Endpoint**: `POST /api/print/resume`  
Continues a previously paused print job from the point of interruption.

### Stop Print Job
**Endpoint**: `POST /api/print/stop`  
Terminates and cancels the current print job, cleaning up resources and resetting the system state.

### Get Print Status
**Endpoint**: `GET /api/print/status`  
Retrieves the current status of the print job, including state, progress, and error information.

**Section sources**
- [print.py](file://backend/app/api/print.py#L300-L400)

## Request Parameters

The Print API accepts various parameters to control print operations:

### Layer Parameters Request
Used when configuring layer-specific settings:

```json
{
  "filling_id": 1,
  "speed": 50.0,
  "powder_saving": true,
  "x_offset": 2.5
}
```

**Parameters:**
- **filling_id**: Drum ID containing filling material (-1 for no filling)
- **speed**: Patterning speed in mm/s (must be > 0)
- **powder_saving**: Flag indicating whether powder saving strategies are used
- **x_offset**: Offset along X-axis in mm (optional)

### Multi-Material Job Request
Used when starting a multi-material job with specific file mappings:

```json
{
  "file_ids": {
    "0": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c",
    "1": "a1b2c3d4-5e6f-4a1b-8c2d-3e4f5a6b7c8d",
    "2": "e5f6a7b8-9c0d-4e5f-8a9b-0c1d2e3f4a5b"
  }
}
```

**Parameters:**
- **file_ids**: Mapping of drum_id (0,1,2) to uploaded file_id

### Layer Range Request
Specifies a range of layers to process:

```json
{
  "start_layer": 1,
  "end_layer": 10
}
```

**Parameters:**
- **start_layer**: Starting layer number (1-indexed, ≥1)
- **end_layer**: Ending layer number (1-indexed, ≥1)

**Section sources**
- [print.py](file://backend/app/api/print.py#L100-L200)

## Response Schemas

### Print Job Response
Standard response for job control operations:

```json
{
  "success": true,
  "status": "started",
  "job_id": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c"
}
```

**Fields:**
- **success**: Boolean indicating operation success
- **status**: Current job status string
- **job_id**: Unique identifier for the print job (optional)

### Print Job Status Response
Detailed status information for active jobs:

```json
{
  "state": "RUNNING",
  "is_printing": true,
  "has_error": false
}
```

**Fields:**
- **state**: Current job state (e.g., "RUNNING", "PAUSED", "IDLE")
- **is_printing**: Boolean indicating if currently printing
- **has_error**: Boolean indicating if an error condition exists

### Multi-Material Job Status Response
Comprehensive status for multi-material jobs:

```json
{
  "job_id": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c",
  "is_active": true,
  "status": "running",
  "current_layer": 5,
  "total_layers": 20,
  "progress_percentage": 20.0,
  "error_message": "",
  "drums": {
    "0": {
      "status": "ready",
      "ready": true,
      "uploaded": true,
      "current_layer": 5,
      "total_layers": 20,
      "error_message": "",
      "file_id": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c"
    },
    "1": {
      "status": "idle",
      "ready": false,
      "uploaded": false,
      "current_layer": 0,
      "total_layers": 15,
      "error_message": "Not ready",
      "file_id": "a1b2c3d4-5e6f-4a1b-8c2d-3e4f5a6b7c8d"
    },
    "2": {
      "status": "ready",
      "ready": true,
      "uploaded": true,
      "current_layer": 5,
      "total_layers": 20,
      "error_message": "",
      "file_id": "e5f6a7b8-9c0d-4e5f-8a9b-0c1d2e3f4a5b"
    }
  }
}
```

**Section sources**
- [print.py](file://backend/app/api/print.py#L150-L200)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L50-L110)

## Error Handling

The Print API implements comprehensive error handling with appropriate HTTP status codes:

### Common Error Responses

#### Invalid Job ID (404)
Returned when attempting to operate on a non-existent job:
```json
{
  "detail": "CLI file not found. Please upload the file first."
}
```

#### Conflicting State (409)
Returned when operation conflicts with current state:
```json
{
  "detail": "Cannot start print job: System not ready"
}
```

#### System Unavailability (503)
Returned when system dependencies are unavailable:
```json
{
  "detail": "Connection error: Failed to connect to recoater"
}
```

#### Validation Error (400)
Returned when request parameters are invalid:
```json
{
  "detail": "Invalid layer number. Must be between 1 and 20"
}
```

### Error Scenarios

| Scenario | HTTP Code | Description |
|--------|---------|-------------|
| Job not found | 404 | Referenced job ID does not exist |
| Invalid state transition | 409 | Operation not allowed in current state |
| System connection failure | 503 | Cannot communicate with recoater hardware |
| Invalid request parameters | 400 | Malformed or out-of-range parameters |
| Internal processing error | 500 | Unexpected server-side exception |

**Section sources**
- [print.py](file://backend/app/api/print.py#L350-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L50-L100)

## Job Lifecycle Workflow

The print job lifecycle follows a coordinated sequence across multiple components:

```mermaid
sequenceDiagram
participant Frontend as "Frontend (PrintView.vue)"
participant API as "Print API (print.py)"
participant JobManager as "MultiMaterialJobManager"
participant Model as "MultiMaterialJobState"
participant Hardware as "Recoater Hardware"
Frontend->>API : POST /api/print/start
API->>JobManager : start_job()
JobManager->>Model : Update job state to RUNNING
Model->>JobManager : State updated
JobManager->>Hardware : Initiate print sequence
Hardware->>JobManager : Ready for layers
JobManager->>JobManager : Coordinate layer uploads
loop For each layer
JobManager->>Hardware : Upload layer to drum 0
Hardware-->>JobManager : Confirmation
JobManager->>JobManager : Wait 2s
JobManager->>Hardware : Upload layer to drum 1
Hardware-->>JobManager : Confirmation
JobManager->>JobManager : Wait 2s
JobManager->>Hardware : Upload layer to drum 2
Hardware-->>JobManager : Confirmation
JobManager->>Model : Increment current_layer
end
JobManager->>Model : Set status to JOB_COMPLETE
Model-->>API : Job complete
API-->>Frontend : Success response
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py#L300-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L200-L300)

## Integration with Multilayer Job System

The Print API integrates with the multilayer job system through a layered architecture:

```mermaid
classDiagram
class PrintAPI {
+start_print_job()
+cancel_print_job()
+get_print_job_status()
}
class MultiMaterialJobManager {
-recoater_client
-cli_parser
-current_job
+create_job()
+start_job()
+cancel_job()
+get_job_status()
+upload_layer_to_all_drums()
}
class MultiMaterialJobState {
+job_id
+file_ids
+total_layers
+current_layer
+is_active
+status
+drums
+get_progress_percentage()
}
class DrumState {
+drum_id
+status
+ready
+uploaded
+current_layer
+total_layers
+error_message
+reset()
}
PrintAPI --> MultiMaterialJobManager : "uses"
MultiMaterialJobManager --> MultiMaterialJobState : "manages"
MultiMaterialJobState --> DrumState : "contains"
MultiMaterialJobManager --> DrumState : "controls"
```

### Data Flow
1. **Print API** receives HTTP requests from frontend
2. **MultiMaterialJobManager** coordinates job operations
3. **MultiMaterialJobState** maintains current job state
4. **DrumState** tracks individual drum status

The `MultiMaterialJobManager` serves as the central coordinator, managing the lifecycle of multi-material jobs and ensuring proper synchronization between the three drums. It validates file inputs, calculates total layers, and manages the progression through the print sequence.

When a job is created, the manager:
- Validates that 1-3 CLI files are provided
- Automatically adds empty templates for missing drums in single/dual material printing
- Calculates total layers as the maximum across all drums
- Prepares layer data for each drum, padding with empty layers as needed

**Section sources**
- [print.py](file://backend/app/api/print.py#L300-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L668)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L111)

## Frontend Integration

The Print API integrates with frontend components for user interaction:

```mermaid
graph TD
A["PrintView.vue"] --> B["Calls Print API endpoints"]
C["JobProgressDisplay.vue"] --> D["Displays job status"]
B --> E["POST /api/print/start"]
B --> F["POST /api/print/stop"]
B --> G["GET /api/print/status"]
D --> H["Consumes status responses"]
I["Vuex Store"] --> J["Stores job state"]
J --> D
J --> A
K["WebSocket"] --> J
K --> D
```

### PrintView.vue
The main print control component that:
- Provides UI controls for starting/stopping print jobs
- Handles user input for layer parameters
- Calls Print API endpoints via axios
- Updates Vuex store with operation results

### JobProgressDisplay.vue
The job progress visualization component that:
- Displays current job status and progress percentage
- Shows individual drum statuses
- Updates in real-time via WebSocket and polling
- Visualizes layer progression and completion

Both components use the Vuex store (`printJobStore.js`) to maintain shared state and coordinate between different views.

**Section sources**
- [PrintView.vue](file://frontend/src/views/PrintView.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)

## WebSocket Real-Time Updates

The system provides real-time status updates through WebSocket connections:

```mermaid
sequenceDiagram
participant Frontend as "Frontend"
participant WebSocket as "WebSocketManager"
participant JobManager as "JobManager"
participant Poller as "StatusPoller"
loop Every 1s
Poller->>JobManager : get_job_status()
JobManager-->>Poller : Status object
Poller->>WebSocket : broadcast_status()
WebSocket->>Frontend : Send status update
end
JobManager->>WebSocket : notify_status_change()
WebSocket->>Frontend : Immediate status update
Frontend->>WebSocket : Connect on page load
WebSocket-->>Frontend : Send initial status
```

The `websocket_manager.py` service maintains persistent connections with connected clients and broadcasts status updates when job states change. This complements the REST API by providing:
- Real-time progress updates without polling
- Immediate notification of state changes
- Reduced server load compared to frequent HTTP polling
- Smooth user experience with instant feedback

The frontend automatically connects to the WebSocket service on page load and receives the current job status immediately upon connection.

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L200)
- [PrintView.vue](file://frontend/src/views/PrintView.vue)

## Usage Examples

### Starting a Print Job
**curl command:**
```bash
curl -X POST http://localhost:8000/api/print/start \
  -H "Content-Type: application/json" \
  -d '{
    "file_ids": {
      "0": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c",
      "1": "a1b2c3d4-5e6f-4a1b-8c2d-3e4f5a6b7c8d",
      "2": "e5f6a7b8-9c0d-4e5f-8a9b-0c1d2e3f4a5b"
    }
  }'
```

**Successful Response (200):**
```json
{
  "success": true,
  "status": "started",
  "job_id": "f3a5b7c1-2d4e-4f1a-8b6c-9d2e8f1a7b3c"
}
```

### Getting Job Status
**curl command:**
```bash
curl -X GET http://localhost:8000/api/print/status
```

**Successful Response (200):**
```json
{
  "state": "RUNNING",
  "is_printing": true,
  "has_error": false
}
```

### Stopping a Print Job
**curl command:**
```bash
curl -X POST http://localhost:8000/api/print/stop
```

**Successful Response (202):**
```json
{
  "success": true,
  "status": "cancelled"
}
```

### Error Response (404)
**Invalid Job ID:**
```json
{
  "detail": "CLI file not found. Please upload the file first."
}
```

### Error Response (409)
**Conflicting State:**
```json
{
  "detail": "Cannot start print job: System not ready"
}
```

**Section sources**
- [print.py](file://backend/app/api/print.py#L300-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L200-L300)