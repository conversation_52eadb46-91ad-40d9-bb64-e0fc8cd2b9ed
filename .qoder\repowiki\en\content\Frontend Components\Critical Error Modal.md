# Critical Error Modal

<cite>
**Referenced Files in This Document**   
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)
- [CriticalErrorModal.test.js](file://frontend/src/components/__tests__/CriticalErrorModal.test.js)
- [status.js](file://frontend/src/stores/status.js)
- [print.py](file://backend/app/api/print.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [ErrorDisplayPanel.vue](file://frontend/src/components/ErrorDisplayPanel.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Overview](#component-overview)
3. [Architecture and Data Flow](#architecture-and-data-flow)
4. [Detailed Component Analysis](#detailed-component-analysis)
5. [Error Handling and Clearing Mechanism](#error-handling-and-clearing-mechanism)
6. [Testing Scenarios](#testing-scenarios)
7. [Edge Cases and Resilience](#edge-cases-and-resilience)
8. [UX Recommendations](#ux-recommendations)
9. [Conclusion](#conclusion)

## Introduction
The **Critical Error Modal** is a high-severity alert component in the APIRecoater_Ethernet system designed to notify operators of critical system failures that require immediate intervention. These errors typically stem from hardware faults, communication breakdowns with the PLC (Programmable Logic Controller), or internal backend system failures. The modal interrupts the user interface to ensure the error is acknowledged and resolved before normal operations can resume.

This document provides a comprehensive analysis of the `CriticalErrorModal.vue` component, detailing its integration with the Vuex store, backend error propagation via OPC UA, and the complete error clearing workflow. The analysis is based on direct code inspection of the frontend and backend components involved in the critical error handling pipeline.

## Component Overview
The `CriticalErrorModal.vue` component is a Vue.js single-file component that renders a full-screen modal dialog when a critical error is detected in the system. It is designed to be highly visible and informative, providing operators with clear guidance on the nature of the error and the steps required to resolve it.

The modal is triggered by a global state change in the Vuex store (`status.js`), specifically when the `hasCriticalError` flag is set to `true`. This flag is updated based on the status of two error flags: `backend_error` and `plc_error`, which are synchronized with the backend system via OPC UA.

**Section sources**
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue#L1-L200)
- [status.js](file://frontend/src/stores/status.js#L1-L100)

## Architecture and Data Flow
The critical error system spans both the frontend and backend, with error states being propagated from the physical hardware through the backend services to the frontend UI.

```mermaid
sequenceDiagram
participant PLC as PLC System
participant Backend as Backend Service
participant OPCUA as OPC UA Server
participant Frontend as Frontend UI
participant Modal as CriticalErrorModal.vue
PLC->>Backend : Set plc_error flag via OPC UA
Backend->>OPCUA : Write plc_error variable
OPCUA->>Backend : Notify of variable change
Backend->>Frontend : WebSocket update or API poll
Frontend->>Frontend : Update printJobStore.errorFlags
Frontend->>Modal : hasCriticalError computed property triggers
Modal->>Modal : Render modal with error details
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L461-L541)
- [print.py](file://backend/app/api/print.py#L961-L1000)
- [status.js](file://frontend/src/stores/status.js#L1-L50)
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue#L1-L50)

### Key Data Flow Steps:
1. **Error Detection**: A hardware fault or system failure is detected by either the PLC or the backend service.
2. **Flag Setting**: The respective system sets its error flag (`plc_error` or `backend_error`) via the OPC UA server.
3. **State Synchronization**: The backend service monitors these OPC UA variables and updates its internal state.
4. **Frontend Notification**: The frontend periodically polls the backend or receives WebSocket updates about the system status.
5. **Store Update**: The Vuex store (`printJobStore`) updates the `errorFlags` object with the current state of both error flags.
6. **Modal Trigger**: The `hasCriticalError` computed property in the store evaluates to `true` if either error flag is active, triggering the modal display.

## Detailed Component Analysis

### CriticalErrorModal.vue
The `CriticalErrorModal.vue` component is structured as a standard Vue.js component with template, script, and style sections. It relies heavily on the `printJobStore` Vuex store for its state.

#### Template Structure
The modal's template includes:
- A header with a warning icon and title
- Sections for error type, impact, and action steps
- A footer with timestamp and action buttons

```vue
<template>
  <div v-if="printJobStore.hasCriticalError" class="critical-error-modal-overlay">
    <div class="critical-error-modal">
      <div class="modal-header">
        <div class="error-icon">⚠️</div>
        <h2>Critical System Error</h2>
        <button @click="closeModal" class="close-button">✕</button>
      </div>

      <div class="modal-content">
        <div class="error-details">
          <div class="error-type-section">
            <h3>Error Type</h3>
            <div class="error-flags">
              <div v-if="printJobStore.errorFlags.backendError" class="error-flag backend-error">
                <div class="flag-icon">🔧</div>
                <div class="flag-content">
                  <div class="flag-title">Backend Error</div>
                  <div class="flag-description">The recoater backend system has encountered an error and requires attention.</div>
                </div>
              </div>
              <div v-if="printJobStore.errorFlags.plcError" class="error-flag plc-error">
                <div class="flag-icon">⚡</div>
                <div class="flag-content">
                  <div class="flag-title">PLC Error</div>
                  <div class="flag-description">The PLC system has detected a hardware or communication fault.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="footer-info">
          <span class="timestamp">Error occurred at: {{ formatErrorTime() }}</span>
        </div>
        <div class="footer-actions">
          <button @click="clearErrors" :disabled="printJobStore.isClearingErrors" class="btn btn-primary clear-errors-btn">
            <span v-if="printJobStore.isClearingErrors">Clearing...</span>
            <span v-else>Clear Error Flags</span>
          </button>
          <button @click="closeModal" class="btn btn-secondary">Acknowledge</button>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### Script Logic
The component's script section defines methods for closing the modal and clearing error flags:

```javascript
methods: {
  closeModal() {
    this.printJobStore.closeCriticalModal();
  },
  async clearErrors() {
    try {
      await this.printJobStore.clearErrorFlagsAPI();
    } catch (error) {
      console.error('Failed to clear error flags:', error);
    }
  }
}
```

**Section sources**
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue#L1-L200)

## Error Handling and Clearing Mechanism

### Backend Error Flag Management
The backend system uses two distinct error flags defined in the OPC UA configuration:

```python
# opcua_config.py
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    # ...
    CoordinationVariable(
        name="backend_error",
        node_id="ns=2;s=backend_error",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes if any issue arises"
    ),
    CoordinationVariable(
        name="plc_error",
        node_id="ns=2;s=plc_error",
        data_type="Boolean",
        initial_value=False,
        description="PLC writes if any issues"
    )
]
```

These flags are managed by the `OPCUACoordinator` class, which provides methods to set and clear them:

```python
# opcua_coordinator.py
async def set_backend_error(self, error: bool) -> bool:
    success = await self.write_variable("backend_error", error)
    return success

async def set_plc_error(self, error: bool) -> bool:
    success = await self.write_variable("plc_error", error)
    return success

async def clear_error_flags(self) -> bool:
    success = True
    success &= await self.set_backend_error(False)
    success &= await self.set_plc_error(False)
    return success
```

### API Endpoint for Error Clearing
The frontend calls a dedicated API endpoint to clear error flags:

```python
# print.py
@router.post("/multimaterial-job/clear-error", response_model=MultiMaterialJobResponse)
async def clear_multimaterial_job_errors(
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> MultiMaterialJobResponse:
    try:
        cleared = await job_manager.clear_error_flags()
        if cleared:
            return MultiMaterialJobResponse(
                success=True,
                message="Error flags cleared successfully"
            )
        else:
            return MultiMaterialJobResponse(
                success=False,
                message="Failed to clear error flags"
            )
    except Exception as e:
        logger.error(f"Unexpected error clearing error flags: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
```

### Service-Level Implementation
The actual clearing logic is implemented in the `MultiMaterialJobManager` service:

```python
# multilayer_job_manager.py
async def clear_error_flags(self) -> bool:
    """Clear all error flags in the system."""
    try:
        # Clear backend error flag
        await self.opcua_coordinator.set_backend_error(False)
        
        # Clear PLC error flag
        await self.opcua_coordinator.set_plc_error(False)
        
        # Reset internal error state
        self._has_error = False
        self.error_message = None
        
        logger.info("All error flags cleared successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to clear error flags: {e}")
        return False
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L176-L232)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L461-L541)
- [print.py](file://backend/app/api/print.py#L961-L1000)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L100)

## Testing Scenarios
The component is thoroughly tested in `CriticalErrorModal.test.js`, covering various scenarios:

```javascript
// CriticalErrorModal.test.js
it('shows PLC error flag when present', () => {
  const plcError = wrapper.find('.plc-error')
  expect(plcError.exists()).toBe(true)
  expect(plcError.find('.flag-title').text()).toBe('PLC Error')
})

it('displays error message when provided', () => {
  const errorMessageBox = wrapper.find('.error-message-box')
  expect(errorMessageBox.exists()).toBe(true)
  expect(errorMessageBox.text()).toBe('Multiple system errors detected')
})

it('shows impact section with correct items', () => {
  const impactItems = wrapper.findAll('.impact-item')
  expect(impactItems).toHaveLength(3)
  expect(impactItems[0].text()).toContain('All print operations have been paused')
})

it('calls clearErrorFlagsAPI when clear button is clicked', async () => {
  const mockClearErrorFlags = vi.spyOn(mockPrintJobStore, 'clearErrorFlagsAPI')
    .mockResolvedValue({ success: true })
  const clearButton = wrapper.find('[data-testid="clear-error-flags-btn"]')
  await clearButton.trigger('click')
  expect(mockClearErrorFlags).toHaveBeenCalled()
})
```

The tests verify:
- Correct rendering of error flags
- Visibility of error messages
- Proper display of impact and action steps
- Functionality of the "Clear Error Flags" button
- Loading state during API calls
- Graceful handling of API failures

**Section sources**
- [CriticalErrorModal.test.js](file://frontend/src/components/__tests__/CriticalErrorModal.test.js#L72-L268)

## Edge Cases and Resilience

### Error Flood Protection
The system does not currently implement explicit rate limiting for error flag updates. However, the OPC UA server and backend services act as natural buffers, preventing UI-level spam from overwhelming the system.

### Incomplete Error Data
The modal gracefully handles cases where error data is incomplete:
- If no error message is provided, the message section is hidden
- The modal displays only the active error flags (backend, PLC, or both)
- Default impact and action steps are shown even without custom messages

### API Failure Handling
When the `clearErrorFlagsAPI` call fails, the system logs the error but maintains the modal state:

```javascript
const clearErrors = async () => {
  try {
    await printJobStore.clearErrorFlagsAPI()
    addToErrorHistory('System', 'Error flags cleared by operator')
  } catch (error) {
    console.error('Failed to clear error flags:', error)
    addToErrorHistory('System', `Failed to clear error flags: ${error.message}`)
  }
}
```

This ensures that operators are aware of clearing failures and can attempt the operation again.

**Section sources**
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue#L1-L200)
- [ErrorDisplayPanel.vue](file://frontend/src/components/ErrorDisplayPanel.vue#L186-L234)

## UX Recommendations

### Visibility and Attention
- **Enhance Visual Hierarchy**: Use high-contrast colors (red/black) for the modal overlay to ensure it captures attention immediately.
- **Add Audio Alert**: Implement a non-intrusive audio cue when the modal appears to alert operators who may not be looking at the screen.
- **Persistent Notification**: Keep the modal visible until acknowledged, even if the user navigates to other views.

### Clarity and Guidance
- **Specific Error Details**: When possible, include specific error codes or diagnostic information to help operators identify the root cause.
- **Contextual Help**: Provide links to troubleshooting guides or system documentation within the modal.
- **Progressive Disclosure**: Show basic information by default, with an option to expand for technical details.

### Recovery Workflow
- **Clear Success Feedback**: After successfully clearing errors, display a temporary success message before closing the modal.
- **Automated Reconnection**: Attempt to re-establish critical connections automatically after error clearance.
- **Pre-Clear Validation**: Add a confirmation dialog for the "Clear Error Flags" button to prevent accidental clearance.

### Accessibility
- **Keyboard Navigation**: Ensure all modal actions are accessible via keyboard (Tab, Enter, Esc).
- **Screen Reader Support**: Use proper ARIA attributes (`aria-modal="true"`, `role="dialog"`) to ensure screen readers announce the modal appropriately.
- **Focus Management**: Trap focus within the modal and return focus to the triggering element when closed.

## Conclusion
The Critical Error Modal is a vital component of the APIRecoater_Ethernet system's error handling architecture, providing a clear and actionable interface for operators to respond to high-severity system failures. Its integration with the OPC UA-based backend ensures that both hardware and software errors are promptly communicated to the user interface.

The component demonstrates a well-structured approach to error management, with proper separation of concerns between the frontend presentation layer and backend service logic. The testing suite provides good coverage of core functionality, though opportunities exist to enhance resilience against error floods and improve the user experience for error recovery.

Future improvements should focus on enhancing the modal's accessibility, providing more detailed diagnostic information, and implementing safeguards against accidental error clearance.