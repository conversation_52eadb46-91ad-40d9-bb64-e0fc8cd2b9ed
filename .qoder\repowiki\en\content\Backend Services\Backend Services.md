# Backend Services

<cite>
**Referenced Files in This Document**   
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py) - *Updated in recent commit*
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [print.py](file://backend/app/api/print.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py) - *Updated in recent commit*
- [empty_layer.cli](file://backend/app/templates/empty_layer.cli) - *New template file for single/dual material printing*
- [mock_client.py](file://backend/infrastructure/mock_recoater_client/mock_client.py) - *Refactored into package in recent commit*
- [mock_async_client.py](file://backend/infrastructure/mock_recoater_client/mock_async_client.py) - *Refactored into package in recent commit*
- [mock_blade_controls.py](file://backend/infrastructure/mock_recoater_client/mock_blade_controls.py) - *Refactored into package in recent commit*
- [mock_leveler_controls.py](file://backend/infrastructure/mock_recoater_client/mock_leveler_controls.py) - *Refactored into package in recent commit*
- [mock_drum_controls.py](file://backend/infrastructure/mock_recoater_client/mock_drum_controls.py) - *Refactored into package in recent commit*
- [mock_file_management.py](file://backend/infrastructure/mock_recoater_client/mock_file_management.py) - *Refactored into package in recent commit*
- [mock_print_controls.py](file://backend/infrastructure/mock_recoater_client/mock_print_controls.py) - *Refactored into package in recent commit*
- [__init__.py](file://backend/infrastructure/mock_recoater_client/__init__.py) - *New package initialization*
</cite>

## Update Summary
**Changes Made**   
- Updated Multilayer Job Manager section to reflect changes in handling single/dual material printing with empty layer templates
- Added documentation for the empty_layer.cli template file and its role in job creation
- Updated OPC UA Server section to reflect server stability fixes and error handling improvements
- Enhanced error handling and recovery section with details about OPC UA server auto-restart capabilities
- Added new diagram showing empty layer template integration in job creation workflow
- Updated documentation to reflect the refactoring of mock_recoater_client into a dedicated package with modular components
- Added new sections documenting the mock client package structure and testing infrastructure

## Table of Contents
1. [Introduction](#introduction)
2. [Core Services Overview](#core-services-overview)
3. [Coordination Engine](#coordination-engine)
4. [Multilayer Job Manager](#multilayer-job-manager)
5. [OPC UA Coordinator](#opc-ua-coordinator)
6. [Status Poller](#status-poller)
7. [WebSocket Manager](#websocket-manager)
8. [Configuration System](#configuration-system)
9. [Workflow and Invocation Relationships](#workflow-and-invocation-relationships)
10. [Error Handling and Recovery](#error-handling-and-recovery)
11. [Mock Testing Infrastructure](#mock-testing-infrastructure)
12. [Architecture Diagram](#architecture-diagram)

## Introduction
The backend services layer of APIRecoater_Ethernet provides the core functionality for managing multi-material 3D printing operations. This document details the key services that coordinate between the frontend interface, backend logic, and physical recoater hardware via OPC UA communication. The system is designed to handle complex multi-layer print jobs across three drums, managing workflow sequencing, job state, device control, system monitoring, and real-time updates. Each service plays a specific role in the overall architecture, working together to ensure reliable and coordinated operation of the recoater system.

## Core Services Overview
The backend architecture consists of five primary services that handle different aspects of the recoater system:

- **CoordinationEngine**: Manages the workflow sequencing for multi-material print jobs
- **MultilayerJobManager**: Handles job state and layer progression logic
- **OPCUACoordinator**: Interfaces with OPC UA servers for device control and monitoring
- **StatusPoller**: Monitors system health and component status
- **WebSocketManager**: Pushes real-time updates to the frontend interface

These services work together to process print commands, coordinate between multiple drums, maintain job state, communicate with the PLC, and provide real-time feedback to the user interface. The system follows a service-oriented architecture with clear separation of concerns, allowing each component to focus on its specific responsibility while collaborating through well-defined interfaces.

## Coordination Engine
The CoordinationEngine is responsible for managing the workflow sequencing between recoater operations during multi-material print jobs. It orchestrates the entire printing process from job initiation to completion, ensuring proper coordination between the three drums and synchronization with the PLC.

### State Management
The engine implements a finite state machine with the following states:

``mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> UPLOADING : start_multimaterial_job()
UPLOADING --> WAITING_FOR_READY : upload_layer_to_all_drums()
WAITING_FOR_READY --> PRINTING : all_drums_ready()
PRINTING --> WAITING_FOR_COMPLETION : set_recoater_ready_to_print(true)
WAITING_FOR_COMPLETION --> UPLOADING : layer_complete()
WAITING_FOR_COMPLETION --> ERROR : timeout/error
UPLOADING --> ERROR : upload_failed
WAITING_FOR_READY --> ERROR : timeout
ERROR --> IDLE : clear_errors()
UPLOADING --> COMPLETE : last_layer_completed
COMPLETE --> IDLE : job_cleanup
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L32)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L32)

### Workflow Orchestration
The coordination engine manages the printing workflow through a main coordination loop that processes each layer in sequence:

``mermaid
flowchart TD
Start([Start Job]) --> Upload["Upload layer to all drums"]
Upload --> WaitReady["Wait for all drums to be ready"]
WaitReady --> SignalReady["Signal PLC: recoater_ready_to_print = TRUE"]
SignalReady --> WaitComplete["Wait for layer completion"]
WaitComplete --> SignalComplete["Signal PLC: recoater_layer_complete = TRUE"]
SignalComplete --> NextLayer["Advance to next layer"]
NextLayer --> CheckComplete{"All layers done?"}
CheckComplete --> |No| Upload
CheckComplete --> |Yes| CompleteJob["Complete job successfully"]
CompleteJob --> End([Job Complete])
Upload -.-> ErrorState["Set error state on failure"]
WaitReady -.-> ErrorState
WaitComplete -.-> ErrorState
ErrorState --> End
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L120-L150)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L120-L150)

The engine follows a strict sequence for each layer:
1. Upload the current layer data to all three drums with a configurable delay between uploads
2. Wait for all drums to report they are ready for printing
3. Signal the PLC that the recoater is ready to print by setting the `recoater_ready_to_print` variable to TRUE
4. Wait for the PLC to complete the layer deposition
5. Signal layer completion to the PLC by setting `recoater_layer_complete` to TRUE
6. Advance to the next layer and repeat the process

This sequential approach ensures that each layer is properly processed across all drums before moving to the next layer, maintaining synchronization between the different material sources.

## Multilayer Job Manager
The MultilayerJobManager handles job state management and layer progression logic for multi-material print jobs. It maintains the state of active jobs, tracks layer progression, and provides an interface for job manipulation.

### Job State Model
The job manager uses a comprehensive state model to track the progress of multi-material jobs:

``mermaid
classDiagram
class MultiMaterialJobState {
+str job_id
+int total_layers
+int current_layer
+JobStatus status
+bool is_active
+Dict[int, List[LayerData]] remaining_layers
}
class JobStatus {
+IDLE
+RUNNING
+PAUSED
+COMPLETED
+CANCELLED
+ERROR
}
class DrumState {
+int drum_id
+str status
+bool ready
+bool uploaded
+int current_layer
+int total_layers
+str error_message
+str file_id
}
MultiMaterialJobState --> JobStatus : "has status"
MultiMaterialJobState --> DrumState : "has 3 drums"
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [multilayer_job_manager.py](file://backend/app/models/multilayer_job.py)

### Layer Progression Logic
The job manager implements sophisticated layer progression logic that handles the distribution of layers across multiple drums:

```python
def get_layers_for_job(self, file_ids: Dict[int, str]) -> MultiMaterialJobState:
    """
    Create a job state from multiple CLI files, distributing layers across drums.
    
    Args:
        file_ids: Mapping of drum_id to file_id for each drum
        
    Returns:
        MultiMaterialJobState: Complete job state with layer distribution
    """
    # Parse all CLI files and extract layers
    parsed_files = {}
    for drum_id, file_id in file_ids.items():
        if file_id in cli_file_cache:
            parsed_files[drum_id] = cli_file_cache[file_id]['parsed_file']
    
    # Determine total layers (maximum across all files)
    total_layers = max(len(parsed_files[drum_id].layers) 
                      for drum_id in parsed_files.keys())
    
    # Create job state with layer distribution
    job_state = MultiMaterialJobState(
        job_id=str(uuid.uuid4()),
        total_layers=total_layers,
        current_layer=1,
        status=JobStatus.IDLE,
        is_active=False,
        remaining_layers={}
    )
    
    # Distribute layers for each drum
    for drum_id, parsed_file in parsed_files.items():
        layers = []
        for i, layer in enumerate(parsed_file.layers):
            layers.append(LayerData(
                layer_number=i+1,
                cli_data=self.cli_parser.generate_single_layer_ascii_cli(
                    layer, parsed_file.header_lines
                ),
                is_empty=False
            ))
        job_state.remaining_layers[drum_id] = layers
    
    return job_state
```

The layer progression system handles several important scenarios:
- **Uneven layer counts**: When different drums have different numbers of layers, the system continues processing until the drum with the most layers is complete
- **Empty layers**: The system can handle empty layers (no material deposition) for specific drums on specific layers
- **Layer skipping**: If a drum runs out of material, the system continues with the remaining drums
- **Job resumption**: The system can resume jobs from the current layer if interrupted

### Empty Layer Template Integration
The system now supports single and dual material printing through the use of empty layer templates. When fewer than three CLI files are provided, the system automatically adds empty templates for the missing drums:

``mermaid
flowchart TD
Start([Start Job]) --> CheckFiles["Check number of CLI files"]
CheckFiles --> |1 or 2 files| LoadTemplate["Load empty_layer.cli template"]
LoadTemplate --> ParseTemplate["Parse template with CLI parser"]
ParseTemplate --> GenerateID["Generate unique file ID"]
GenerateID --> CacheTemplate["Add to CLI cache"]
CacheTemplate --> AssignFiles["Assign template to missing drums"]
AssignFiles --> Continue["Continue with job creation"]
CheckFiles --> |3 files| Continue
Continue --> CreateJob["Create job with all files"]
CreateJob --> End([Job Created])
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L476-L544)
- [empty_layer.cli](file://backend/app/templates/empty_layer.cli)

**Section sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L476-L544)
- [empty_layer.cli](file://backend/app/templates/empty_layer.cli)

The empty layer template (`empty_layer.cli`) is a minimal CLI file that contains no polylines, effectively creating a "no material" layer. This allows the system to maintain synchronization across all three drums even when printing with only one or two materials. The template is loaded once and cached for reuse, with each instance receiving a unique file ID to maintain proper tracking.

When creating a job with fewer than three materials:
1. The system identifies which drums are missing CLI files
2. For each missing drum, it loads and parses the empty_layer.cli template
3. The parsed template is added to the CLI cache with a unique ID
4. The template is assigned to the appropriate drum in the job configuration
5. The job proceeds with the complete set of three drum configurations

This enhancement provides greater flexibility in printing configurations while maintaining the integrity of the multi-drum coordination system.

## OPC UA Coordinator
The OPCUACoordinator service provides the interface between the backend system and the OPC UA server for device control and monitoring. It handles all communication with the PLC through the OPC UA protocol.

### Variable Management
The coordinator manages a set of coordination variables that serve as the communication channel between the backend and PLC:

```python
# Simplified coordination variables for backend-PLC coordination
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    # Job Control
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    CoordinationVariable(
        name="current_layer",
        node_id="ns=2;s=current_layer",
        data_type="Int32",
        initial_value=0,
        description="Backend manages, PLC reads"
    ),

    # Recoater Coordination
    CoordinationVariable(
        name="recoater_ready_to_print",
        node_id="ns=2;s=recoater_ready_to_print",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when Aerosint is ready"
    ),
    CoordinationVariable(
        name="recoater_layer_complete",
        node_id="ns=2;s=recoater_layer_complete",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when deposition complete"
    ),

    # System Status
    CoordinationVariable(
        name="backend_error",
        node_id="ns=2;s=backend_error",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes if any issue arises"
    ),
    CoordinationVariable(
        name="plc_error",
        node_id="ns=2;s=plc_error",
        data_type="Boolean",
        initial_value=False,
        description="PLC writes if any issues"
    )
]
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L100-L150)

### Communication Flow
The OPC UA coordinator implements a bidirectional communication flow between the backend and PLC:

``mermaid
sequenceDiagram
participant Backend as "Backend Service"
participant OPCUA as "OPC UA Server"
participant PLC as "PLC Client"
Backend->>OPCUA : set_job_active(total_layers)
OPCUA->>PLC : job_active = TRUE, total_layers = N
PLC-->>OPCUA : Acknowledges job start
OPCUA-->>Backend : Confirmation
loop For each layer
Backend->>OPCUUA : update_layer_progress(current_layer)
OPCUA->>PLC : current_layer = N
Backend->>OPCUA : set_recoater_ready_to_print(TRUE)
OPCUA->>PLC : recoater_ready_to_print = TRUE
PLC->>OPCUA : Begins deposition
PLC->>OPCUA : Sets plc_error = TRUE on error
OPCUA->>Backend : Error notification
PLC->>OPCUA : Signals completion
OPCUA->>Backend : Completion detected
Backend->>OPCUA : set_recoater_layer_complete(TRUE)
OPCUA->>PLC : recoater_layer_complete = TRUE
end
Backend->>OPCUA : set_job_inactive()
OPCUA->>PLC : job_active = FALSE
PLC-->>OPCUA : Acknowledges job end
OPCUA-->>Backend : Confirmation
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)

The coordinator provides high-level methods for job control:
- `set_job_active(total_layers)`: Activates a job and sets the total layer count
- `set_job_inactive()`: Deactivates the current job
- `update_layer_progress(current_layer)`: Updates the current layer number
- `set_recoater_ready_to_print(ready)`: Signals the PLC that the recoater is ready
- `set_recoater_layer_complete(complete)`: Signals layer completion to the PLC
- `set_backend_error(error)`: Sets the backend error flag
- `clear_error_flags()`: Clears all error flags

## Status Poller
The StatusPoller service continuously monitors the health and status of the recoater system components. It provides real-time status updates and detects potential issues before they affect print operations.

### Polling Architecture
The status poller implements a multi-level monitoring system:

``mermaid
flowchart TD
Start([Start Polling]) --> PollRecoater["Poll recoater hardware status"]
PollRecoater --> PollOPCUA["Poll OPC UA server status"]
PollOPCUA --> PollDrums["Poll individual drum status"]
PollDrums --> ProcessStatus["Process status data"]
ProcessStatus --> CheckErrors{"Errors detected?"}
CheckErrors --> |Yes| HandleError["Handle error condition"]
CheckErrors --> |No| UpdateStatus["Update system status"]
UpdateStatus --> NotifyWebSocket["Notify WebSocket clients"]
NotifyWebSocket --> Delay["Wait polling interval"]
Delay --> PollRecoater
HandleError --> NotifyWebSocket
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py)

The poller runs at a configurable interval (default: 1 second) and checks multiple system components:
- **Recoater hardware**: Connectivity and overall status
- **OPC UA server**: Availability and communication status
- **Individual drums**: Ready state, error conditions, and operational status
- **Job progress**: Current layer and completion percentage

When status changes are detected, the poller updates the system state and pushes notifications to connected WebSocket clients, ensuring the frontend interface remains synchronized with the actual system state.

## WebSocket Manager
The WebSocketManager handles real-time communication between the backend and frontend, pushing status updates and notifications to connected clients.

### Connection Management
The manager implements a robust connection handling system:

```python
class WebSocketConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        """Initialize the connection manager."""
        self.active_connections: List[WebSocket] = []
        # Track subscriptions for each connection
        self.connection_subscriptions: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket) -> None:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: The WebSocket connection to accept
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        # Initialize with default subscription (status only)
        self.connection_subscriptions[websocket] = {'status'}
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
```

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L20-L40)

### Message Broadcasting
The manager supports subscription-based message filtering to optimize network usage:

``mermaid
flowchart TD
Start([Message Received]) --> CheckSubscriptions["Check connection subscriptions"]
CheckSubscriptions --> FilterMessage["Filter message content"]
FilterMessage --> Connection1["Connection 1: status, axis"]
FilterMessage --> Connection2["Connection 2: status, drum"]
FilterMessage --> Connection3["Connection 3: status, print"]
Connection1 --> SendFiltered["Send filtered message"]
Connection2 --> SendFiltered
Connection3 --> SendFiltered
SendFiltered --> HandleErrors["Handle send errors"]
HandleErrors --> Cleanup["Remove disconnected clients"]
Cleanup --> End([Broadcast Complete])
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L80-L120)

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L80-L120)

Clients can subscribe to specific data types:
- **status**: System status and job progress
- **axis**: Axis position and movement data
- **drum**: Drum status and readiness
- **leveler**: Leveler position and status
- **print**: Print job-specific data

The manager automatically filters messages based on each client's subscriptions, ensuring clients only receive relevant data and reducing network overhead.

## Configuration System
The configuration system provides flexible setup options for the OPC UA server and coordination parameters through environment variables and configuration files.

### OPC UA Configuration
The system uses a comprehensive configuration structure for OPC UA settings:

```python
@dataclass
class OPCUAServerConfig:
    """Configuration for OPC UA server hosting."""
    
    # Server endpoint configuration
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    
    # Namespace configuration
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    
    # Security settings
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    
    # Connection settings
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    
    # Server management
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L50-L80)

### Environment Variable Overrides
All configuration parameters can be overridden using environment variables:

| Environment Variable | Configuration Parameter | Default Value |
|----------------------|------------------------|---------------|
| OPCUA_SERVER_ENDPOINT | endpoint | opc.tcp://0.0.0.0:4843/recoater/server/ |
| OPCUA_SERVER_NAME | server_name | Recoater Multi-Material Coordination Server |
| OPCUA_NAMESPACE_URI | namespace_uri | http://recoater.backend.server |
| OPCUA_NAMESPACE_IDX | namespace_idx | 2 |
| OPCUA_SECURITY_POLICY | security_policy | None |
| OPCUA_SECURITY_MODE | security_mode | None |
| OPCUA_CONNECTION_TIMEOUT | connection_timeout | 5.0 |
| OPCUA_SESSION_TIMEOUT | session_timeout | 60.0 |
| OPCUA_AUTO_RESTART | auto_restart | true |
| OPCUA_RESTART_DELAY | restart_delay | 5.0 |
| OPCUA_MAX_RESTART_ATTEMPTS | max_restart_attempts | 3 |

This flexible configuration system allows the application to be easily adapted to different deployment environments without code changes.

## Workflow and Invocation Relationships
The backend services work together in a coordinated manner to process print commands and manage the printing workflow. The invocation relationships between components follow a clear pattern from API request to hardware operation.

### Print Command Propagation
When a print command is issued, it propagates through the system as follows:

``mermaid
sequenceDiagram
participant Frontend as "Frontend"
participant API as "Print API"
participant JobManager as "MultilayerJobManager"
participant CoordinationEngine as "CoordinationEngine"
participant OPCUACoordinator as "OPCUACoordinator"
participant PLC as "PLC"
Frontend->>API : POST /api/v1/print/multimaterial/start
API->>JobManager : create_job_state(file_ids)
JobManager-->>API : MultiMaterialJobState
API->>CoordinationEngine : start_multimaterial_job(job_state)
CoordinationEngine->>OPCUACoordinator : set_job_active(total_layers)
CoordinationEngine->>OPCUACoordinator : update_layer_progress(1)
CoordinationEngine->>OPCUACoordinator : clear_error_flags()
OPCUACoordinator->>PLC : Write job_active=TRUE, total_layers=N
OPCUACoordinator->>PLC : Write current_layer=1
OPCUACoordinator->>PLC : Write backend_error=FALSE
CoordinationEngine->>API : Job started successfully
API-->>Frontend : Success response
loop Layer Processing
CoordinationEngine->>CoordinationEngine : _process_layer_cycle_all_drums()
CoordinationEngine->>CoordinationEngine : _upload_layer_to_all_drums()
CoordinationEngine->>CoordinationEngine : _wait_for_all_drums_ready()
CoordinationEngine->>OPCUACoordinator : set_recoater_ready_to_print(TRUE)
OPCUACoordinator->>PLC : Write recoater_ready_to_print=TRUE
CoordinationEngine->>CoordinationEngine : _wait_for_layer_completion()
CoordinationEngine->>OPCUACoordinator : set_recoater_layer_complete(TRUE)
OPCUACoordinator->>PLC : Write recoater_layer_complete=TRUE
CoordinationEngine->>CoordinationEngine : Advance to next layer
end
CoordinationEngine->>OPCUACoordinator : set_job_inactive()
OPCUACoordinator->>PLC : Write job_active=FALSE
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

**Section sources**
- [print.py](file://backend/app/api/print.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

### Layer Processing Example
A concrete example of how a single layer is processed through the system:

1. **API Request**: Frontend sends a request to start a multi-material job with specific CLI files for each drum
2. **Job Creation**: The MultilayerJobManager creates a job state object with layer data from all CLI files, automatically adding empty templates for any missing drums
3. **Job Start**: The CoordinationEngine starts the job, updating OPC UA variables to notify the PLC
4. **Layer Upload**: The engine uploads the current layer data to all three drums sequentially
5. **Ready Check**: The system waits for all drums to report they are ready for printing
6. **PLC Notification**: The OPCUACoordinator sets `recoater_ready_to_print` to TRUE, signaling the PLC to begin deposition
7. **Completion Wait**: The system monitors for completion, either through a timeout or PLC signal
8. **Completion Signal**: The OPCUACoordinator pulses the `recoater_layer_complete` variable to signal successful completion
9. **Next Layer**: The engine advances to the next layer and repeats the process

This coordinated workflow ensures that each step is completed successfully before proceeding to the next, maintaining synchronization between the multiple material sources and the PLC-controlled deposition process.

## Error Handling and Recovery
The system implements comprehensive error handling and recovery mechanisms to ensure reliability and fault tolerance.

### Error States and Transitions
The coordination engine manages several error conditions and provides recovery mechanisms:

``mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> UPLOADING : start_job()
UPLOADING --> ERROR : upload_failed
WAITING_FOR_READY --> ERROR : timeout
WAITING_FOR_COMPLETION --> ERROR : timeout/error
ERROR --> IDLE : clear_errors()
ERROR --> ERROR : max_retries_exceeded
state ERROR {
[*] --> BACKEND_ERROR
BACKEND_ERROR --> CLEARING : clear_errors()
CLEARING --> IDLE : success
CLEARING --> BACKEND_ERROR : failure
}
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L32)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L32)

### Error Recovery Mechanisms
The system implements several recovery strategies:

1. **Automatic Retry**: Failed operations are retried up to a configurable maximum (default: 3 times)
2. **Error Clearing**: The `clear_errors()` method resets the error state and allows job resumption
3. **OPC UA Error Flags**: Bidirectional error flags allow both backend and PLC to communicate error conditions
4. **Graceful Degradation**: The system can continue operation with fewer drums if one drum fails
5. **State Preservation**: Job state is preserved across restarts, allowing resumption from the current layer
6. **OPC UA Server Auto-Restart**: The OPC UA server automatically attempts to restart on failure with exponential backoff

When an error occurs, the system:
1. Sets the `backend_error` OPC UA variable to TRUE
2. Logs detailed error information
3. Stops the current job if critical
4. Waits for operator intervention or automatic recovery
5. Allows error clearing and job resumption via API

The OPC UA server manager includes enhanced error handling with auto-restart capabilities:
- On server failure, the system attempts to restart up to `max_restart_attempts` times
- A delay of `restart_delay` seconds occurs between restart attempts
- After successful restart, the server reinitializes all coordination variables
- The heartbeat task is restarted to maintain connection health

This comprehensive error handling ensures the system can recover from transient issues while providing clear feedback about persistent problems.

## Mock Testing Infrastructure
The system includes a comprehensive mock testing infrastructure that allows development and testing without requiring physical hardware. The mock_recoater_client package has been refactored into a dedicated package with modular components.

### Package Structure
The mock_recoater_client package is organized into the following modules:
- **mock_client**: Main MockRecoaterClient class with core functionality
- **mock_blade_controls**: Blade control methods
- **mock_leveler_controls**: Leveler control methods
- **mock_print_controls**: Print control methods
- **mock_file_management**: File management methods
- **mock_async_client**: Async wrapper methods for multi-material coordination
- **mock_drum_controls**: Drum control methods

The MockRecoaterClient class implements the same interface as the real RecoaterClient but returns mock data instead of making actual HTTP requests to hardware.

### Core Implementation
The MockRecoaterClient class uses a mixin pattern to combine functionality from multiple modules:

```python
class MockRecoaterClient(
    MockBladeControlMixin,
    MockLevelerControlMixin,
    MockPrintControlMixin,
    MockFileManagementMixin,
    MockAsyncClientMixin,
    MockDrumControlMixin
):
    """
    Mock client that simulates the Aerosint SPD Recoater hardware API.
    
    This class implements the same interface as RecoaterClient but returns
    mock data instead of making actual HTTP requests to hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the MockRecoaterClient.
        
        Args:
            base_url: The base URL (ignored in mock mode)
            timeout: Request timeout (ignored in mock mode)
        """
        self.base_url = base_url
        self.timeout = timeout
        self._start_time = time.time()
        self._state = {
            "status": "idle",
            "current_layer": 0,
            "total_layers": 100,
            "progress": 0.0,
            "temperature": 25.0,
            "errors": []
        }

        # Print job state management
        self._print_job_state = "ready"
        self._print_job_id = None
        self._print_job_start_time = None

        # Drum geometry storage for persistent mock data
        self._drum_geometries = {}  # {drum_id: {"file_data": bytes, "content_type": str}}

        # Drum state management for coordination engine
        self._drum_states = {}  # {drum_id: state_string}
        self._current_layers = {}  # {drum_id: current_layer_number}

        # Multi-material job state
        self._job_active = False

        logger.info(f"MockRecoaterClient initialized (development mode)")
```

**Section sources**
- [mock_client.py](file://backend/infrastructure/mock_recoater_client/mock_client.py#L26-L80)
- [__init__.py](file://backend/infrastructure/mock_recoater_client/__init__.py)

### Testing Integration
The mock client is integrated into the testing framework through dependency injection:

```python
@pytest.fixture
def mock_recoater_client(setup_test_dependencies):
    """
    Provide access to the mock recoater client for test customization.
    """
    return setup_test_dependencies
```

The mock client is used in various test scenarios to simulate different conditions:
- Hardware connectivity issues
- State transitions
- Error conditions
- Performance characteristics

This allows comprehensive testing of the backend services without requiring physical hardware, enabling faster development cycles and more reliable testing.

## Architecture Diagram
The overall architecture of the backend services shows how the components interact to provide a complete solution for multi-material recoater control:

``mermaid
graph TD
subgraph "Frontend"
UI[User Interface]
WS[WebSocket Client]
end
subgraph "Backend Services"
API[Print API]
CoordinationEngine[Coordination Engine]
JobManager[Multilayer Job Manager]
OPCUACoordinator[OPC UA Coordinator]
StatusPoller[Status Poller]
WebSocketManager[WebSocket Manager]
end
subgraph "External Systems"
OPCUAServer[OPC UA Server]
PLC[PLC]
Recoater[Recoater Hardware]
end
UI --> API
API --> CoordinationEngine
API --> JobManager
CoordinationEngine --> JobManager
CoordinationEngine --> OPCUACoordinator
OPCUACoordinator --> OPCUAServer
OPCUAServer --> PLC
OPCUACoordinator --> Recoater
StatusPoller --> OPCUAServer
StatusPoller --> Recoater
StatusPoller --> CoordinationEngine
CoordinationEngine --> WebSocketManager
JobManager --> WebSocketManager
StatusPoller --> WebSocketManager
WebSocketManager --> WS
style CoordinationEngine fill:#f9f,stroke:#333
style JobManager fill:#ff9,stroke:#333
style OPCUACoordinator fill:#9ff,stroke:#333
style StatusPoller fill:#9f9,stroke:#333
style WebSocketManager fill:#f99,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)

The architecture demonstrates a clean separation of concerns with well-defined interfaces between components. The CoordinationEngine serves as the central orchestrator, while specialized services handle specific responsibilities. This modular design allows for independent development, testing, and scaling of each component while maintaining overall system integrity.