# Configuration API

<cite>
**Referenced Files in This Document**   
- [configuration.py](file://backend/app/api/configuration.py)
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)
- [api.js](file://frontend/src/services/api.js)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Endpoints](#api-endpoints)
3. [Configuration Categories](#configuration-categories)
4. [Request and Response Schema](#request-and-response-schema)
5. [Validation Rules](#validation-rules)
6. [Error Handling](#error-handling)
7. [Frontend Integration](#frontend-integration)
8. [Configuration Persistence](#configuration-persistence)
9. [Usage Examples](#usage-examples)
10. [Sequence Diagram](#sequence-diagram)

## Introduction
The Configuration API provides endpoints for managing the recoater system's configuration parameters. It enables clients to retrieve current settings, update configuration values, and validate changes before persistence. The API serves as the central interface between the frontend user interface and backend system components, ensuring consistent configuration management across the application.

The configuration system supports various parameters related to build space dimensions, system resolution, drum gaps, and ejection matrix size. All operations are performed through RESTful endpoints with proper validation and error handling to maintain system integrity.

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L1-L118)

## API Endpoints

### GET /api/config
Retrieves the current recoater configuration.

- **HTTP Method**: GET
- **URL**: `/api/config`
- **Response**: 200 OK with configuration data in JSON format
- **Error Responses**:
  - 503 Service Unavailable: When unable to connect to the recoater
  - 502 Bad Gateway: When the recoater API returns an error
  - 500 Internal Server Error: For unexpected server errors

### PUT /api/config
Updates the recoater configuration with new values.

- **HTTP Method**: PUT
- **URL**: `/api/config`
- **Request Body**: JSON object containing configuration parameters
- **Response**: 200 OK with success message and applied configuration
- **Error Responses**:
  - 422 Unprocessable Entity: When request data fails validation
  - 503 Service Unavailable: When unable to connect to the recoater
  - 502 Bad Gateway: When the recoater API returns an error
  - 500 Internal Server Error: For unexpected server errors

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)

## Configuration Categories
The Configuration API manages several categories of system parameters:

### Build Space Configuration
Parameters defining the physical dimensions of the build area:
- **Build Space Diameter**: The diameter of the circular build area in millimeters
- **Build Space Dimensions**: Rectangular dimensions with length and width in millimeters

### System Configuration
General system parameters affecting operation:
- **Resolution**: The size of one pixel in micrometers (µm)
- **Ejection Matrix Size**: The number of points in the ejection matrix

### Drum Gap Configuration
Parameters related to drum spacing:
- **Gaps**: A list of gap distances between drums in millimeters

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L22-L41)

## Request and Response Schema
The API uses Pydantic models for request validation and response serialization.

### ConfigurationRequest
The request model for configuration updates:

```python
class ConfigurationRequest(BaseModel):
    build_space_diameter: Optional[float] = Field(None, ge=0, description="The diameter of the build space [mm]")
    build_space_dimensions: Optional[BuildSpaceDimensions] = Field(None, description="Build space dimensions")
    ejection_matrix_size: Optional[int] = Field(None, ge=0, description="The number of points in the ejection matrix")
    gaps: Optional[List[float]] = Field(None, description="The list of gaps between the drums [mm]")
    resolution: Optional[int] = Field(None, ge=0, description="The resolution of the recoater, the size of one pixel [µm]")
```

### ConfigurationResponse
The response model for configuration retrieval:

```python
class ConfigurationResponse(BaseModel):
    build_space_diameter: Optional[float] = Field(None, description="The diameter of the build space [mm]")
    build_space_dimensions: Optional[BuildSpaceDimensions] = Field(None, description="Build space dimensions")
    ejection_matrix_size: Optional[int] = Field(None, description="The number of points in the ejection matrix")
    gaps: Optional[List[float]] = Field(None, description="The list of gaps between the drums [mm]")
    resolution: Optional[int] = Field(None, description="The resolution of the recoater, the size of one pixel [µm]")
```

### BuildSpaceDimensions
Nested model for rectangular build space dimensions:

```python
class BuildSpaceDimensions(BaseModel):
    length: float = Field(..., ge=0, description="The length of the build space [mm]")
    width: float = Field(..., ge=0, description="The width of the build space [mm]")
```

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L22-L41)

## Validation Rules
The Configuration API enforces strict validation rules to ensure data integrity:

- **Numeric Values**: All numeric parameters must be greater than or equal to zero (ge=0)
- **Build Space Dimensions**: Both length and width are required when specifying dimensions
- **Optional Parameters**: All fields are optional, allowing partial updates
- **Data Types**: Proper type enforcement (float for measurements, int for counts)
- **Nested Validation**: BuildSpaceDimensions is validated as a complete unit

The validation is implemented using Pydantic's Field constraints, which automatically validate incoming data and generate appropriate error responses for invalid inputs.

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L22-L33)

## Error Handling
The API implements comprehensive error handling for various failure scenarios:

```mermaid
flowchart TD
Start([Request Received]) --> ValidateInput["Validate Request Data"]
ValidateInput --> InputValid{"Valid Data?"}
InputValid --> |No| Return422["Return 422 Unprocessable Entity"]
InputValid --> |Yes| ProcessRequest["Process Configuration Request"]
ProcessRequest --> ConnectionSuccess{"Connection Successful?"}
ConnectionSuccess --> |No| Return503["Return 503 Service Unavailable"]
ConnectionSuccess --> |Yes| APISuccess{"API Call Successful?"}
APISuccess --> |No| Return502["Return 502 Bad Gateway"]
APISuccess --> |Yes| Return200["Return 200 OK with Response"]
Return422 --> End([Response Sent])
Return503 --> End
Return502 --> End
Return200 --> End
```

**Diagram sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)

## Frontend Integration
The Configuration API is integrated with the frontend through the ConfigurationView.vue component and api.js service.

### ConfigurationView.vue
The Vue component provides a user interface for viewing and editing configuration settings:

- Displays current configuration values in input fields
- Supports adding and removing gap entries
- Shows loading and error states appropriately
- Provides save functionality with success feedback

### api.js Service
The centralized API service handles communication with the backend:

```javascript
/**
 * Get the current recoater configuration
 * @returns {Promise} Axios response promise with configuration data
 */
async getConfiguration() {
  const response = await apiClient.get('/config/')
  return response.data
}

/**
 * Set the recoater configuration
 * @param {Object} config - Configuration object
 * @returns {Promise} Axios response promise
 */
async setConfiguration(config) {
  const response = await apiClient.put('/config/', config)
  return response.data
}
```

**Section sources**
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue#L1-L480)
- [api.js](file://frontend/src/services/api.js#L1-L587)

## Configuration Persistence
Configuration changes are persisted through the backend services and integrated with the multilayer job management system.

The `multilayer_job_manager.py` service uses configuration data for job coordination and layer management. When configuration changes affect system parameters, they are validated and applied before being stored in the system state.

The persistence mechanism ensures that configuration changes are:
- Validated before application
- Applied atomically
- Available to all system components
- Maintained across system restarts

**Section sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L668)

## Usage Examples

### Fetch Current Configuration
```bash
curl -X GET "http://localhost:8000/api/v1/config/" \
     -H "Content-Type: application/json"
```

**Response (200 OK):**
```json
{
  "build_space_diameter": 250.0,
  "build_space_dimensions": {
    "length": 250.0,
    "width": 96.0
  },
  "ejection_matrix_size": 192,
  "gaps": [130.0, 130.0],
  "resolution": 500
}
```

### Update Configuration
```bash
curl -X PUT "http://localhost:8000/api/v1/config/" \
     -H "Content-Type: application/json" \
     -d '{
  "build_space_diameter": 300.0,
  "resolution": 400,
  "gaps": [120.0, 120.0, 120.0]
}'
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Configuration updated successfully",
  "config": {
    "build_space_diameter": 300.0,
    "resolution": 400,
    "gaps": [120.0, 120.0, 120.0]
  }
}
```

### Invalid Configuration Update
```bash
curl -X PUT "http://localhost:8000/api/v1/config/" \
     -H "Content-Type: application/json" \
     -d '{
  "build_space_diameter": -50.0
}'
```

**Response (422 Unprocessable Entity):**
```json
{
  "detail": [
    {
      "loc": ["body", "build_space_diameter"],
      "msg": "ensure this value is greater than or equal to 0",
      "type": "value_error.number.not_ge"
    }
  ]
}
```

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)

## Sequence Diagram
```mermaid
sequenceDiagram
participant Frontend as "Frontend (ConfigurationView.vue)"
participant ApiService as "API Service (api.js)"
participant Backend as "Backend (configuration.py)"
participant RecoaterClient as "Recoater Client"
Frontend->>ApiService : loadConfiguration()
ApiService->>Backend : GET /api/v1/config/
Backend->>RecoaterClient : get_config()
RecoaterClient-->>Backend : Configuration Data
Backend-->>ApiService : 200 OK + Data
ApiService-->>Frontend : Configuration Object
Frontend->>Frontend : Populate Form Fields
Frontend->>ApiService : saveConfiguration(config)
ApiService->>Backend : PUT /api/v1/config/ + Data
Backend->>Backend : Validate Request (Pydantic)
alt Valid Data
Backend->>RecoaterClient : set_config(config_dict)
RecoaterClient-->>Backend : Success Response
Backend-->>ApiService : 200 OK + Success
ApiService-->>Frontend : Success Response
Frontend->>Frontend : Show Success Message
else Invalid Data
Backend-->>ApiService : 422 Error
ApiService-->>Frontend : Error Response
Frontend->>Frontend : Show Error Message
end
```

**Diagram sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue#L1-L480)
- [api.js](file://frontend/src/services/api.js#L1-L587)

**Section sources**
- [configuration.py](file://backend/app/api/configuration.py#L75-L118)
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue#L1-L480)
- [api.js](file://frontend/src/services/api.js#L1-L587)