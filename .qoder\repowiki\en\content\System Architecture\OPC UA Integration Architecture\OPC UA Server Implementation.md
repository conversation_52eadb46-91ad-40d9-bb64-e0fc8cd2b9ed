# OPC UA Server Implementation

<cite>
**Referenced Files in This Document**   
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [test_opcua_infrastructure.py](file://backend/tests/test_opcua_infrastructure.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides a comprehensive analysis of the OPC UA server implementation in the APIRecoater_Ethernet project. The system enables industrial communication between a FastAPI backend and a TwinCAT PLC through OPC UA protocol, facilitating coordination in multi-material 3D printing processes. The implementation uses the FreeOpcUa (asyncua) library to expose backend state variables such as print job status, axis positions, and recoater controls to external SCADA systems. This documentation details the architecture, configuration, data model, and integration patterns while maintaining accessibility for users with varying technical backgrounds.

## Project Structure
The project follows a layered architecture with clear separation between configuration, services, API endpoints, and infrastructure components. The OPC UA functionality is primarily contained within the backend/app/services and backend/app/config directories, with integration into the main FastAPI application through dependency injection.

```mermaid
graph TD
subgraph "Backend"
A[main.py] --> B[dependencies.py]
B --> C[opcua_coordinator.py]
C --> D[opcua_server.py]
D --> E[opcua_config.py]
end
subgraph "Configuration"
E --> F[Environment Variables]
end
subgraph "Testing"
G[test_opcua_infrastructure.py]
end
A --> H[API Routers]
H --> I[print.py]
H --> J[status.py]
H --> K[axis.py]
C --> L[Web Interface]
D --> M[PLC Client]
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

**Section sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)

## Core Components
The OPC UA implementation consists of three core components working in concert: the server manager (low-level protocol handling), the coordinator (high-level business logic), and the configuration system (settings and variable definitions). These components enable bidirectional communication between the Python backend and industrial PLC systems using standardized OPC UA protocols.

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Architecture Overview
The architecture implements a layered approach to OPC UA communication, abstracting protocol complexity from business logic. The design follows industrial interoperability standards (OPC UA Part 6) while providing a clean interface for integration with web APIs.

```mermaid
graph TB
subgraph "Frontend"
UI[Web Interface]
end
subgraph "Backend Application"
API[FastAPI Server]
DEP[Dependency Manager]
WS[WebSocket Manager]
end
subgraph "OPC UA Layer"
COORD[OPCUACoordinator]
SERVER[OPCUAServerManager]
CONFIG[opcua_config]
end
subgraph "Industrial Network"
PLC[TwinCAT PLC]
SCADA[SCADA System]
end
UI --> API
API --> DEP
DEP --> COORD
COORD --> SERVER
SERVER --> CONFIG
SERVER < --> PLC
SERVER < --> SCADA
style COORD fill:#f9f,stroke:#333
style SERVER fill:#bbf,stroke:#333
style CONFIG fill:#dfd,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Detailed Component Analysis

### OPCUAServerManager Analysis
The OPCUAServerManager class provides low-level management of the OPC UA server instance, handling protocol-specific operations and server lifecycle management.

#### Class Diagram
```mermaid
classDiagram
class OPCUAServerManager {
-config : OPCUAServerConfig
-server : Optional[Server]
-namespace_idx : int
-variable_nodes : Dict[str, Any]
-_running : bool
-_restart_count : int
-_heartbeat_task : Optional[Task]
+start_server() bool
+stop_server() None
+write_variable(name, val) bool
+read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
-_create_coordination_variables() None
-_get_ua_data_type(data_type) ua.VariantType
-_heartbeat_loop() None
-_cleanup() None
-_handle_server_error(error) None
}
class OPCUAServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
OPCUAServerManager --> OPCUAServerConfig : "uses"
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L189-L519)
- [opcua_config.py](file://backend/app/config/opcua_config.py#L34-L78)

#### Server Startup Sequence
```mermaid
sequenceDiagram
participant App as Application
participant Coord as OPCUACoordinator
participant Server as OPCUAServerManager
participant UA as asyncua.Server
App->>Coord : connect()
Coord->>Server : start_server()
Server->>UA : Server()
Server->>UA : init()
Server->>UA : set_endpoint()
Server->>UA : set_server_name()
Server->>UA : register_namespace()
Server->>Server : _create_coordination_variables()
Server->>UA : start()
Server->>Server : _heartbeat_loop()
Server-->>Coord : True
Coord-->>App : True
Note over Server,UA : Server successfully started on opc.tcp : //0.0.0.0 : 4843
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L208-L250)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L237-L260)

### OPCUACoordinator Analysis
The OPCUACoordinator provides a high-level interface that abstracts the complexity of OPC UA protocol operations, offering business-oriented methods for job coordination and status management.

#### Class Diagram
```mermaid
classDiagram
class OPCUACoordinator {
-config : OPCUAServerConfig
-server_manager : OPCUAServerManager
-_connected : bool
-_monitoring_task : Optional[Task]
-_event_handlers : Dict[str, List]
+connect() bool
+disconnect() bool
+write_variable(name, val) bool
+read_variable(name) Any
+subscribe_to_changes(vars, handler) bool
+set_job_active(layers) bool
+set_job_inactive() bool
+update_layer_progress(num) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+is_connected() bool
+get_server_status() Dict
-_monitoring_loop() None
-_trigger_event_handlers(name, value) None
}
OPCUACoordinator --> OPCUAServerManager : "delegates to"
OPCUACoordinator --> OPCUAServerConfig : "uses"
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)
- [opcua_server.py](file://backend/app/services/opcua_server.py)

#### Job Activation Flow
```mermaid
sequenceDiagram
participant API as Print API
participant Coord as OPCUACoordinator
participant Server as OPCUAServerManager
participant PLC as TwinCAT PLC
API->>Coord : set_job_active(total_layers=150)
Coord->>Server : write_variable("job_active", True)
Coord->>Server : write_variable("total_layers", 150)
Coord->>Server : write_variable("current_layer", 0)
Server-->>Coord : Success
Coord-->>API : True
PLC->>Server : Read job_active
PLC->>Server : Read total_layers
PLC->>Server : Read current_layer
Note over Coord,Server : Three coordination variables updated atomically
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L358-L373)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L388-L408)

### Configuration System Analysis
The configuration system provides a flexible way to define OPC UA server settings and coordination variables, supporting both default values and environment variable overrides.

#### Data Model
```mermaid
erDiagram
OPCUAServerConfig {
string endpoint PK
string server_name
string namespace_uri
int namespace_idx
string security_policy
string security_mode
bool auto_restart
float restart_delay
int max_restart_attempts
}
CoordinationVariable {
string name PK
string node_id
string data_type
any initial_value
bool writable
string description
}
OPCUAServerConfig ||--o{ CoordinationVariable : "defines"
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L34-L232)

#### Configuration Flow
```mermaid
flowchart TD
Start([Application Start]) --> LoadEnv["Load Environment Variables"]
LoadEnv --> CreateConfig["Create OPCUAServerConfig"]
CreateConfig --> SetDefaults["Apply Default Values"]
SetDefaults --> Override["Override with Environment Variables"]
Override --> Validate["Validate Configuration"]
Validate --> CreateVars["Create COORDINATION_VARIABLES"]
CreateVars --> Expose["Expose via opcua_config"]
Expose --> End([Configuration Ready])
style Start fill:#f9f,stroke:#333
style End fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L134-L175)

## Dependency Analysis
The OPC UA components are integrated into the application through a dependency injection pattern, ensuring proper initialization order and resource management.

```mermaid
graph TD
main[main.py] --> dependencies[dependencies.py]
dependencies --> coordinator[opcua_coordinator.py]
coordinator --> server[opcua_server.py]
server --> config[opcua_config.py]
server --> asyncua[asyncua library]
coordinator --> logger[logging]
server --> logger
style main fill:#f96,stroke:#333
style dependencies fill:#69f,stroke:#333
style coordinator fill:#f9f,stroke:#333
style server fill:#bbf,stroke:#333
style config fill:#dfd,stroke:#333
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Performance Considerations
The OPC UA implementation includes several performance and reliability features designed for industrial environments:

- **Heartbeat Mechanism**: The `_heartbeat_loop()` method runs every 5 seconds to maintain server responsiveness and detect failures early.
- **Error Recovery**: Automatic server restart capability with configurable attempts (default: 3) and delay (default: 5 seconds).
- **Type Coercion**: Efficient value type conversion prevents BadTypeMismatch errors during variable writes.
- **Asynchronous Operations**: All methods use async/await pattern to prevent blocking the event loop.
- **Graceful Shutdown**: Proper cleanup of resources including heartbeat tasks and server instances.

The system is optimized for moderate-frequency data publishing (typically 1-10 Hz for industrial processes) rather than high-frequency sensor data. For higher frequency requirements, OPC UA subscriptions would need to be implemented in the monitoring loop.

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L488-L500)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L475-L486)

## Troubleshooting Guide
Common issues and their solutions for the OPC UA implementation:

### Server Startup Failures
**Symptom**: Server fails to start with "Failed to start OPC UA server" error
**Causes**:
- Port 4843 already in use
- Invalid security configuration
- Network interface binding issues

**Solution**: Check port availability and verify endpoint configuration in environment variables.

### Variable Access Errors
**Symptom**: "Variable not found" when reading/writing
**Causes**:
- Variable name mismatch
- Server not fully initialized
- Case sensitivity issues

**Solution**: Verify variable names against COORDINATION_VARIABLES list and ensure server is running.

### Connection Timeouts
**Symptom**: PLC cannot connect to OPC UA server
**Causes**:
- Firewall blocking port 4843
- Incorrect IP address in endpoint URL
- Security policy mismatch

**Solution**: Use `opc.tcp://0.0.0.0:4843` for development and verify network connectivity.

### Type Mismatch Errors
**Symptom**: BadTypeMismatch during variable writes
**Causes**:
- Sending integer when Boolean expected
- Null values for non-nullable types

**Solution**: Ensure proper type coercion using the built-in type mapping system.

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L409-L450)
- [test_opcua_infrastructure.py](file://backend/tests/test_opcua_infrastructure.py#L108-L128)

## Conclusion
The OPC UA server implementation in APIRecoater_Ethernet provides a robust, standards-compliant interface for industrial communication between a Python backend and PLC systems. By following OPC UA Part 6 standards, the implementation ensures interoperability with SCADA systems and industrial automation equipment. The layered architecture separates concerns effectively, with the OPCUAServerManager handling protocol details, the OPCUACoordinator providing business logic, and the configuration system enabling flexibility. The integration with FastAPI through dependency injection creates a cohesive system where REST API endpoints can update OPC UA variables that are immediately available to PLC clients. This design supports reliable, real-time coordination of multi-material 3D printing processes in industrial environments.