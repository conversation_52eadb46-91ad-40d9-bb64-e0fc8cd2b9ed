# Job Coordination Engine

<cite>
**Referenced Files in This Document**   
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [print.py](file://backend/app/api/print.py)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The Job Coordination Engine is the central orchestrator for multilayer print jobs in the APIRecoater_Ethernet system. It manages the complete lifecycle of multi-material printing operations, coordinating between configuration, printing, and recoating phases across three drums. The engine integrates with the multilayer job manager for job queue management and persistence, communicates with OPC UA clients for hardware-level command execution, and interfaces with REST APIs for job initiation and status reporting. This documentation provides a comprehensive analysis of the coordination engine's architecture, state management, error handling, and integration points.

## Project Structure
The project follows a layered architecture with clear separation between frontend and backend components. The backend is organized into API endpoints, configuration, models, services, and utilities, while the frontend uses a component-based Vue.js architecture with Pinia for state management.

```mermaid
graph TD
subgraph "Frontend"
UI[Vue.js Components]
Store[Pinia Stores]
Services[API Services]
end
subgraph "Backend"
API[FastAPI Endpoints]
Services[Business Logic Services]
Models[Data Models]
Config[Configuration]
end
UI --> Services
Services --> API
API --> Services
Services --> Models
Services --> Config
Services --> OPCUA[OPC UA Coordinator]
OPCUA --> PLC[TwinCAT PLC]
```

**Diagram sources**
- [project_structure](file://README.md#L1-L50)

## Core Components
The Job Coordination Engine consists of several core components that work together to manage multilayer print jobs. The coordination_engine.py module serves as the central orchestrator, while multilayer_job_manager.py handles job state management, and opcua_coordinator.py manages hardware communication.

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L50)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L50)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L50)

## Architecture Overview
The Job Coordination Engine implements a state machine design that manages the progression of multilayer print jobs through various phases. The architecture follows a layered approach with clear separation between business logic, hardware communication, and API interfaces.

```mermaid
graph TB
subgraph "Frontend"
F[printJobStore.js]
end
subgraph "Backend API"
A[print.py]
end
subgraph "Coordination Engine"
C[coordination_engine.py]
M[multilayer_job_manager.py]
O[opcua_coordinator.py]
end
subgraph "Hardware"
H[OPC UA Server]
P[PLC]
end
F --> A
A --> M
M --> C
C --> O
O --> H
H --> P
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L50)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L50)
- [print.py](file://backend/app/api/print.py#L1-L50)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L50)

## Detailed Component Analysis

### Coordination Engine Analysis
The MultiMaterialCoordinationEngine class is the central component that orchestrates the entire multilayer print job process. It manages the state transitions between different phases of the printing cycle and coordinates activities across multiple drums.

#### Class Diagram
```mermaid
classDiagram
class CoordinationState {
<<enumeration>>
IDLE
UPLOADING
WAITING_FOR_READY
PRINTING
WAITING_FOR_COMPLETION
ERROR
COMPLETE
}
class MultiMaterialCoordinationEngine {
-recoater_client : RecoaterClient
-job_manager : MultiMaterialJobManager
-state : CoordinationState
-current_job : MultiMaterialJobState
-drum_upload_delay : float
-status_poll_interval : float
-ready_timeout : float
-completion_timeout : float
-error_count : int
-max_retries : int
+start_multimaterial_job(job_state) : bool
+stop_job() : bool
-_coordination_loop() : None
-_process_layer_cycle_all_drums() : bool
-_upload_layer_to_all_drums() : bool
-_wait_for_all_drums_ready() : bool
-_wait_for_layer_completion() : bool
-_complete_job() : None
-_set_error_state(error_msg) : None
}
class MultiMaterialJobManager {
+create_job(file_ids) : MultiMaterialJobState
+start_job() : bool
+cancel_job() : bool
+get_job_status() : Dict
+get_drum_status(drum_id) : Dict
+add_cli_file(file_id, parsed_file) : None
+upload_layer_to_all_drums(layer_num) : bool
+wait_for_all_drums_ready() : bool
+clear_error_flags() : bool
}
class OPCUACoordinator {
+connect() : bool
+disconnect() : bool
+set_job_active(total_layers) : bool
+set_job_inactive() : bool
+update_layer_progress(current_layer) : bool
+set_recoater_ready_to_print(ready) : bool
+set_recoater_layer_complete(complete) : bool
+set_backend_error(error) : bool
+set_plc_error(error) : bool
+clear_error_flags() : bool
+write_variable(name, value) : bool
+read_variable(name) : Any
}
MultiMaterialCoordinationEngine --> MultiMaterialJobManager : "uses"
MultiMaterialCoordinationEngine --> OPCUACoordinator : "uses"
MultiMaterialCoordinationEngine --> CoordinationState : "state"
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L50-L100)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L50-L100)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L50-L100)

#### State Machine Diagram
```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Uploading : "start_multimaterial_job()"
Uploading --> WaitingForReady : "layers uploaded"
WaitingForReady --> Printing : "all drums ready"
Printing --> WaitingForCompletion : "recoater_ready_to_print=True"
WaitingForCompletion --> Uploading : "layer_complete=True"
WaitingForCompletion --> Error : "timeout"
Error --> Idle : "stop_job()"
Uploading --> Error : "upload failed"
WaitingForReady --> Error : "timeout"
Idle --> Complete : "job completed"
Complete --> Idle : "reset"
state Uploading {
[*] --> UploadLayerToDrum0
UploadLayerToDrum0 --> UploadLayerToDrum1 : "delay=2s"
UploadLayerToDrum1 --> UploadLayerToDrum2 : "delay=2s"
UploadLayerToDrum2 --> Uploaded
}
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L150-L200)

#### Sequence Diagram for Complete Multilayer Cycle
```mermaid
sequenceDiagram
participant Frontend
participant PrintAPI
participant JobManager
participant CoordinationEngine
participant OPCUACoordinator
participant PLC
Frontend->>PrintAPI : start_multimaterial_job()
PrintAPI->>JobManager : create_job()
JobManager->>JobManager : validate files
JobManager->>JobManager : create job state
PrintAPI->>JobManager : start_job()
JobManager->>CoordinationEngine : start_multimaterial_job()
CoordinationEngine->>OPCUACoordinator : set_job_active()
CoordinationEngine->>CoordinationEngine : _coordination_loop()
loop For each layer
CoordinationEngine->>CoordinationEngine : _process_layer_cycle_all_drums()
CoordinationEngine->>JobManager : upload_layer_to_all_drums()
JobManager->>JobManager : upload to drum 0
JobManager->>JobManager : delay 2s
JobManager->>JobManager : upload to drum 1
JobManager->>JobManager : delay 2s
JobManager->>JobManager : upload to drum 2
CoordinationEngine->>JobManager : wait_for_all_drums_ready()
JobManager->>PLC : get drum status
PLC-->>JobManager : ready status
CoordinationEngine->>OPCUACoordinator : set_recoater_ready_to_print(True)
OPCUACoordinator->>PLC : write variable
PLC->>PLC : execute printing
PLC->>OPCUACoordinator : set_recoater_layer_complete(True)
OPCUACoordinator->>CoordinationEngine : event
CoordinationEngine->>OPCUACoordinator : update_layer_progress()
end
CoordinationEngine->>OPCUACoordinator : set_job_inactive()
CoordinationEngine->>CoordinationEngine : state=COMPLETE
CoordinationEngine-->>JobManager : job completed
JobManager-->>PrintAPI : success
PrintAPI-->>Frontend : job completed
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L200-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L200-L400)
- [print.py](file://backend/app/api/print.py#L800-L900)

### Multilayer Job Manager Analysis
The MultiMaterialJobManager handles job state management, persistence, and coordination with the coordination engine. It manages the lifecycle of multilayer jobs and provides an interface for job creation, status reporting, and error handling.

**Section sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L50)

#### Key Methods Analysis
```mermaid
flowchart TD
Start([create_job]) --> ValidateInput["Validate file_ids mapping"]
ValidateInput --> InputValid{"Valid drum IDs?"}
InputValid --> |No| ReturnError["Raise HTTPException"]
InputValid --> |Yes| CreateJobState["Create MultiMaterialJobState"]
CreateJobState --> AddToCache["Add to cli_cache"]
AddToCache --> ReturnState["Return job state"]
Start2([start_job]) --> CheckCurrentJob["Check current_job exists"]
CheckCurrentJob --> JobExists{"Job exists?"}
JobExists --> |No| ReturnError2["Raise MultiMaterialJobError"]
JobExists --> |Yes| UpdateStatus["Set job status to ACTIVE"]
UpdateStatus --> UpdateOPCUA["Update OPC UA variables"]
UpdateOPCUA --> ReturnSuccess["Return True"]
Start3([cancel_job]) --> CheckActiveJob["Check current_job exists"]
CheckActiveJob --> JobExists2{"Job exists?"}
JobExists2 --> |No| ReturnTrue["Return True"]
JobExists2 --> |Yes| SetInactive["Set is_active=False"]
SetInactive --> UpdateStatus2["Set status=CANCELLED"]
UpdateStatus2 --> UpdateOPCUA2["Update OPC UA variables"]
UpdateOPCUA2 --> ReturnSuccess2["Return True"]
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L100-L300)

### API Integration Analysis
The print.py module provides REST API endpoints that serve as the interface between the frontend and the coordination engine. These endpoints handle job initiation, status reporting, and control operations.

**Section sources**
- [print.py](file://backend/app/api/print.py#L1-L50)

#### API Workflow Sequence
```mermaid
sequenceDiagram
participant Frontend
participant printJobStore
participant printAPI
participant JobManager
participant CoordinationEngine
Frontend->>printJobStore : startMultiMaterialJob()
printJobStore->>printAPI : POST /cli/start-multimaterial-job
printAPI->>JobManager : create_job(file_ids)
JobManager->>JobManager : validate and create job
printAPI->>JobManager : start_job()
JobManager->>CoordinationEngine : start_multimaterial_job()
CoordinationEngine->>CoordinationEngine : begin coordination loop
CoordinationEngine-->>JobManager : success
JobManager-->>printAPI : success
printAPI-->>printJobStore : success
printJobStore->>printJobStore : update job state
printJobStore-->>Frontend : job started
loop Periodic Status Updates
Frontend->>printJobStore : fetchJobStatus()
printJobStore->>printAPI : GET /multimaterial-job/status
printAPI->>JobManager : get_job_status()
JobManager-->>printAPI : status data
printAPI-->>printJobStore : status data
printJobStore->>printJobStore : update job status
printJobStore-->>Frontend : updated status
end
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py#L800-L950)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L200-L300)

### Frontend State Management Analysis
The printJobStore.js module manages the frontend state for multilayer print jobs, providing a reactive interface for job status, error handling, and user interactions.

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L50)

#### State Management Flow
```mermaid
flowchart TD
A[User Uploads Files] --> B[setFileUploaded]
B --> C[Files stored in uploadedFiles]
C --> D[hasMinimumFiles computed]
D --> E{At least 2 files?}
E --> |Yes| F[canStartJob = true]
E --> |No| G[canStartJob = false]
H[User Clicks Start Job] --> I[canStartJob]
I --> J{canStartJob?}
J --> |Yes| K[startMultiMaterialJob()]
J --> |No| L[Show error]
K --> M[API call to start job]
M --> N{Success?}
N --> |Yes| O[Update job state]
N --> |No| P[Show error]
O --> Q[Job in progress]
R[Error Occurs] --> S[setErrorFlags]
S --> T[showCriticalModal = true]
T --> U[User can clear errors]
U --> V[clearErrorFlagsAPI]
V --> W[API call to clear errors]
```

**Diagram sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L50-L200)

## Dependency Analysis
The Job Coordination Engine has a well-defined dependency structure that ensures loose coupling between components while maintaining clear integration points.

```mermaid
graph TD
A[Frontend] --> B[print.py]
B --> C[multilayer_job_manager.py]
C --> D[coordination_engine.py]
D --> E[opcua_coordinator.py]
E --> F[OPC UA Server]
F --> G[PLC]
C --> H[recoater_client.py]
D --> H
C --> I[cli_parser.py]
B --> I
E --> J[opcua_server.py]
J --> K[asyncua.Server]
style A fill:#f9f,stroke:#333
style G fill:#f96,stroke:#333
style D fill:#bbf,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L50)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L50)
- [print.py](file://backend/app/api/print.py#L1-L50)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L50)

## Performance Considerations
The Job Coordination Engine implements several performance optimizations to ensure reliable operation during multilayer print jobs:

1. **Asynchronous Operations**: All I/O operations are performed asynchronously using asyncio, preventing blocking of the main thread during hardware communication and file operations.

2. **Connection Management**: The OPCUA coordinator maintains a persistent connection to the OPC UA server, reducing connection overhead during job execution.

3. **Caching**: CLI files are cached in memory after parsing, eliminating the need to re-parse files for each layer operation.

4. **Batch Processing**: Layer uploads are coordinated with a 2-second delay between drums to prevent server overload while maintaining efficient throughput.

5. **Error Handling**: The engine implements retry mechanisms and error recovery procedures to handle transient failures without requiring job restart.

6. **Resource Management**: Temporary files are created for verification purposes but are cleaned up after successful operations to prevent disk space exhaustion.

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L50)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L50)

## Troubleshooting Guide
This section addresses common issues and error scenarios in the Job Coordination Engine and provides guidance for diagnosis and resolution.

### Common Error Scenarios
```mermaid
flowchart TD
A[Job Fails to Start] --> B{Check prerequisites}
B --> C[At least 2 files uploaded?]
C --> |No| D[Upload additional files]
C --> |Yes| E[Check drum status]
E --> F[All drums ready?]
F --> |No| G[Check hardware connection]
F --> |Yes| H[Check OPC UA connection]
I[Layer Upload Fails] --> J{Identify failed drum}
J --> K[Drum 0, 1, or 2?]
K --> L[Check network connection]
L --> M[Test individual drum]
M --> N[Replace faulty component]
O[Job Stuck in Waiting State] --> P{Check timeout values}
P --> Q[ready_timeout exceeded?]
Q --> |Yes| R[Check drum readiness]
Q --> |No| S[Check polling interval]
T[Error Flags Not Clearing] --> U{Check error source}
U --> V[Backend error?]
V --> |Yes| W[Check backend logs]
V --> |No| X[PLC error?]
X --> |Yes| Y[Check PLC status]
```

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L300-L400)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L400-L500)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L400-L500)

### Error Recovery Procedures
1. **Clear Error Flags**: Use the clear_error_flags endpoint to reset error states after resolving the underlying issue.

2. **Job Cancellation**: Cancel the current job using the cancel_multimaterial_job endpoint if recovery is not possible.

3. **Connection Reset**: Restart the OPC UA server and coordinator if communication issues persist.

4. **File Re-upload**: Re-upload CLI files if parsing or upload errors occur.

5. **System Restart**: As a last resort, restart the entire system to clear any persistent state issues.

## Conclusion
The Job Coordination Engine in APIRecoater_Ethernet provides a robust and reliable system for managing multilayer print jobs across multiple drums. Its architecture follows sound software engineering principles with clear separation of concerns, asynchronous operation, and comprehensive error handling. The engine successfully coordinates between configuration, printing, and recoating phases through a well-defined state machine, ensuring atomic operations and transactional integrity throughout the job lifecycle. Integration with the REST API and frontend store provides a seamless user experience, while OPC UA communication enables precise control of hardware components. The system's design supports error recovery, job pausing/resumption, and synchronization with external systems, making it suitable for industrial applications requiring high reliability and precision.