# Coordination Engine

<cite>
**Referenced Files in This Document**   
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [print.py](file://backend/app/api/print.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The Coordination Engine is the central orchestrator for multi-material print jobs in the APIRecoater_Ethernet system. It manages the complete workflow for 3-drum recoater operations, including layer leveling, powder spreading, and drum control. This document provides a comprehensive analysis of its architecture, functionality, and integration points, explaining how it sequences operations during print jobs, handles state management, and coordinates between various system components.

## Project Structure
The project follows a layered architecture with clear separation between API endpoints, business logic, and hardware integration layers. The coordination engine resides in the services layer and acts as the central workflow orchestrator.

```mermaid
graph TD
subgraph "Frontend"
UI[Vue.js UI]
WS[WebSocket]
end
subgraph "Backend"
API[FastAPI Endpoints]
Services[Services Layer]
Models[Data Models]
end
subgraph "Hardware Integration"
OPCUA[OPC UA Coordinator]
Recoater[Recoater Client]
PLC[PLC System]
end
UI --> WS
UI --> API
API --> Services
Services --> Models
Services --> OPCUA
Services --> Recoater
OPCUA --> PLC
Recoater --> PLC
style Services fill:#f9f,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [print.py](file://backend/app/api/print.py)

## Core Components
The Coordination Engine integrates several core components to manage multi-material print jobs:

- **MultiMaterialJobState**: Manages job state with layer tracking and drum coordination
- **RecoaterClient**: Handles communication with the recoater hardware API
- **OPCUACoordinator**: Interfaces with OPC UA server for PLC communication
- **WebSocketManager**: Provides real-time status updates to the frontend
- **MultilayerJobManager**: Manages job lifecycle and state persistence

These components work together to ensure reliable and synchronized operation of the 3-drum recoater system.

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L474)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L111)

## Architecture Overview
The Coordination Engine implements a state machine architecture that sequences recoater operations through defined states. It receives commands from API endpoints, validates job state through the MultilayerJobManager, and delegates device-level actions to the OPCUACoordinator.

```mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> UPLOADING : start_multimaterial_job()
UPLOADING --> WAITING_FOR_READY : layer uploaded
WAITING_FOR_READY --> PRINTING : all drums ready
PRINTING --> WAITING_FOR_COMPLETION : ready_to_print set
WAITING_FOR_COMPLETION --> UPLOADING : layer_complete
WAITING_FOR_COMPLETION --> ERROR : timeout/error
UPLOADING --> ERROR : upload failure
WAITING_FOR_READY --> ERROR : timeout
ERROR --> IDLE : clear_errors()
WAITING_FOR_COMPLETION --> COMPLETE : last layer
COMPLETE --> IDLE : job complete
state CoordinationState {
IDLE
UPLOADING
WAITING_FOR_READY
PRINTING
WAITING_FOR_COMPLETION
ERROR
COMPLETE
}
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L35)

## Detailed Component Analysis

### Coordination Engine Analysis
The MultiMaterialCoordinationEngine class is the central component that orchestrates all recoater operations. It implements a state machine that sequences operations through the complete print job lifecycle.

#### Class Diagram
```mermaid
classDiagram
class MultiMaterialCoordinationEngine {
-recoater_client : RecoaterClient
-job_manager : MultiMaterialJobManager
-state : CoordinationState
-current_job : MultiMaterialJobState
-drum_upload_delay : float
-status_poll_interval : float
-ready_timeout : float
-completion_timeout : float
-error_count : int
-max_retries : int
+start_multimaterial_job(job_state) : bool
+stop_job() : bool
+clear_errors() : bool
+get_coordination_status() : Dict[str, Any]
-_coordination_loop() : None
-_process_layer_cycle_all_drums() : bool
-_upload_layer_to_all_drums() : bool
-_wait_for_all_drums_ready() : bool
-_wait_for_layer_completion() : bool
-_complete_job() : None
-_set_error_state(error_message) : None
}
class CoordinationState {
IDLE
UPLOADING
WAITING_FOR_READY
PRINTING
WAITING_FOR_COMPLETION
ERROR
COMPLETE
}
class CoordinationError {
+__init__(message)
}
MultiMaterialCoordinationEngine --> RecoaterClient : "uses"
MultiMaterialCoordinationEngine --> MultiMaterialJobManager : "uses"
MultiMaterialCoordinationEngine --> MultiMaterialJobState : "manages"
MultiMaterialCoordinationEngine --> CoordinationState : "state"
MultiMaterialCoordinationEngine --> CoordinationError : "throws"
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L45-L474)

#### State Machine Logic
The coordination engine implements a state machine with the following states:

- **IDLE**: Waiting for job start
- **UPLOADING**: Uploading layer data to drums
- **WAITING_FOR_READY**: Waiting for all drums to be ready
- **PRINTING**: Signal sent to PLC, waiting for deposition
- **WAITING_FOR_COMPLETION**: Waiting for layer completion signal
- **ERROR**: Error state requiring operator intervention
- **COMPLETE**: Job completed successfully

The engine transitions between these states based on hardware responses and timeout conditions.

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L35)

### Job State Management
The coordination engine relies on the MultiMaterialJobState class to maintain job state throughout the print process.

#### Job State Data Structure
```mermaid
classDiagram
class MultiMaterialJobState {
+job_id : str
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+remaining_layers : Dict[int, List[LayerData]]
+header_lines : Dict[int, List[str]]
+is_active : bool
+status : JobStatus
+start_time : Optional[float]
+last_layer_time : Optional[float]
+estimated_completion : Optional[float]
+waiting_for_print_start : bool
+waiting_for_layer_complete : bool
+error_message : str
+retry_count : int
+drums : Dict[int, DrumState]
+get_progress_percentage() : float
}
class LayerData {
+layer_number : int
+cli_data : bytes
+is_empty : bool
+upload_time : Optional[float]
+completion_time : Optional[float]
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset() : void
}
class JobStatus {
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
MultiMaterialJobState --> LayerData : "contains"
MultiMaterialJobState --> DrumState : "contains"
MultiMaterialJobState --> JobStatus : "status"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L111)

### API Integration
The coordination engine is triggered by API endpoints that handle multi-material job operations.

#### API Workflow Sequence
```mermaid
sequenceDiagram
participant Frontend
participant PrintAPI
participant JobManager
participant CoordinationEngine
participant OPCUACoordinator
participant RecoaterClient
Frontend->>PrintAPI : POST /print/cli/start-multimaterial-job
PrintAPI->>JobManager : create_job(file_ids)
JobManager-->>PrintAPI : job_state
PrintAPI->>JobManager : start_job()
JobManager->>CoordinationEngine : start_multimaterial_job(job_state)
CoordinationEngine->>OPCUACoordinator : set_job_active(total_layers)
CoordinationEngine->>OPCUACoordinator : clear_error_flags()
CoordinationEngine->>CoordinationEngine : _coordination_loop()
loop For each layer
CoordinationEngine->>CoordinationEngine : _upload_layer_to_all_drums()
CoordinationEngine->>RecoaterClient : upload_cli_data(drum_id, cli_data)
CoordinationEngine->>CoordinationEngine : _wait_for_all_drums_ready()
CoordinationEngine->>OPCUACoordinator : set_recoater_ready_to_print(True)
CoordinationEngine->>CoordinationEngine : _wait_for_layer_completion()
CoordinationEngine->>OPCUACoordinator : set_recoater_layer_complete(True)
CoordinationEngine->>OPCUACoordinator : update_layer_progress(current_layer)
end
CoordinationEngine->>OPCUACoordinator : set_job_inactive()
```

**Diagram sources**
- [print.py](file://backend/app/api/print.py#L1-L1012)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L474)

### Threading and Synchronization
The coordination engine uses asyncio for non-blocking operations and proper synchronization to prevent race conditions.

#### Threading Model
```mermaid
flowchart TD
MainThread["Main Thread\n(FastAPI Event Loop)"]
CoordinationTask["Coordination Loop\n(asyncio.Task)"]
StatusPoller["Status Poller\n(asyncio.Task)"]
WebSocketHandler["WebSocket Handler\n(asyncio.Task)"]
MainThread --> CoordinationTask
MainThread --> StatusPoller
MainThread --> WebSocketHandler
CoordinationTask --> |Synchronized Access| JobState["Job State\n(MultiMaterialJobState)"]
StatusPoller --> |Read Only| JobState
WebSocketHandler --> |Read Only| JobState
style CoordinationTask fill:#f96,stroke:#333
style JobState fill:#bbf,stroke:#333,color:#fff
note right of CoordinationTask
Single coordination task
ensures exclusive access
to job state
end
note right of JobState
Shared state protected
by asyncio synchronization
end
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L474)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

## Dependency Analysis
The coordination engine has well-defined dependencies on other components, ensuring loose coupling and maintainability.

```mermaid
graph TD
CoordinationEngine[MultiMaterialCoordinationEngine]
RecoaterClient[RecoaterClient]
JobManager[MultiMaterialJobManager]
OPCUACoordinator[OPCUACoordinator]
WebSocketManager[WebSocketConnectionManager]
CoordinationEngine --> RecoaterClient : "delegates hardware actions"
CoordinationEngine --> JobManager : "retrieves job state"
CoordinationEngine --> OPCUACoordinator : "controls PLC signals"
JobManager --> CoordinationEngine : "triggers coordination"
WebSocketManager --> CoordinationEngine : "status updates"
style CoordinationEngine fill:#f9f,stroke:#333
style RecoaterClient fill:#9f9,stroke:#333
style JobManager fill:#9f9,stroke:#333
style OPCUACoordinator fill:#9f9,stroke:#333
style WebSocketManager fill:#9f9,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

## Performance Considerations
The coordination engine is designed with performance and reliability in mind, implementing several optimization strategies:

- **Asynchronous Operations**: Uses asyncio for non-blocking hardware communication
- **Batch Processing**: Processes all drums in sequence with minimal delays
- **Efficient Polling**: Uses configurable poll intervals to balance responsiveness and resource usage
- **Error Resilience**: Implements retry logic and timeout handling for robust operation

The engine is optimized for real-time coordination of multi-material print jobs, ensuring timely execution of operations while maintaining system stability.

## Troubleshooting Guide
This section addresses common issues encountered with the coordination engine and provides solutions.

### Command Timeouts
**Issue**: Operations timeout waiting for hardware response
**Solution**: 
- Check network connectivity to recoater hardware
- Verify hardware power and operational status
- Adjust timeout values in coordination engine configuration
- Review hardware logs for error conditions

### Partial Execution Recovery
**Issue**: Job fails after partial completion
**Solution**:
- Use the clear_errors endpoint to reset error state
- Check job state to determine recovery point
- Resume from current layer or restart job as appropriate
- Verify drum states before resuming

### Conflict Resolution
**Issue**: Concurrent requests causing state conflicts
**Solution**:
- The coordination engine uses a state machine to prevent invalid transitions
- Only one active job is allowed at a time
- API endpoints validate state before accepting commands
- Use the stop_job method to terminate current operations

### Best Practices for Extension
When extending the engine with new operation types:
1. Add new states to the CoordinationState enum
2. Implement new methods following existing patterns
3. Update the coordination loop to handle new states
4. Add appropriate error handling and logging
5. Update OPC UA variable mappings as needed

For integrating custom hardware steps:
1. Create new methods in the OPCUACoordinator
2. Add corresponding methods in the coordination engine
3. Ensure proper synchronization with existing operations
4. Implement timeout handling for new steps
5. Add comprehensive logging for debugging

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L1-L474)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L200)

## Conclusion
The Coordination Engine serves as the central orchestrator for multi-material print jobs in the APIRecoater_Ethernet system. It effectively sequences recoater operations, manages job state, and coordinates between various system components. The engine's state machine architecture ensures reliable operation, while its asynchronous design enables responsive performance. Through integration with the OPC UA coordinator and recoater client, it provides a robust solution for 3-drum recoater control, handling complex workflows with proper error management and recovery capabilities.