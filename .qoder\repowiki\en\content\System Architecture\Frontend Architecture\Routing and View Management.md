# Routing and View Management

<cite>
**Referenced Files in This Document**   
- [StatusView.vue](file://frontend/src/views/StatusView.vue)
- [PrintView.vue](file://frontend/src/views/PrintView.vue)
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [AxisView.vue](file://frontend/src/views/AxisView.vue)
- [index.js](file://frontend/src/router/index.js)
- [status.js](file://frontend/src/stores/status.js)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [PrintView.test.js](file://frontend/tests/PrintView.test.js)
- [StatusView.test.js](file://frontend/tests/StatusView.test.js)
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Route Definitions and Navigation Flow](#route-definitions-and-navigation-flow)
3. [View Components and Application Screens](#view-components-and-application-screens)
4. [Shared State Management via Vuex Stores](#shared-state-management-via-vuex-stores)
5. [Navigation Guards and Page-aware Data Subscriptions](#navigation-guards-and-page-aware-data-subscriptions)
6. [Programmatic Navigation and Route-based Data Loading](#programmatic-navigation-and-route-based-data-loading)
7. [Lazy Loading and Performance Optimization](#lazy-loading-and-performance-optimization)
8. [Error Handling and Testing Practices](#error-handling-and-testing-practices)
9. [Conclusion](#conclusion)

## Introduction
This document provides a comprehensive analysis of the Vue Router implementation in the APIRecoater_Ethernet application. It details the route definitions, view components, shared state management, navigation flow, and testing practices used in the system. The routing system enables navigation between different functional areas of the recoater control interface, including status monitoring, axis control, recoater hardware management, print job control, and system configuration.

**Section sources**
- [index.js](file://frontend/src/router/index.js#L1-L57)
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L1-L319)

## Route Definitions and Navigation Flow

The Vue Router configuration defines five primary routes that map URL paths to specific view components. The routing system uses HTML5 history mode for clean URLs without hash fragments.

```mermaid
graph TD
A[Root /] --> B[StatusView]
C[/axis] --> D[AxisView]
E[/recoater] --> F[RecoaterView]
G[/print] --> H[PrintView]
I[/config] --> J[ConfigurationView]
```

**Diagram sources**
- [index.js](file://frontend/src/router/index.js#L10-L30)

The route definitions include:

- **Root route (`/`)**: Maps to the StatusView component, serving as the default landing page
- **`/axis`**: Lazy-loads the AxisView component for axis control functionality
- **`/recoater`**: Lazy-loads the RecoaterView component for comprehensive hardware control
- **`/print`**: Lazy-loads the PrintView component for print job management
- **`/config`**: Lazy-loads the ConfigurationView component for system configuration

Each route is assigned a unique name that corresponds to the view it represents, enabling named route navigation and facilitating the page-aware data subscription system.

**Section sources**
- [index.js](file://frontend/src/router/index.js#L10-L30)

## View Components and Application Screens

### StatusView
The StatusView component serves as the default landing page, providing an overview of the system's connection status, recoater status, and error information. It displays three main status cards: Connection Status, System Information, and Error Information. The component includes manual refresh functionality and server control buttons for restarting or shutting down the recoater server.

```mermaid
classDiagram
class StatusView {
+refreshStatus()
+restartServer()
+shutdownServer()
+formatTime(date)
}
```

**Diagram sources**
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L1-L319)

### PrintView
The PrintView component provides comprehensive print job management functionality, including layer parameter configuration, layer preview, file management, CLI file handling, and print job controls. It integrates multiple subcomponents such as FileUploadColumn, Legend, JobProgressDisplay, and ErrorDisplayPanel to provide a complete print control interface.

**Section sources**
- [PrintView.vue](file://frontend/src/views/PrintView.vue#L1-L1463)

### ConfigurationView
The ConfigurationView component allows users to configure system parameters including build space dimensions, resolution, ejection matrix size, and drum gap settings. It supports dynamic addition and removal of gap configurations and provides form validation for input fields.

```mermaid
classDiagram
class ConfigurationView {
+loadConfiguration()
+saveConfiguration()
+addGap()
+removeGap()
}
```

**Diagram sources**
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue#L1-L480)

### RecoaterView
The RecoaterView component serves as a dashboard for monitoring and controlling all recoater hardware components. It displays drum controls, hopper controls (scraping blades), and leveler control in a grid layout. The component uses subcomponents like DrumControl, HopperControl, and LevelerControl to manage individual hardware elements.

**Section sources**
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue#L1-L350)

### AxisView
The AxisView component provides control over X and Z axes, including movement controls and gripper functionality. It includes a hardware limitation notice indicating that axis control endpoints are not available in the current hardware API specification. The interface is disabled for production use but remains available for development testing with mock data.

**Section sources**
- [AxisView.vue](file://frontend/src/views/AxisView.vue#L1-L623)

## Shared State Management via Vuex Stores

### Status Store
The status store manages the application's connection status and real-time updates from the backend via WebSocket. It maintains state for connection status, recoater status, axis data, drum data, leveler data, print data, and error information.

```mermaid
classDiagram
class useStatusStore {
+isConnected : boolean
+recoaterStatus : object
+axisData : object
+drumData : object
+levelerData : object
+printData : object
+lastError : string
+lastUpdate : Date
+currentPage : string
+subscribedDataTypes : Set
+updateStatus(statusData)
+updateAxisData(newAxisData)
+updateDrumData(data)
+updateLevelerData(data)
+updatePrintData(data)
+setError(error)
+setConnectionStatus(connected)
+setCurrentPage(page)
+updateDataSubscriptions()
+setManualSubscriptions(dataTypes)
+fetchStatus()
+connectWebSocket()
+disconnectWebSocket()
}
```

**Diagram sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)

### Print Job Store
The print job store manages multi-material print job state, including 3-drum coordination, job progress, error handling, and real-time updates. It tracks job status, drum readiness, file uploads, and error flags for OPC UA coordination.

```mermaid
classDiagram
class usePrintJobStore {
+multiMaterialJob : object
+errorFlags : object
+isLoading : boolean
+isStartingJob : boolean
+isCancellingJob : boolean
+isClearingErrors : boolean
+uploadedFiles : object
+lastUploadedFiles : object
+isJobActive : boolean
+hasActiveJob : boolean
+hasErrors : boolean
+hasCriticalError : boolean
+allDrumsReady : boolean
+allFilesUploaded : boolean
+hasMinimumFiles : boolean
+canStartJob : boolean
+jobProgress : number
+updateJobStatus(statusData)
+updateDrumStatus(drumId, drumData)
+setErrorFlags(backendError, plcError, message)
+clearErrorFlags()
+closeCriticalModal()
+setFileUploaded(drumId, fileData)
+clearUploadedFiles()
+setLastUploadedFile(drumId, fileName, source)
+clearLastUploadedFiles()
+getLastUploadedFileName(drumId)
+resetJobState()
+startMultiMaterialJob()
+cancelMultiMaterialJob()
+clearErrorFlagsAPI()
+fetchJobStatus()
+fetchDrumStatus(drumId)
}
```

**Diagram sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L320)

## Navigation Guards and Page-aware Data Subscriptions

The application implements a navigation guard that updates data subscriptions based on the current page. When a route change occurs, the guard updates the currentPage value in the status store, which triggers selective data fetching for the new view.

```mermaid
sequenceDiagram
participant Router
participant StatusStore
participant WebSocket
Router->>StatusStore : beforeEach(to, from, next)
StatusStore->>StatusStore : setCurrentPage(to.name)
StatusStore->>StatusStore : updateDataSubscriptions()
StatusStore->>WebSocket : Send subscription update
WebSocket-->>StatusStore : Stream relevant data
StatusStore-->>Router : next()
```

**Diagram sources**
- [index.js](file://frontend/src/router/index.js#L48-L56)
- [status.js](file://frontend/src/stores/status.js#L84-L115)

The page-aware data subscription system optimizes performance by only requesting data that is relevant to the current view:

- **Status page**: Subscribes to basic status information
- **Recoater page**: Subscribes to drum and leveler data
- **Print page**: Subscribes to print and drum data
- **Config page**: Can subscribe to comprehensive data (currently commented out)
- **Axis page**: No additional subscriptions (currently commented out)

This approach reduces network traffic and improves application responsiveness by avoiding unnecessary data transfers.

**Section sources**
- [index.js](file://frontend/src/router/index.js#L48-L56)
- [status.js](file://frontend/src/stores/status.js#L84-L115)

## Programmatic Navigation and Route-based Data Loading

The application uses programmatic navigation patterns to manage user interactions and state changes. While direct router.push() calls are not explicitly shown in the analyzed components, the navigation system is designed to support programmatic navigation through the Vue Router instance.

Route-based data loading is implemented through the page-aware subscription system, where data fetching is triggered by route changes. When a user navigates to a new view, the navigation guard updates the currentPage value, which in turn triggers the appropriate data subscriptions.

The StatusView component demonstrates route-based data loading by fetching initial status information when the component is mounted:

```javascript
onMounted(() => {
  refreshStatus()
})
```

Similarly, the ConfigurationView component loads configuration data when the component is mounted:

```javascript
onMounted(() => {
  loadConfiguration()
})
```

These patterns ensure that each view has the necessary data available when it becomes active, providing a responsive user experience.

**Section sources**
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L278-L282)
- [ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue#L178-L181)

## Lazy Loading and Performance Optimization

The application implements lazy loading for all views except the default StatusView. This optimization strategy reduces the initial bundle size and improves application startup time by only loading components when they are needed.

```javascript
{
  path: '/axis',
  name: 'axis',
  component: () => import('../views/AxisView.vue')
}
```

The lazy loading pattern uses dynamic imports, which are automatically code-split by the Vite build system. This results in separate chunks for each view component, allowing the browser to download them on demand.

The StatusView is loaded eagerly as the default route, ensuring that the primary interface is immediately available when the application starts. All other views are lazy-loaded, balancing initial load performance with subsequent navigation responsiveness.

This approach optimizes resource usage by:
- Reducing initial download size
- Minimizing memory usage
- Improving time-to-interactive
- Enabling parallel loading of non-critical components

**Section sources**
- [index.js](file://frontend/src/router/index.js#L20-L30)

## Error Handling and Testing Practices

### Error Handling
The application implements comprehensive error handling at multiple levels:

1. **Component-level error handling**: Each view component includes error states and user feedback mechanisms
2. **Store-level error handling**: The status store manages connection errors and backend errors
3. **API-level error handling**: Service calls are wrapped in try-catch blocks with appropriate error reporting

The StatusView includes server control error handling with user confirmation dialogs and error alerts:

```javascript
async function restartServer() {
  if (!confirm('Are you sure you want to restart the recoater server?')) {
    return
  }
  
  try {
    await apiService.restartServer()
  } catch (error) {
    alert('Failed to restart server: ' + (error.response?.data?.detail || error.message))
  }
}
```

### Testing Practices
The application includes test files for view components, following Vue testing best practices:

```mermaid
graph TD
A[PrintView.test.js] --> B[Unit Tests]
C[StatusView.test.js] --> B
D[ConfigurationView.test.js] --> B
B --> E[Component Rendering]
B --> F[User Interactions]
B --> G[State Management]
B --> H[Error Scenarios]
```

**Diagram sources**
- [PrintView.test.js](file://frontend/tests/PrintView.test.js)
- [StatusView.test.js](file://frontend/tests/StatusView.test.js)
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js)

The test files verify:
- Component rendering with proper props and state
- User interactions and event handling
- Integration with Vuex stores
- Error handling and edge cases
- Form validation and data submission

Testing practices follow the Vue Test Utils and Vitest frameworks, ensuring comprehensive coverage of component functionality.

**Section sources**
- [PrintView.test.js](file://frontend/tests/PrintView.test.js)
- [StatusView.test.js](file://frontend/tests/StatusView.test.js)
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js)

## Conclusion
The Vue Router implementation in APIRecoater_Ethernet provides a robust navigation system that effectively manages the application's multiple views and their associated functionality. The routing configuration, combined with the page-aware data subscription system, creates an efficient architecture that optimizes data loading and improves user experience.

Key strengths of the implementation include:
- Clear separation of concerns between routes and components
- Effective use of lazy loading to optimize performance
- Sophisticated page-aware data subscription system
- Comprehensive error handling at multiple levels
- Well-structured testing practices for view components

The navigation system successfully enables users to move between different functional areas of the recoater control interface while maintaining optimal performance and responsiveness. The integration with Vuex stores ensures consistent state management across views, creating a cohesive user experience.