# Backend Architecture

<cite>
**Referenced Files in This Document**   
- [main.py](file://backend\app\main.py) - *Updated with WebSocketHandler extraction*
- [websockets.py](file://backend\app\websockets.py) - *New WebSocketHandler implementation*
- [websocket_manager.py](file://backend\app\services\websocket_manager.py) - *WebSocketConnectionManager implementation*
- [status_poller.py](file://backend\app\services\status_poller.py) - *Status polling service with WebSocket integration*
- [data_gatherer.py](file://backend\app\services\data_gatherer.py) - *Data gathering service for status updates*
- [dependencies.py](file://backend\app\dependencies.py) - *Dependency injection system*
- [print.py](file://backend\app\api\print.py)
- [axis.py](file://backend\app\api\axis.py)
- [configuration.py](file://backend\app\api\configuration.py)
- [recoater_controls.py](file://backend\app\api\recoater_controls.py)
- [status.py](file://backend\app\api\status.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [coordination_engine.py](file://backend\app\services\coordination_engine.py)
- [heartbeat.py](file://backend\app\utils\heartbeat.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [multilayer_job.py](file://backend\app\models\multilayer_job.py)
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the extraction of WebSocket functionality into a separate websockets.py module
- Added detailed documentation for the new WebSocketHandler class and its integration with WebSocketConnectionManager
- Updated architecture diagrams to reflect the new WebSocket implementation structure
- Enhanced documentation of the WebSocket message flow and subscription management
- Added sequence diagram for WebSocket connection lifecycle
- Updated section sources to reflect recent changes in WebSocket implementation

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The APIRecoater_Ethernet backend is a FastAPI-based application designed to manage and coordinate multi-material 3D printing operations with a recoater system. It serves as an intermediary between the frontend HMI (Human-Machine Interface) and physical or simulated recoater hardware, providing REST APIs, WebSocket real-time updates, and OPC UA integration for industrial automation. The system enables complex multi-layer print jobs across three drums, handling layer parameter configuration, CLI file processing, status monitoring, and coordination with a TwinCAT PLC via OPC UA. This document provides a comprehensive architectural overview of the backend system, detailing its components, data flows, and integration patterns.

## Project Structure

``mermaid
graph TD
backend[backend/] --> app[app/]
backend --> services[backend/services/]
backend --> tests[backend/tests/]
app --> api[api/]
app --> config[config/]
app --> models[models/]
app --> services[services/]
app --> utils[utils/]
app --> dependencies[dependencies.py]
app --> main[main.py]
app --> websockets[websockets.py]
api --> axis[axis.py]
api --> configuration[configuration.py]
api --> print[print.py]
api --> recoater_controls[recoater_controls.py]
api --> status[status.py]
config --> opcua_config[opcua_config.py]
models --> multilayer_job[multilayer_job.py]
services --> coordination_engine[coordination_engine.py]
services --> data_gatherer[data_gatherer.py]
services --> multilayer_job_manager[multilayer_job_manager.py]
services --> opcua_coordinator[opcua_coordinator.py]
services --> opcua_server[opcua_server.py]
services --> status_poller[status_poller.py]
services --> websocket_manager[websocket_manager.py]
utils --> heartbeat[heartbeat.py]
```

**Diagram sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [project_structure](file://#L1-L100)

## Core Components

The backend architecture is built around several core components that work together to provide a robust system for managing recoater operations. The FastAPI application in main.py serves as the entry point, routing requests to various API modules that handle specific functionality. The dependency injection system in dependencies.py manages shared resources like the recoater client and OPC UA coordinator. The OPC UA Server (opcua_server.py) exposes coordination variables to industrial clients, while the OPC UA Coordinator (opcua_coordinator.py) provides a high-level interface for managing these variables. The Coordination Engine (coordination_engine.py) acts as the central logic hub, synchronizing multi-layer print jobs with hardware operations. The Status Poller (status_poller.py) monitors device state, and the WebSocket Manager (websocket_manager.py) broadcasts real-time updates to the frontend. The WebSocketHandler (websockets.py) manages WebSocket connections and message processing, having been extracted from main.py into its own module.

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L525)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)

## Architecture Overview

``mermaid
graph TD
Frontend[Frontend HMI] --> FastAPI[FastAPI Server]
subgraph Backend
FastAPI --> API[API Routers]
API --> Axis[axis.py]
API --> Configuration[configuration.py]
API --> Print[print.py]
API --> Recoater[recoater_controls.py]
API --> Status[status.py]
FastAPI --> Dependencies[dependencies.py]
FastAPI --> WebSocketHandler[WebSocketHandler]
Dependencies --> RecoaterClient[RecoaterClient]
Dependencies --> OPCUACoordinator[OPCUACoordinator]
Dependencies --> JobManager[MultiMaterialJobManager]
Print --> JobManager
Print --> CLI[cli_parser.py]
CoordinationEngine[coordination_engine.py] --> JobManager
CoordinationEngine --> OPCUACoordinator
CoordinationEngine --> RecoaterClient
OPCUACoordinator --> OPCUAServer[OPCUAServerManager]
OPCUAServer --> PLC[TwinCAT PLC]
StatusPoller[StatusPollingService] --> RecoaterClient
StatusPoller --> WebSocketManager[WebSocketConnectionManager]
WebSocketHandler --> WebSocketManager
WebSocketManager --> Frontend
Heartbeat[Heartbeat] --> RecoaterClient
end
subgraph Hardware
RecoaterClient --> PhysicalRecoater[Physical Recoater]
RecoaterClient --> SimulatedRecoater[Simulated Recoater]
end
```

**Diagram sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)
- [print.py](file://backend\app\api\print.py#L1-L1012)
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)

## Detailed Component Analysis

### FastAPI Application Structure

The main.py file contains the FastAPI application that serves as the entry point for the backend system. It sets up the application with proper configuration, including CORS middleware to allow connections from the frontend development server. The application uses a lifespan context manager to handle startup and shutdown procedures, ensuring proper initialization and cleanup of resources. The recent implementation of .env variable support has enhanced the configuration architecture, allowing environment-specific settings to be easily managed. The WebSocket functionality has been extracted into a separate websockets.py module, improving code organization and maintainability.

``mermaid
sequenceDiagram
participant App as main.py
participant Lifespan as lifespan()
participant Init as initialize_*
participant Poller as StatusPollingService
participant Heartbeat as Heartbeat
App->>Lifespan : Startup
Lifespan->>Init : initialize_recoater_client()
Lifespan->>Init : initialize_multilayer_job_manager()
Lifespan->>Init : initialize_opcua_coordinator()
Lifespan->>Poller : status_poller.start()
Lifespan->>Heartbeat : start_heartbeat_task()
Lifespan-->>App : Yield
App->>App : Include API routers
App->>App : Register WebSocket endpoint
loop Shutdown
App->>Lifespan : Shutdown
Lifespan->>Poller : status_poller.stop()
Lifespan->>Heartbeat : stop_heartbeat_task()
end
```

**Updated** Added documentation for WebSocketHandler extraction and .env configuration support

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)

### API Module Routing

The backend implements a modular API structure with separate router files for different functional areas. These include axis.py for axis control, configuration.py for system configuration, print.py for print job management, recoater_controls.py for recoater-specific controls, and status.py for status queries. Each router is prefixed with "/api/v1" and grouped under appropriate tags for OpenAPI documentation.

``mermaid
graph TD
main[main.py] --> app[FastAPI app]
app --> include_router[app.include_router()]
include_router --> status_router[status_router]
include_router --> axis_router[axis_router]
include_router --> recoater_router[recoater_router]
include_router --> print_router[print_router]
include_router --> config_router[config_router]
status_router --> status[status.py]
axis_router --> axis[axis.py]
recoater_router --> recoater_controls[recoater_controls.py]
print_router --> print[print.py]
config_router --> configuration[configuration.py]
```

**Diagram sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [print.py](file://backend\app\api\print.py#L1-L1012)

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [print.py](file://backend\app\api\print.py#L1-L1012)

### Dependency Injection System

The dependencies.py module implements a dependency injection pattern to manage shared application resources. It provides centralized initialization and access to critical components like the recoater client, OPC UA coordinator, and multilayer job manager. This approach prevents circular imports and ensures that components are properly initialized before use. The system now supports environment variable configuration through .env files, allowing for flexible deployment across different environments.

``mermaid
classDiagram
class dependencies {
+_recoater_client : RecoaterClient
+_opcua_coordinator : OPCUACoordinator
+_multilayer_job_manager : MultiMaterialJobManager
+initialize_recoater_client() : None
+initialize_opcua_coordinator() : None
+initialize_multilayer_job_manager() : None
+get_recoater_client() : RecoaterClient
+get_opcua_coordinator() : OPCUACoordinator
+get_multilayer_job_manager() : MultiMaterialJobManager
}
class main {
+lifespan() : asynccontextmanager
+app : FastAPI
}
main --> dependencies : Uses initialization functions
dependencies --> main : Provides dependency functions
```

**Updated** Added documentation for environment variable usage in dependency initialization

**Section sources**
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)

### OPC UA Server Implementation

The opcua_server.py module implements an OPC UA server using the asyncua library. This server exposes coordination variables that allow communication between the backend and a TwinCAT PLC. The server hosts variables for job control, recoater coordination, and error handling, enabling real-time synchronization between the software and hardware components. Configuration is now managed through environment variables, allowing for easy customization of server settings.

``mermaid
classDiagram
class OPCUAServerManager {
-config : OPCUAServerConfig
-server : Server
-namespace_idx : int
-variable_nodes : Dict[str, Any]
-_running : bool
-_restart_count : int
-_heartbeat_task : Task
+start_server() : bool
+stop_server() : None
+write_variable(name, value) : bool
+read_variable(name) : Any
+is_running : bool
+get_variable_names() : List[str]
-_create_coordination_variables() : None
-_get_ua_data_type(data_type) : ua.VariantType
-_heartbeat_loop() : None
-_cleanup() : None
-_handle_server_error(error) : None
}
class OPCUAServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
class CoordinationVariable {
+name : str
+node_id : str
+data_type : str
+initial_value : Any
+writable : bool
+description : str
}
OPCUAServerManager --> OPCUAServerConfig : Uses
OPCUAServerManager --> CoordinationVariable : Manages
OPCUAServerManager --> ua : Server
```

**Updated** Added documentation for environment variable configuration in OPC UA server settings

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L525)
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L294)

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L525)
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L294)

### OPC UA Coordinator

The opcua_coordinator.py module provides a high-level interface for OPC UA communication, abstracting away the complexity of direct server management. It offers convenience methods for common operations like setting job status, updating layer progress, and handling errors, making it easier for other components to interact with the OPC UA system. The coordinator now respects environment variable settings for connection parameters and server configuration.

``mermaid
classDiagram
class OPCUACoordinator {
-config : OPCUAServerConfig
-server_manager : OPCUAServerManager
-_connected : bool
-_monitoring_task : Task
-_event_handlers : Dict[str, List]
+connect() : bool
+disconnect() : bool
+write_variable(name, value) : bool
+read_variable(name) : Any
+subscribe_to_changes(variables, handler) : bool
+set_job_active(total_layers) : bool
+set_job_inactive() : bool
+update_layer_progress(current_layer) : bool
+set_recoater_ready_to_print(ready) : bool
+set_recoater_layer_complete(complete) : bool
+set_backend_error(error) : bool
+set_plc_error(error) : bool
+clear_error_flags() : bool
+is_connected() : bool
+get_server_status() : Dict[str, Any]
-_monitoring_loop() : None
-_trigger_event_handlers(variable_name, value) : None
}
OPCUACoordinator --> OPCUAServerManager : Delegates to
OPCUACoordinator --> CoordinationVariable : Manages
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L525)

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)

### Coordination Engine

The coordination_engine.py module serves as the central logic hub for multi-material print jobs. It orchestrates the entire printing process, coordinating between the recoater hardware, the OPC UA system, and the job manager. The engine handles layer-by-layer progression, error management, and state synchronization across all components.

``mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> UPLOADING : start_multimaterial_job()
UPLOADING --> WAITING_FOR_READY : _upload_layer_to_all_drums()
WAITING_FOR_READY --> PRINTING : All drums ready
PRINTING --> WAITING_FOR_COMPLETION : set_recoater_ready_to_print(True)
WAITING_FOR_COMPLETION --> IDLE : Job complete
WAITING_FOR_COMPLETION --> ERROR : Timeout or error
ERROR --> IDLE : clear_errors()
state CoordinationState {
IDLE
UPLOADING
WAITING_FOR_READY
PRINTING
WAITING_FOR_COMPLETION
ERROR
COMPLETE
}
```

**Diagram sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)

**Section sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)

### WebSocket Implementation

The WebSocket functionality has been refactored into a dedicated websockets.py module containing the WebSocketHandler class. This handler manages WebSocket connections and message processing, working with the WebSocketConnectionManager from websocket_manager.py to handle connection lifecycle and message broadcasting. The separation improves code organization and maintainability.

``mermaid
classDiagram
class WebSocketHandler {
-websocket_manager : WebSocketConnectionManager
+websocket_endpoint(websocket) : None
+handle_websocket_message(websocket, message_text) : None
}
class WebSocketConnectionManager {
-active_connections : List[WebSocket]
-connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket) : None
+disconnect(websocket) : None
+update_subscription(websocket, data_types) : None
+broadcast(message) : None
+get_required_data_types() : Set[str]
}
WebSocketHandler --> WebSocketConnectionManager : Uses
```

**Updated** Added documentation for WebSocketHandler extraction and implementation details

**Diagram sources**
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)

**Section sources**
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [main.py](file://backend\app\main.py#L1-L162)

### Status Poller and WebSocket Manager

The status_poller.py and websocket_manager.py modules work together to provide real-time status updates to the frontend. The status poller periodically queries the recoater hardware for its current state, while the WebSocket manager broadcasts these updates to all connected clients, enabling a responsive user interface. The polling interval is now configurable through environment variables. The StatusPollingService uses the WebSocketConnectionManager to determine required data types based on client subscriptions, optimizing data gathering efficiency.

``mermaid
sequenceDiagram
participant Poller as StatusPollingService
participant Gatherer as RecoaterDataGatherer
participant Client as RecoaterClient
participant Manager as WebSocketConnectionManager
participant Frontend as Frontend
Poller->>Poller : _polling_loop()
Poller->>Poller : _poll_and_broadcast()
Poller->>Manager : get_required_data_types()
Poller->>Gatherer : gather_all_data()
Gatherer->>Client : get_state()
Gatherer->>Client : get_drum_status(0)
Gatherer->>Client : get_drum_status(1)
Gatherer->>Client : get_drum_status(2)
Gatherer->>Client : get_layer_parameters()
Gatherer-->>Poller : gathered_data
Poller->>Gatherer : construct_status_message()
Poller->>Manager : broadcast(message)
Manager->>Frontend : Send WebSocket message
Poller->>Poller : sleep(poll_interval)
```

**Updated** Added documentation for environment variable configuration of polling interval and subscription-based data gathering

**Diagram sources**
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [data_gatherer.py](file://backend\app\services\data_gatherer.py#L1-L230)

**Section sources**
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [data_gatherer.py](file://backend\app\services\data_gatherer.py#L1-L230)

## Dependency Analysis

``mermaid
graph TD
main[main.py] --> dependencies[dependencies.py]
main --> status_poller[status_poller.py]
main --> websocket_manager[websocket_manager.py]
main --> websockets[websockets.py]
websockets --> websocket_manager
dependencies --> recoater_client[recoater_client.py]
dependencies --> mock_recoater_client[mock_recoater_client.py]
dependencies --> opcua_coordinator[opcua_coordinator.py]
print[print.py] --> dependencies
print --> cli_parser[cli_parser.py]
print --> multilayer_job_manager[multilayer_job_manager.py]
coordination_engine[coordination_engine.py] --> multilayer_job_manager
coordination_engine --> opcua_coordinator
coordination_engine --> recoater_client
opcua_coordinator --> opcua_server[opcua_server.py]
opcua_server --> opcua_config[opcua_config.py]
status_poller --> websocket_manager
status_poller --> data_gatherer[data_gatherer.py]
status_poller --> dependencies
websocket_manager --> data_gatherer
data_gatherer --> recoater_client
multilayer_job_manager --> recoater_client
```

**Diagram sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [data_gatherer.py](file://backend\app\services\data_gatherer.py#L1-L230)
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)
- [print.py](file://backend\app\api\print.py#L1-L1012)
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L525)

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)

## Performance Considerations

The backend architecture is designed with performance and scalability in mind. The use of asynchronous I/O throughout the system allows for efficient handling of concurrent operations without blocking the event loop. The status polling mechanism is optimized to only gather data that is actually required by connected clients, reducing unnecessary hardware queries. The OPC UA server implements auto-restart functionality to maintain availability in case of connection issues. The system uses in-memory caching for parsed CLI files to avoid repeated parsing operations. The heartbeat mechanism in heartbeat.py helps maintain stable connections with the recoater hardware. Error handling is implemented at multiple levels to ensure graceful degradation in case of failures. The modular design allows for independent scaling of different components based on their resource requirements. Environment variables now provide flexible configuration of performance-related parameters such as polling intervals and connection timeouts. The WebSocket subscription system ensures that only necessary data is gathered and transmitted, improving overall system efficiency.

## Troubleshooting Guide

Common issues and their solutions:

1. **OPC UA Connection Failures**: Check that the OPC UA server is running and accessible at the configured endpoint. Verify firewall settings and network connectivity between the backend and PLC. Ensure environment variables for OPC UA configuration are correctly set.

2. **Recoater Communication Errors**: Ensure the recoater hardware is powered on and connected to the network. Verify the RECOATER_API_BASE_URL environment variable points to the correct address.

3. **Status Updates Not Refreshing**: Check that the status polling task is running and the WebSocket connections are established. Verify the poll interval is set appropriately through the WEBSOCKET_POLL_INTERVAL environment variable.

4. **Job Coordination Issues**: Confirm that the OPC UA coordinator is properly connected and that the coordination variables are being updated as expected.

5. **CLI File Processing Errors**: Validate that uploaded CLI files are in the correct format and contain valid data. Check the parsing logs for specific error messages.

6. **Development Mode Issues**: When DEVELOPMENT_MODE is enabled, the system uses a mock recoater client. Ensure this setting matches your deployment environment.

7. **WebSocket Connection Issues**: Verify that the WebSocket endpoint is properly registered in main.py and that the WebSocketHandler is correctly initialized with the WebSocketConnectionManager.

**Updated** Added troubleshooting guidance for environment variable configuration issues and WebSocket connection problems

**Section sources**
- [main.py](file://backend\app\main.py#L1-L162)
- [websockets.py](file://backend\app\websockets.py#L1-L34)
- [websocket_manager.py](file://backend\app\services\websocket_manager.py#L1-L146)
- [status_poller.py](file://backend\app\services\status_poller.py#L1-L124)
- [dependencies.py](file://backend\app\dependencies.py#L1-L184)

## Conclusion

The APIRecoater_Ethernet backend presents a well-structured, modular architecture that effectively separates concerns while maintaining tight integration between components. The system leverages FastAPI for REST API endpoints, asyncua for OPC UA communication, and WebSockets for real-time updates, creating a comprehensive solution for managing multi-material 3D printing operations. The dependency injection pattern in dependencies.py provides a clean way to manage shared resources, while the service layer abstraction in the services directory promotes code reuse and testability. The coordination engine serves as the central orchestrator, ensuring synchronized operation between the software and hardware components. The architecture demonstrates strong design principles, including separation of concerns, asynchronous I/O for performance, and fault tolerance through comprehensive error handling. The recent addition of .env file support and environment variable configuration enhances deployment flexibility and simplifies environment-specific settings management. The extraction of WebSocket functionality into a dedicated module improves code organization and maintainability. This backend system provides a solid foundation for industrial automation applications requiring precise coordination between software and hardware components.