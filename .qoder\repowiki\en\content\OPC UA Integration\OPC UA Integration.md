# OPC UA Integration

<cite>
**Referenced Files in This Document**   
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Updated in recent commit*
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py) - *Replaces coordinator and server manager classes*
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py) - *Refactored from OPCUACoordinator functionality*
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py) - *Refactored from OPCUAServerManager functionality*
- [monitoring_mixin.py](file://backend\app\services\opcua\mixins\monitoring_mixin.py) - *Monitoring functionality*
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [dependencies.py](file://backend\app\dependencies.py) - *OPC UA service initialization*
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py) - *Async OPC UA-driven job status*
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py) - *Job status implementation*
- [multimaterial.py](file://backend\app\api\print\multimaterial.py) - *API layer for async job status*
- [opcua_simulator.py](file://tests\opcua_simulator.py)
</cite>

## Update Summary
**Changes Made**   
- Updated architecture overview to reflect the new mixin-based OPCUAService replacing OPCUACoordinator and OPCUAServerManager
- Added new sections for the coordination_mixin, server_mixin, and monitoring_mixin classes
- Updated the command propagation example to show the new async flow with OPC UA as source of truth
- Added documentation for the async OPC UA-driven job status implementation
- Removed references to legacy CoordinationStatus enum and redundant getter methods
- Updated section sources to reflect the new file locations and structure
- Enhanced the dependency analysis diagram to show the new mixin composition
- Updated the Table of Contents to include new sections

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive architectural and implementation documentation for the OPC UA integration layer in the APIRecoater_Ethernet system. The integration enables communication between the backend application and industrial recoater devices using the OPC UA client-server architecture. The backend acts as an OPC UA client to coordinate with industrial devices, primarily through a TwinCAT PLC. This document details the design patterns, configuration, services, and testing mechanisms involved in this integration, focusing on the roles of key components such as `opcua_config.py`, `OPCUAService`, `RecoaterClient`, and `MockRecoaterClient`. It also covers the use of `opcua_simulator.py` for development without physical hardware and provides examples of command propagation, such as the 'start_recoating' command, from API to OPC UA node write. Common issues like network latency, connection timeouts, and data type mismatches are addressed, along with performance considerations for polling frequency, subscription batching, and error recovery with retry logic.

## Project Structure
The project structure is organized into backend and frontend components, with additional utilities and test scripts. The backend contains the core logic for OPC UA integration, while the frontend provides a user interface for interaction. Key directories include `backend/app` for application logic, `backend/services` for hardware clients, and `tests` for validation. The OPC UA integration is primarily handled within the `backend/app/services/opcua` directory with a mixin-based architecture.

```
backend/
├── app/
│   ├── config/
│   │   └── opcua_config.py
│   ├── services/
│   │   └── opcua/
│   │       ├── mixins/
│   │       │   ├── coordination_mixin.py
│   │       │   ├── monitoring_mixin.py
│   │       │   └── server_mixin.py
│   │       ├── __init__.py
│   │       ├── models.py
│   │       └── opcua_service.py
│   └── api/
│       ├── recoater_controls.py
│       └── print.py
├── infrastructure/
│   ├── recoater_client/
│   │   ├── __init__.py
│   │   ├── client.py
│   │   ├── exceptions.py
│   │   ├── blade_controls.py
│   │   ├── leveler_controls.py
│   │   ├── print_controls.py
│   │   ├── file_management.py
│   │   └── async_client.py
│   ├── mock_recoater_client/
│   │   ├── __init__.py
│   │   ├── mock_client.py
│   │   ├── mock_blade_controls.py
│   │   ├── mock_drum_controls.py
│   │   ├── mock_leveler_controls.py
│   │   ├── mock_print_controls.py
│   │   ├── mock_file_management.py
│   │   └── mock_async_client.py
│   └── __init__.py
├── tests/
│   ├── test_recoater_client.py
│   └── test_opcua_infrastructure.py
├── opcua_simulator.py
└── simple_connection_test.py
```

**Diagram sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [opcua_simulator.py](file://tests\opcua_simulator.py)

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [opcua_simulator.py](file://tests\opcua_simulator.py)

## Core Components
The core components of the OPC UA integration layer include the configuration module (`opcua_config.py`), the unified service (`OPCUAService`), the hardware client (`RecoaterClient`), and the mock client (`MockRecoaterClient`). These components work together to enable seamless communication between the backend and industrial devices. The `OPCUAService` class replaces the previous `OPCUACoordinator` and `OPCUAServerManager` classes with a mixin-based architecture that combines server management, coordination, and monitoring functionality.

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)

## Architecture Overview
The OPC UA integration follows a client-server architecture where the backend acts as a client to industrial recoater devices. The `OPCUAService` serves as a unified interface combining server management, coordination, and monitoring functionality through mixin composition. This replaces the previous separate `OPCUACoordinator` and `OPCUAServerManager` classes. The `RecoaterClient` communicates with the actual hardware via HTTP, and the `MockRecoaterClient` provides a simulation for testing. The `opcua_simulator.py` script allows development without physical hardware by simulating an OPC UA server.

```
mermaid
graph LR
subgraph "Backend Application"
Frontend["Frontend (Vue.js)"]
API["FastAPI Endpoints"]
OPCUAService["OPCUAService (Unified)"]
end
subgraph "Hardware Layer"
PLC["TwinCAT PLC"]
Recoater["Recoater Device"]
end
Frontend --> API
API --> OPCUAService
OPCUAService --> |OPC UA Protocol| PLC
PLC --> Recoater
OPCUAService --> |HTTP| Recoater
MockClient["MockRecoaterClient"] --> OPCUAService
Simulator["opcua_simulator.py"] --> OPCUAService
```

**Diagram sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [opcua_simulator.py](file://tests\opcua_simulator.py)

## Detailed Component Analysis

### OPCUA Configuration
The `opcua_config.py` file defines the endpoint URLs, credentials, and node mappings for the OPC UA server. It includes configuration for the server endpoint, namespace URI, and a list of coordination variables with their data types and initial values.

```
mermaid
classDiagram
class OPCUAServerConfig {
+str endpoint
+str namespace_uri
+str server_name
+bool auto_restart
+int max_restart_attempts
+float restart_delay
}
class CoordinationVariable {
+str name
+str data_type
+Any initial_value
+bool writable
}
OPCUAServerConfig "1" -- "many" CoordinationVariable : contains
```

**Diagram sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)

### OPCUA Service Architecture
The `OPCUAService` class provides a unified interface for managing OPC UA communication by combining server management, coordination, and monitoring functionality through mixin composition. This replaces the previous separate `OPCUACoordinator` and `OPCUAServerManager` classes. The service uses a specific mixin composition order to ensure proper method resolution: `CoordinationMixin` comes first to override `write_variable()` for event handling, `ServerMixin` comes second to provide the base implementation, and `MonitoringMixin` comes last as it provides independent monitoring functionality.

```
mermaid
classDiagram
class OPCUAService {
+config : ServerConfig
+_server_running : bool
+_variable_store : Dict[str, Any]
+_connected : bool
+_monitoring_task : Optional[Task]
+_event_handlers : Dict[str, List]
+initialize() bool
+shutdown() bool
+connect() bool
+disconnect() bool
+is_connected() bool
+get_server_status() Dict
+write_variable(name, val) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
}
OPCUAService --> CoordinationMixin : inherits
OPCUAService --> ServerMixin : inherits
OPCUAService --> MonitoringMixin : inherits
```

**Diagram sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)

### Coordination Mixin
The `CoordinationMixin` class provides business-logic helpers for coordinating job state via the variable store. It implements the coordination layer between domain logic and infrastructure, handling connection state management, high-level business operations, event subscription, and error management. This mixin replaces the functionality previously contained in the `OPCUACoordinator` class.

```
mermaid
classDiagram
class CoordinationMixin {
+_connected : bool
+_monitoring_task : Optional[Task]
+_event_handlers : Dict[str, List]
+connect() bool
+disconnect() bool
+is_connected() bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+write_variable(name, val) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+_trigger_event_handlers(name, value) None
}
```

**Diagram sources**
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py)

**Section sources**
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py)

### Server Mixin
The `ServerMixin` class provides infrastructure-level functionality for OPC UA server lifecycle and variable store management. It manages server lifecycle (start/stop) and an in-process variable key→value store, exposing async read/write APIs consistent with OPC UA protocol expectations. This mixin replaces the functionality previously contained in the `OPCUAServerManager` class.

```
mermaid
classDiagram
class ServerMixin {
+_server_running : bool
+_variable_store : Dict[str, Any]
+start_server() bool
+stop_server() bool
+is_server_running() bool
+get_variable_names() List[str]
+write_variable(name, val) bool
+read_variable(name) Any
}
```

**Diagram sources**
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py)

**Section sources**
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py)

### Monitoring Mixin
The `MonitoringMixin` class provides health monitoring and system reliability features for OPC UA operations. It implements background task management for health monitoring with configurable heartbeat intervals, error detection, and graceful shutdown handling.

```
mermaid
classDiagram
class MonitoringMixin {
+monitoring_active : bool
+_monitor_task : Optional[Task]
+start_monitoring(interval) None
+stop_monitoring() None
+_monitoring_loop(interval) None
}
```

**Diagram sources**
- [monitoring_mixin.py](file://backend\app\services\opcua\mixins\monitoring_mixin.py)

**Section sources**
- [monitoring_mixin.py](file://backend\app\services\opcua\mixins\monitoring_mixin.py)

### Recoater Client Package Structure
The recoater client has been refactored into a package structure to improve maintainability and organization. The package is located at `backend/infrastructure/recoater_client/` and consists of multiple modules that work together to provide a comprehensive interface to the recoater hardware.

```
mermaid
classDiagram
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
+_make_request(method, endpoint, return_raw, **kwargs) Any
+get_state() Dict
+set_state(action) Dict
+get_config() Dict
+set_config(config) Dict
+get_drums() Dict
+get_drum(drum_id) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class BladeControlMixin {
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
}
class LevelerControlMixin {
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
}
class PrintControlMixin {
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class FileManagementMixin {
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
}
class AsyncClientMixin {
+async get_state_async() Dict
+async set_state_async(action) Dict
+async get_config_async() Dict
+async set_config_async(config) Dict
+async get_drums_async() Dict
+async get_drum_async(drum_id) Dict
+async health_check_async() bool
+async get_drum_motion_async(drum_id) Dict
+async set_drum_motion_async(drum_id, mode, speed, distance, turns) Dict
+async cancel_drum_motion_async(drum_id) Dict
+async get_drum_ejection_async(drum_id, unit) Dict
+async set_drum_ejection_async(drum_id, target, unit) Dict
+async get_drum_suction_async(drum_id) Dict
+async set_drum_suction_async(drum_id, target) Dict
}
RecoaterClient --> BladeControlMixin : inherits
RecoaterClient --> LevelerControlMixin : inherits
RecoaterClient --> PrintControlMixin : inherits
RecoaterClient --> FileManagementMixin : inherits
RecoaterClient --> AsyncClientMixin : inherits
```

**Diagram sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [blade_controls.py](file://backend\infrastructure\recoater_client\blade_controls.py)
- [leveler_controls.py](file://backend\infrastructure\recoater_client\leveler_controls.py)
- [print_controls.py](file://backend\infrastructure\recoater_client\print_controls.py)
- [file_management.py](file://backend\infrastructure\recoater_client\file_management.py)
- [async_client.py](file://backend\infrastructure\recoater_client\async_client.py)

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [blade_controls.py](file://backend\infrastructure\recoater_client\blade_controls.py)
- [leveler_controls.py](file://backend\infrastructure\recoater_client\leveler_controls.py)
- [print_controls.py](file://backend\infrastructure\recoater_client\print_controls.py)
- [file_management.py](file://backend\infrastructure\recoater_client\file_management.py)
- [async_client.py](file://backend\infrastructure\recoater_client\async_client.py)

### Recoater Client Implementation
The `RecoaterClient` class handles communication with the Aerosint SPD Recoater hardware API. It provides methods for all endpoints defined in the openapi.json specification, including drum control, blade control, leveler control, and print control. The client includes error handling and retry logic for robust operation. The implementation has been refactored into a package structure with specialized mixin classes for better maintainability.

The main `RecoaterClient` class in `client.py` serves as the entry point and orchestrator, inheriting functionality from various mixin classes that handle specific aspects of the hardware interface:

- **BladeControlMixin**: Handles all blade-related operations including screw control and motion
- **LevelerControlMixin**: Manages leveler pressure and sensor operations
- **PrintControlMixin**: Controls print job operations, layer parameters, and status monitoring
- **FileManagementMixin**: Handles geometry file uploads, downloads, and deletions
- **AsyncClientMixin**: Provides asynchronous versions of all client methods for non-blocking operations

The package also includes an `__init__.py` file that exports the main classes and exceptions for easy import:

```python
"""
Recoater Client Package
======================

This package provides the RecoaterClient class and related components for
communicating with the Aerosint SPD Recoater hardware API.

The package is organized into the following modules:
- client: Main RecoaterClient class
- exceptions: Custom exception classes
- async_client: Async wrapper methods for multi-material coordination
"""

from .exceptions import RecoaterConnectionError, RecoaterAPIError
from .client import RecoaterClient

__all__ = [
    'RecoaterClient',
    'RecoaterConnectionError', 
    'RecoaterAPIError'
]
```

This modular structure improves code organization, makes the codebase more maintainable, and allows for easier testing of individual components.

**Diagram sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [__init__.py](file://backend\infrastructure\recoater_client\__init__.py)

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [__init__.py](file://backend\infrastructure\recoater_client\__init__.py)

### Mock Recoater Client Package Structure
The mock recoater client has been refactored into a package structure to improve modularity and testability. The package is located at `backend/infrastructure/mock_recoater_client/` and consists of dedicated modules for different control aspects, mirroring the structure of the real client.

```
mermaid
classDiagram
class MockRecoaterClient {
+base_url : str
+timeout : float
+_start_time : float
+_state : Dict
+_print_job_state : str
+_print_job_id : Optional[str]
+_print_job_start_time : Optional[float]
+_drum_geometries : Dict
+_drum_states : Dict
+_current_layers : Dict
+get_state() Dict
+get_config() Dict
+set_config(config) Dict
+get_status() Dict
+start_job(job_data) Dict
+stop_job() Dict
+pause_job() Dict
+resume_job() Dict
+get_axis_position(axis) Dict
+move_axis(axis, position, speed) Dict
+home_axis(axis) Dict
+get_axis_status(axis) Dict
+get_gripper_state() Dict
+get_drums() List[Dict]
+get_drum(drum_id) Dict
+set_state(action) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
}
class MockBladeControlMixin {
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
}
class MockLevelerControlMixin {
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
}
class MockPrintControlMixin {
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class MockFileManagementMixin {
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
}
class MockAsyncClientMixin {
+async upload_cli_data(drum_id, cli_data) Dict
+async get_drum_status(drum_id) Dict
+async get_multimaterial_status() Dict
+async monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
+set_multimaterial_job_active(active) None
+advance_layer(drum_id) None
+reset_multimaterial_state() None
}
class MockDrumControlMixin {
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
}
MockRecoaterClient --> MockBladeControlMixin : inherits
MockRecoaterClient --> MockLevelerControlMixin : inherits
MockRecoaterClient --> MockPrintControlMixin : inherits
MockRecoaterClient --> MockFileManagementMixin : inherits
MockRecoaterClient --> MockAsyncClientMixin : inherits
MockRecoaterClient --> MockDrumControlMixin : inherits
```

**Diagram sources**
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [mock_blade_controls.py](file://backend\infrastructure\mock_recoater_client\mock_blade_controls.py)
- [mock_leveler_controls.py](file://backend\infrastructure\mock_recoater_client\mock_leveler_controls.py)
- [mock_print_controls.py](file://backend\infrastructure\mock_recoater_client\mock_print_controls.py)
- [mock_file_management.py](file://backend\infrastructure\mock_recoater_client\mock_file_management.py)
- [mock_async_client.py](file://backend\infrastructure\mock_recoater_client\mock_async_client.py)
- [mock_drum_controls.py](file://backend\infrastructure\mock_recoater_client\mock_drum_controls.py)

**Section sources**
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [mock_blade_controls.py](file://backend\infrastructure\mock_recoater_client\mock_blade_controls.py)
- [mock_leveler_controls.py](file://backend\infrastructure\mock_recoater_client\mock_leveler_controls.py)
- [mock_print_controls.py](file://backend\infrastructure\mock_recoater_client\mock_print_controls.py)
- [mock_file_management.py](file://backend\infrastructure\mock_recoater_client\mock_file_management.py)
- [mock_async_client.py](file://backend\infrastructure\mock_recoater_client\mock_async_client.py)
- [mock_drum_controls.py](file://backend\infrastructure\mock_recoater_client\mock_drum_controls.py)

### Mock Recoater Client Implementation
The `MockRecoaterClient` class simulates the behavior of the real recoater hardware for development and testing. It returns realistic sample data and simulates state changes, allowing frontend development without requiring actual hardware. The implementation has been refactored into a package structure with dedicated modules for different control aspects, improving modularity and testability.

The main `MockRecoaterClient` class in `mock_client.py` serves as the entry point and orchestrator, inheriting functionality from various mixin classes that handle specific aspects of the hardware interface:

- **MockBladeControlMixin**: Handles all blade-related operations including screw control and motion
- **MockLevelerControlMixin**: Manages leveler pressure and sensor operations
- **MockPrintControlMixin**: Controls print job operations, layer parameters, and status monitoring
- **MockFileManagementMixin**: Handles geometry file uploads, downloads, and deletions
- **MockAsyncClientMixin**: Provides asynchronous versions of all client methods for multi-material coordination
- **MockDrumControlMixin**: Handles drum motion, ejection, and suction controls

The package also includes an `__init__.py` file that exports the main classes for easy import:

```python
"""
Mock Recoater Client Package
============================

This package provides the MockRecoaterClient class and related components for
simulating the Aerosint SPD Recoater hardware API during development and testing.

The package is organized into the following modules:
- mock_client: Main MockRecoaterClient class with core functionality
- mock_blade_controls: Blade control methods
- mock_leveler_controls: Leveler control methods  
- mock_print_controls: Print control methods
- mock_file_management: File management methods
- mock_async_client: Async wrapper methods for multi-material coordination
- mock_drum_controls: Drum control methods

The MockRecoaterClient class implements the same interface as RecoaterClient
but returns mock data instead of making actual HTTP requests to hardware.
"""

from .mock_client import MockRecoaterClient

__all__ = [
    'MockRecoaterClient'
]
```

The mock client's behavior is controlled by the `DEVELOPMENT_MODE` environment variable in `dependencies.py`, which determines whether to initialize the real or mock client during application startup:

```python
def initialize_recoater_client() -> None:
    """
    Initialize the global recoater client instance.

    This should be called during application startup.
    """
    global _recoater_client

    base_url = os.getenv("RECOATER_API_BASE_URL", "http://172.16.17.224:8080")
    development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

    if development_mode:
        logger.info("Initializing mock recoater client for development mode")
        _recoater_client = MockRecoaterClient(base_url)
    else:
        logger.info("Initializing real recoater client for production mode")
        _recoater_client = RecoaterClient(base_url)
```

This modular structure improves code organization, makes the codebase more maintainable, and allows for easier testing of individual components.

**Diagram sources**
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [__init__.py](file://backend\infrastructure\mock_recoater_client\__init__.py)
- [dependencies.py](file://backend\app\dependencies.py)

**Section sources**
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)
- [__init__.py](file://backend\infrastructure\mock_recoater_client\__init__.py)
- [dependencies.py](file://backend\app\dependencies.py)

### Async OPC UA-Driven Job Status
The job status implementation has been converted to an async OPC UA-driven model, removing in-memory state and making OPC UA the single source of truth. The `get_job_status()` method in `LayerProcessingMixin` now reads directly from OPC UA variables instead of relying on in-memory state, ensuring consistency and eliminating potential state drift.

```python
async def get_job_status(self) -> Optional[Dict[str, Any]]:
    """Get current job status using OPC UA as the source of truth."""
    # Attempt to read live values from OPC UA
    opcua = None
    try:
        opcua = self._resolve_opcua() if hasattr(self, "_resolve_opcua") else None
    except Exception:
        opcua = None

    # Defaults when OPC UA not available
    job_active = False
    total_layers = 0
    current_layer = 0
    backend_error = False
    plc_error = False
    ready_to_print = False
    layer_complete = False

    if opcua:
        try:
            # Read variables asynchronously from the in-proc OPC UA service
            job_active = bool(await opcua.read_variable("job_active"))
            total_layers = int(await opcua.read_variable("total_layers") or 0)
            current_layer = int(await opcua.read_variable("current_layer") or 0)
            backend_error = bool(await opcua.read_variable("backend_error") or False)
            plc_error = bool(await opcua.read_variable("plc_error") or False)
            ready_to_print = bool(await opcua.read_variable("recoater_ready_to_print") or False)
            layer_complete = bool(await opcua.read_variable("recoater_layer_complete") or False)
        except Exception as e:
            logger.warning(f"Failed to read OPC UA variables for status: {e}")
```

This change ensures that the job status is always consistent with the actual state in the OPC UA server, eliminating race conditions and state inconsistencies that could occur with in-memory state management.

**Section sources**
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py)
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py)
- [multimaterial.py](file://backend\app\api\print\multimaterial.py)

### Command Propagation Example
The 'start_recoating' command propagates from the API to the OPC UA node write through the following sequence. The implementation includes type coercion to ensure data type compatibility between the backend and OPC UA server.

```
mermaid
sequenceDiagram
participant Frontend
participant API
participant OPCUAService
participant PLC
participant Recoater
Frontend->>API : POST /api/print/start_recoating
API->>OPCUAService : set_job_active(total_layers)
OPCUAService->>OPCUAService : write_variable("job_active", True)
Note right of OPCUAService : Type coercion applied to ensure Boolean type
OPCUAService->>PLC : OPC UA write job_active=True
PLC->>Recoater : Start recoating process
Recoater-->>PLC : Status updates
PLC-->>OPCUAService : OPC UA variable updates
OPCUAService-->>API : Event notifications
API-->>Frontend : Status updates
```

**Diagram sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [multimaterial.py](file://backend\app\api\print\multimaterial.py)

## Dependency Analysis
The components of the OPC UA integration layer are tightly coupled, with the `OPCUAService` combining server, coordination, and monitoring functionality through mixin composition. The service depends on the configuration in `opcua_config.py`, and both the real and mock clients depend on the OPC UA service for coordination. The `RecoaterClient` and `MockRecoaterClient` provide alternative implementations for hardware communication, allowing for flexible testing and development.

```
mermaid
graph TD
opcua_config --> opcua_service
opcua_service --> coordination_mixin
opcua_service --> server_mixin
opcua_service --> monitoring_mixin
opcua_service --> recoater_client
opcua_service --> mock_recoater_client
recoater_client --> blade_controls
recoater_client --> leveler_controls
recoater_client --> print_controls
recoater_client --> file_management
recoater_client --> async_client
mock_recoater_client --> mock_blade_controls
mock_recoater_client --> mock_leveler_controls
mock_recoater_client --> mock_print_controls
mock_recoater_client --> mock_file_management
mock_recoater_client --> mock_async_client
mock_recoater_client --> mock_drum_controls
```

**Diagram sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py)
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py)
- [monitoring_mixin.py](file://backend\app\services\opcua\mixins\monitoring_mixin.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py)

## Performance Considerations
Performance considerations for the OPC UA integration include polling frequency, subscription batching, and error recovery with retry logic. The `OPCUAService` uses a heartbeat loop to keep the server alive, and the `RecoaterClient` includes retry logic for connection errors. The service uses asynchronous tasks for monitoring and event handling to ensure responsiveness. The async OPC UA-driven job status implementation reduces the need for frequent polling by making OPC UA the single source of truth, improving overall system efficiency.

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py)

## Troubleshooting Guide
Common issues in the OPC UA integration include network latency, connection timeouts, and data type mismatches. Network latency can be mitigated by optimizing the polling frequency and using subscription batching. Connection timeouts can be handled with retry logic and proper error handling. Data type mismatches can be avoided by ensuring that the data types in `opcua_config.py` match the expected types in the PLC. The implementation now includes type coercion in the `write_variable` method of `ServerMixin` to automatically convert values to the expected OPC UA data types, preventing BadTypeMismatch errors. The async OPC UA-driven job status implementation helps prevent state inconsistencies that could occur with in-memory state management.

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py)

## Conclusion
The OPC UA integration layer in APIRecoater_Ethernet provides a robust and flexible solution for communication between the backend and industrial recoater devices. The use of a client-server architecture, with the backend acting as a client, enables efficient coordination and control. The `OPCUAService` class replaces the previous `OPCUACoordinator` and `OPCUAServerManager` classes with a mixin-based architecture that combines server management, coordination, and monitoring functionality, improving code organization and maintainability. The `RecoaterClient` and `MockRecoaterClient` provide reliable hardware communication and testing capabilities. The `opcua_simulator.py` script allows for development without physical hardware, and the configuration in `opcua_config.py` ensures consistency and ease of setup. Performance considerations and error handling mechanisms ensure reliable operation in real-world conditions. Recent updates have improved integration stability by adding type coercion to prevent BadTypeMismatch errors when writing values to OPC UA nodes. The refactoring of both the `recoater_client.py` and `mock_recoater_client.py` into package structures with specialized mixin classes has improved maintainability and organization of the codebase. The selection between real and mock clients is now controlled by the `DEVELOPMENT_MODE` environment variable, providing a clean separation between development and production environments. The conversion to an async OPC UA-driven job status model, removing in-memory state and making OPC UA the single source of truth, has eliminated potential state inconsistencies and improved system reliability.