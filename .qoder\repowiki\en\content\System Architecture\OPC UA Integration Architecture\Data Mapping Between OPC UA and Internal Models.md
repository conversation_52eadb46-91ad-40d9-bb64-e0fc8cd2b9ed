# Data Mapping Between OPC UA and Internal Models

<cite>
**Referenced Files in This Document**   
- [opcua_server.py](file://backend/app/services/opcua_server.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [OPC UA Configuration and Variable Definitions](#opc-ua-configuration-and-variable-definitions)
3. [Internal Domain Models](#internal-domain-models)
4. [Bidirectional Data Mapping Architecture](#bidirectional-data-mapping-architecture)
5. [Writing Internal State to OPC UA Nodes](#writing-internal-state-to-opc-ua-nodes)
6. [Reading OPC UA Node Values into Internal State](#reading-opc-ua-node-values-into-internal-state)
7. [Type Conversion and Value Coercion](#type-conversion-and-value-coercion)
8. [Synchronization Timing and Consistency](#synchronization-timing-and-consistency)
9. [Error Handling and Null Value Management](#error-handling-and-null-value-management)
10. [Coordination Engine and Status Polling](#coordination-engine-and-status-polling)
11. [Schema Diagrams and Field Mappings](#schema-diagrams-and-field-mappings)

## Introduction
This document details the bidirectional data mapping system between OPC UA nodes and internal Python domain models in the multi-material recoater system. The architecture enables seamless coordination between the FastAPI backend and TwinCAT PLC through a well-defined set of coordination variables. The system uses OPC UA as a communication protocol to synchronize state between the backend application and industrial control systems, allowing real-time monitoring and control of the recoater process.

The data mapping system consists of two primary components: the OPC UA server that exposes coordination variables to the PLC, and the coordination layer that translates between internal application state and OPC UA variables. This document explains how internal state (such as job status, layer progress, and error conditions) is synchronized with OPC UA variables in both directions, ensuring consistency across the distributed system.

## OPC UA Configuration and Variable Definitions
The OPC UA server configuration defines the variables that serve as the interface between the backend application and the PLC. These variables are defined in the opcua_config.py file and represent the shared state between systems.

```mermaid
classDiagram
class OPCUAServerConfig {
+string endpoint
+string server_name
+string namespace_uri
+int namespace_idx
+string security_policy
+string security_mode
+string certificate_path
+string private_key_path
+float connection_timeout
+float session_timeout
+bool auto_restart
+float restart_delay
+int max_restart_attempts
}
class CoordinationVariable {
+string name
+string node_id
+string data_type
+Any initial_value
+bool writable
+string description
}
class OPCUACoordinator {
+OPCUAServerConfig config
+OPCUAServerManager server_manager
+bool _connected
+Task _monitoring_task
+Dict[str, List] _event_handlers
}
OPCUACoordinator --> OPCUAServerConfig : "uses"
OPCUACoordinator --> CoordinationVariable : "manages"
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L45-L294)

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)

## Internal Domain Models
The internal state of the application is represented by domain models that capture the complete state of multi-material print jobs. These models are synchronized with OPC UA variables to maintain consistency between the application and control systems.

```mermaid
classDiagram
class JobStatus {
<<enumeration>>
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
class DrumState {
+int drum_id
+string status
+bool ready
+bool uploaded
+int current_layer
+int total_layers
+string error_message
+string file_id
+float last_update_time
+reset() void
}
class LayerData {
+int layer_number
+bytes cli_data
+bool is_empty
+float upload_time
+float completion_time
}
class MultiMaterialJobState {
+string job_id
+Dict[int, str] file_ids
+int total_layers
+int current_layer
+Dict[int, List[LayerData]] remaining_layers
+Dict[int, List[str]] header_lines
+bool is_active
+JobStatus status
+float start_time
+float last_layer_time
+float estimated_completion
+bool waiting_for_print_start
+bool waiting_for_layer_complete
+string error_message
+int retry_count
+Dict[int, DrumState] drums
+get_progress_percentage() float
}
MultiMaterialJobState --> DrumState : "contains"
MultiMaterialJobState --> LayerData : "contains"
MultiMaterialJobState --> JobStatus : "references"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L19-L111)

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L1-L111)

## Bidirectional Data Mapping Architecture
The data mapping system uses a layered architecture to translate between internal application state and OPC UA variables. The OPCUACoordinator provides a high-level interface for managing job state, while the OPCUAServerManager handles the low-level OPC UA protocol details.

```mermaid
graph TD
subgraph "Application Layer"
A[FastAPI Endpoints]
B[Business Logic]
end
subgraph "Coordination Layer"
C[OPCUACoordinator]
end
subgraph "Protocol Layer"
D[OPCUAServerManager]
E[asyncua Server]
end
subgraph "Control System"
F[TwinCAT PLC]
end
A --> B
B --> C
C --> D
D --> E
E < --> F
style A fill:#f9f,stroke:#333
style B fill:#f9f,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#bbf,stroke:#333
style E fill:#9f9,stroke:#333
style F fill:#9f9,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)

## Writing Internal State to OPC UA Nodes
The process of writing internal state to OPC UA nodes involves type coercion, validation, and protocol-specific formatting. The write_variable method in OPCUAServerManager ensures that values are properly converted to the expected OPC UA data types.

```mermaid
flowchart TD
Start([Write Variable Request]) --> ValidateServer["Validate Server Running"]
ValidateServer --> ServerRunning{Server Running?}
ServerRunning --> |No| ReturnFalse["Return False"]
ServerRunning --> |Yes| ValidateVariable["Validate Variable Exists"]
ValidateVariable --> VariableExists{Variable Exists?}
VariableExists --> |No| ReturnFalse
VariableExists --> |Yes| DetermineType["Determine Expected Data Type"]
DetermineType --> CoerceValue["Coerce Value to Expected Type"]
CoerceValue --> CreateVariant["Create OPC UA Variant"]
CreateVariant --> WriteNode["Write to OPC UA Node"]
WriteNode --> TriggerHandlers["Trigger Change Handlers"]
TriggerHandlers --> ReturnSuccess["Return True"]
style ReturnFalse fill:#f99,stroke:#333
style ReturnSuccess fill:#9f9,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L372-L438)

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L372-L438)

## Reading OPC UA Node Values into Internal State
Reading values from OPC UA nodes follows a similar pattern to writing, with appropriate error handling and type safety. The read_variable method provides a safe interface for accessing OPC UA variable values from the application.

```mermaid
sequenceDiagram
participant Application as "Application"
participant Coordinator as "OPCUACoordinator"
participant ServerManager as "OPCUAServerManager"
participant OPCUAServer as "asyncua Server"
Application->>Coordinator : read_variable("job_active")
Coordinator->>ServerManager : read_variable("job_active")
ServerManager->>ServerManager : Validate server running
ServerManager->>ServerManager : Validate variable exists
ServerManager->>OPCUAServer : node.read_value()
OPCUAServer-->>ServerManager : Return value
ServerManager-->>Coordinator : Return value
Coordinator-->>Application : Return value
Note over ServerManager,OPCUAServer : Direct OPC UA protocol interaction
Note over Coordinator,ServerManager : High-level to low-level translation
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L440-L466)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L336-L350)

## Type Conversion and Value Coercion
The system implements comprehensive type conversion to ensure data integrity when mapping between Python types and OPC UA data types. This includes handling of boolean, integer, string, and floating-point values with appropriate coercion rules.

```mermaid
flowchart TD
Input[Input Value] --> TypeCheck["Check Expected Type"]
TypeCheck --> Int32{"Expected: Int32?"}
Int32 --> |Yes| CoerceInt["Coerce to int"]
Int32 --> |No| Boolean{"Expected: Boolean?"}
Boolean --> |Yes| CoerceBool["Coerce to bool"]
Boolean --> |No| String{"Expected: String?"}
String --> |Yes| CoerceString["Coerce to str"]
String --> |No| Float{"Expected: Float?"}
Float --> |Yes| CoerceFloat["Coerce to float"]
Float --> |No| Double{"Expected: Double?"}
Double --> |Yes| CoerceDouble["Coerce to float"]
Double --> |No| Fallback["Use raw value"]
CoerceInt --> CreateVariant
CoerceBool --> CreateVariant
CoerceString --> CreateVariant
CoerceFloat --> CreateVariant
CoerceDouble --> CreateVariant
Fallback --> CreateVariant
CreateVariant["Create OPC UA Variant"] --> Output["Write to Node"]
style CreateVariant fill:#bbf,stroke:#333
style Output fill:#9f9,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L395-L425)

## Synchronization Timing and Consistency
The system ensures data consistency through careful management of synchronization timing and state transitions. The coordination engine manages the timing of state updates to prevent race conditions and ensure atomic updates when necessary.

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> JobActive : set_job_active()
JobActive --> LayerProgress : update_layer_progress()
LayerProgress --> LayerComplete : set_recoater_layer_complete(True)
LayerComplete --> NextLayer : update_layer_progress()
NextLayer --> LayerProgress : set_recoater_ready_to_print(True)
LayerProgress --> JobComplete : current_layer == total_layers
JobComplete --> Idle : set_job_inactive()
JobActive --> Error : set_error_state()
LayerProgress --> Error : set_error_state()
LayerComplete --> Error : set_error_state()
Error --> Idle : clear_error_flags()
note right of JobActive
job_active = True
total_layers = N
current_layer = 0
end note
note right of LayerProgress
current_layer = n
recoater_ready_to_print = True
end note
note right of LayerComplete
recoater_layer_complete = True
recoater_ready_to_print = False
end note
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L376-L399)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L437-L455)

## Error Handling and Null Value Management
The system implements robust error handling for both connection issues and data validation problems. Null and undefined values are handled through default values and type coercion to ensure system stability.

```mermaid
flowchart TD
Operation[Operation Attempt] --> Success{"Operation Successful?"}
Success --> |Yes| Complete["Complete Operation"]
Success --> |No| ExceptionType{"Exception Type?"}
ExceptionType --> Connection{"Connection Error?"}
Connection --> |Yes| HandleConnection["Log Warning, Return False"]
ExceptionType --> ValidationError{"Validation Error?"}
ValidationError --> |Yes| HandleValidation["Log Error, Return False"]
ExceptionType --> CoercionError{"Coercion Error?"}
CoercionError --> |Yes| HandleCoercion["Log Warning, Use Raw Value"]
ExceptionType --> Unknown{"Unknown Error?"}
Unknown --> |Yes| HandleUnknown["Log Error with Traceback"]
HandleConnection --> Return
HandleValidation --> Return
HandleCoercion --> Retry["Retry with Raw Value"]
Retry --> WriteRaw["Write Raw Value"]
WriteRaw --> Return
HandleUnknown --> Return
Return["Return Result"] --> End
style Complete fill:#9f9,stroke:#333
style Return fill:#f99,stroke:#333
style WriteRaw fill:#ff9,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L372-L438)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L440-L466)

## Coordination Engine and Status Polling
The coordination engine integrates with the status polling system to provide real-time updates to the frontend while maintaining synchronization with the PLC through OPC UA variables.

```mermaid
graph TD
subgraph "Backend"
A[StatusPollingService]
B[RecoaterDataGatherer]
C[WebSocketManager]
end
subgraph "Hardware"
D[Recoater Client]
end
subgraph "Frontend"
E[WebSocket Clients]
end
A --> B
B --> D
D --> B
B --> A
A --> C
C --> E
A -.->|Polls every 1s| B
B -.->|Gathers data| D
C -.->|Broadcasts| E
style A fill:#bbf,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#9f9,stroke:#333
style E fill:#f9f,stroke:#333
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

## Schema Diagrams and Field Mappings
The following diagram illustrates the complete mapping between internal domain models and OPC UA variables, showing the bidirectional data flow and transformation logic.

```mermaid
erDiagram
MULTI_MATERIAL_JOB_STATE {
string job_id PK
int total_layers
int current_layer
bool is_active
JobStatus status
string error_message
int retry_count
}
DRUM_STATE {
int drum_id PK
string status
bool ready
int current_layer
int total_layers
string error_message
float last_update_time
}
COORDINATION_VARIABLES {
string name PK
string node_id
string data_type
Any initial_value
bool writable
}
MULTI_MATERIAL_JOB_STATE ||--o{ DRUM_STATE : "contains"
MULTI_MATERIAL_JOB_STATE }|--|| COORDINATION_VARIABLES : "maps to"
COORDINATION_VARIABLES {
"job_active" string
"total_layers" int
"current_layer" int
"recoater_ready_to_print" bool
"recoater_layer_complete" bool
"backend_error" bool
"plc_error" bool
}
note as FieldMapping
job_active: is_active
total_layers: total_layers
current_layer: current_layer
recoater_ready_to_print: status == RECOATER_ACTIVE
recoater_layer_complete: status == LAYER_COMPLETE
backend_error: error_message != ""
plc_error: received from PLC
end note
COORDINATION_VARIABLES .. FieldMapping
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py#L19-L111)
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L1-L590)