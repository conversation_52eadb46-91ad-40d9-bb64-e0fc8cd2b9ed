# Frontend API Client

<cite>
**Referenced Files in This Document**   
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [api.test.js](file://frontend/tests/api.test.js#L1-L55)
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js#L58-L94)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Client Configuration](#api-client-configuration)
3. [Service Wrapper Design Pattern](#service-wrapper-design-pattern)
4. [API Method Categories](#api-method-categories)
5. [Error Handling Strategy](#error-handling-strategy)
6. [Usage in Vue Components](#usage-in-vue-components)
7. [Code Examples](#code-examples)
8. [Interceptors and Logging](#interceptors-and-logging)

## Introduction

The frontend API client implemented in `api.js` serves as the single point of interaction between the Vue.js frontend and the backend REST API. This service wrapper encapsulates all axios calls to provide a consistent interface for communicating with the backend. The API client abstracts the complexity of direct HTTP requests from UI components, enabling cleaner separation of concerns and centralized management of API interactions.

The service exports an `apiService` object containing methods for all backend endpoints, organized by functional groups including axis movement, configuration management, print control, recoater operations, and status polling. This documentation details the implementation, usage patterns, and integration points of this critical service layer.

**Section sources**
- [api.js](file://frontend/src/services/api.js#L1-L50)

## API Client Configuration

The API client is configured with a centralized axios instance that establishes default settings for all HTTP requests. This configuration ensures consistency across the application and provides a single location for modifying request behavior.

```javascript
const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})
```

**Configuration Parameters:**
- **Base URL**: `/api/v1` - All endpoints are relative to this base path
- **Timeout**: 10,000 milliseconds (10 seconds) - Requests exceeding this duration are automatically aborted
- **Content-Type**: `application/json` - Default header for request content type

The base URL configuration enables the application to work with the backend API without requiring full absolute URLs in each request. The timeout setting prevents indefinite waiting for responses, which is crucial for maintaining application responsiveness in network-constrained environments.

For file upload operations, specific methods override the default configuration to accommodate larger payloads and longer processing times. For example, the `uploadCliFile` method sets a timeout of 300,000 milliseconds (5 minutes) to allow sufficient time for parsing multi-layer CLI files.

**Section sources**
- [api.js](file://frontend/src/services/api.js#L15-L25)

## Service Wrapper Design Pattern

The API client implements a service wrapper design pattern that encapsulates axios functionality behind a clean, application-specific interface. This pattern provides several key benefits:

1. **Abstraction**: Hides the implementation details of HTTP communication from consuming components
2. **Consistency**: Ensures uniform request/response handling across the application
3. **Maintainability**: Centralizes API interactions, making changes easier to implement
4. **Testability**: Facilitates mocking of API responses during unit testing

The service wrapper is implemented as a JavaScript object (`apiService`) containing methods for each API endpoint. Each method returns a Promise that resolves with the axios response, allowing consumers to use async/await or .then() syntax for handling responses.

```mermaid
flowchart TD
A["Vue Component"] --> B["apiService Method"]
B --> C["axios Instance"]
C --> D["Backend API"]
D --> C
C --> B
B --> A
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#f96,stroke:#333
style D fill:#9f9,stroke:#333
subgraph "Frontend"
A
B
C
end
subgraph "Backend"
D
end
```

**Diagram sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)

**Section sources**
- [api.js](file://frontend/src/services/api.js#L58-L587)

## API Method Categories

The API service organizes methods into logical categories based on functionality. Each category corresponds to a specific aspect of the recoater system control.

### Status and Configuration Methods

These methods handle system status queries and configuration management:

**Status Polling:**
- `getStatus()`: Retrieves current system status from `/status/`
- `getHealth()`: Performs health check via `/status/health`
- `restartServer()`: Restarts server with `/status/state?action=restart`
- `shutdownServer()`: Shuts down server with `/status/state?action=shutdown`

**Configuration Management:**
- `getConfig()`: Gets current configuration from `/config`
- `setConfig(config)`: Updates configuration via PUT to `/config`
- `getConfiguration()`: Gets detailed configuration from `/config/`
- `setConfiguration(config)`: Sets detailed configuration via PUT to `/config/`

### Axis Movement Controls

Methods for controlling the X, Z, and gripper axes:

- `getAxisStatus(axis)`: Gets status of specified axis
- `moveAxis(axis, motionData)`: Moves axis with specified parameters
- `homeAxis(axis, homingData)`: Homes specified axis
- `getAxisMotion(axis)`: Gets motion status of axis
- `cancelAxisMotion(axis)`: Cancels ongoing axis motion
- `setGripperState(gripperData)`: Sets gripper enabled/disabled state
- `getGripperState()`: Gets current gripper state

### Recoater Operations

Methods for controlling drum, blade, and leveler components:

**Drum Controls:**
- `getDrumMotion(drumId)`: Gets motion status of specified drum
- `setDrumMotion(drumId, motionData)`: Sets motion parameters for drum
- `cancelDrumMotion(drumId)`: Cancels drum motion
- `getDrumEjection(drumId, unit)`: Gets ejection pressure
- `setDrumEjection(drumId, ejectionData)`: Sets ejection pressure
- `getDrumSuction(drumId)`: Gets suction pressure
- `setDrumSuction(drumId, suctionData)`: Sets suction pressure

**Blade Controls:**
- `getBladeScrewsInfo(drumId)`: Gets blade screws information
- `getBladeScrewsMotion(drumId)`: Gets blade screws motion status
- `setBladeScrewsMotion(drumId, motionData)`: Sets blade screws motion
- `cancelBladeScrewsMotion(drumId)`: Cancels blade screws motion
- `getBladeScrew(drumId, screwId)`: Gets individual screw information
- `getBladeScrewMotion(drumId, screwId)`: Gets individual screw motion status
- `setBladeScrewMotion(drumId, screwId, motionData)`: Sets individual screw motion
- `cancelBladeScrewMotion(drumId, screwId)`: Cancels individual screw motion

**Leveler Controls:**
- `getLevelerPressure()`: Gets leveler pressure information
- `setLevelerPressure(target)`: Sets leveler pressure target
- `getLevelerSensor()`: Gets leveler sensor state

### Print Control Functions

Methods for managing print jobs and related operations:

**Job Management:**
- `startPrintJob()`: Starts print job via POST to `/print/job`
- `cancelPrintJob()`: Cancels print job via DELETE to `/print/job`
- `getPrintJobStatus()`: Gets current print job status

**Layer Management:**
- `getLayerParameters()`: Gets current layer parameters
- `setLayerParameters(parameters)`: Sets layer parameters via PUT
- `getLayerPreview()`: Gets layer preview image (returns blob)
- `getDrumGeometryPreview(drumId)`: Gets drum geometry preview image

**Multi-Material Job Management:**
- `startMultiMaterialJob(fileIds)`: Starts multi-material job
- `getMultiMaterialJobStatus()`: Gets multi-material job status
- `cancelMultiMaterialJob()`: Cancels multi-material job
- `clearErrorFlags()`: Clears error flags
- `getDrumStatus(drumId)`: Gets status of specific drum

### File Management Operations

Methods for handling file uploads, downloads, and processing:

**Geometry File Management:**
- `uploadDrumGeometry(drumId, file)`: Uploads geometry file to drum
- `downloadDrumGeometry(drumId)`: Downloads geometry file from drum
- `deleteDrumGeometry(drumId)`: Deletes geometry file from drum

**CLI File Management:**
- `uploadCliFile(file)`: Uploads and parses multi-layer CLI file
- `getCliLayerPreview(fileId, layerNum)`: Gets preview of specific CLI layer
- `sendCliLayerToDrum(fileId, layerNum, drumId)`: Sends CLI layer to drum
- `sendCliLayerRangeToDrum(fileId, drumId, startLayer, endLayer)`: Sends range of CLI layers to drum

**Section sources**
- [api.js](file://frontend/src/services/api.js#L58-L587)

## Error Handling Strategy

The API client implements a comprehensive error handling strategy that addresses both network-level failures and application-level errors. This strategy ensures reliable operation and provides meaningful feedback to users.

### Network Error Detection

The client detects various types of network failures through axios's built-in error handling:

- **Timeout errors**: Requests exceeding the 10-second timeout
- **Connection errors**: Failed to establish connection to server
- **Network interruptions**: Loss of network connectivity during request
- **Server unavailability**: Server not responding to requests

The response interceptor logs error details to the console, including the HTTP status code and error message:

```javascript
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)
```

### User Feedback Integration

While the API client itself focuses on request/response handling, it works in conjunction with the `ErrorDisplayPanel.vue` component to provide user feedback. The promise-based interface allows components to catch errors and display appropriate messages.

The test files demonstrate the expected error handling pattern:

```javascript
// In ConfigurationView.test.js
apiService.getConfiguration.mockRejectedValue(new Error('Network error'))
```

This pattern shows that when API calls fail, components should catch the rejected promise and update their state to reflect the error condition, typically by displaying an error message to the user.

### Consistent Error Propagation

All API methods follow a consistent pattern of returning promises that either resolve with successful responses or reject with error objects. This consistency enables uniform error handling across the application:

- Successful responses are resolved with the full axios response object
- Failed requests are rejected with the axios error object
- Components use try/catch with async/await or .catch() to handle errors

**Section sources**
- [api.js](file://frontend/src/services/api.js#L40-L50)
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js#L85-L94)

## Usage in Vue Components

The API service is designed to be consumed by Vue components throughout the application. Components import the service and call its methods to interact with the backend, handling the returned promises to update component state.

### Component Integration Pattern

Vue components typically follow this pattern when using the API service:

1. Import the apiService
2. Call API methods in lifecycle hooks or event handlers
3. Handle success and error cases
4. Update component state based on results

The `ConfigurationView.test.js` file provides insight into how components use the API service:

```javascript
// Component loads configuration on mount
expect(apiService.getConfiguration).toHaveBeenCalledOnce()

// Component displays loading state initially
const loadingState = wrapper.find('.loading-state')
expect(loadingState.exists()).toBe(true)

// Component displays error state when loading fails
const errorState = wrapper.find('.error-state')
expect(errorState.exists()).toBe(true)
```

This test code reveals that components:
- Call `getConfiguration()` when mounting
- Display a loading state during request processing
- Display an error state if the request fails

### State Management Integration

The API service works closely with the Vuex stores (`printJobStore.js` and `status.js`) to maintain application state. When API calls succeed, components typically commit mutations to update the store with new data.

For example, after calling `getPrintJobStatus()`, a component would update the print job store with the latest status information, triggering UI updates across the application.

**Section sources**
- [ConfigurationView.test.js](file://frontend/src/views/__tests__/ConfigurationView.test.js#L58-L94)

## Code Examples

### Basic API Call in a Component

```javascript
import { apiService } from '@/services/api.js'

export default {
  name: 'PrintView',
  data() {
    return {
      printStatus: null,
      loading: false,
      error: null
    }
  },
  async mounted() {
    await this.loadPrintStatus()
  },
  methods: {
    async loadPrintStatus() {
      this.loading = true
      this.error = null
      
      try {
        const response = await apiService.getPrintJobStatus()
        this.printStatus = response.data
      } catch (error) {
        this.error = 'Failed to load print status'
        console.error('Error fetching print status:', error)
      } finally {
        this.loading = false
      }
    },
    
    async startPrint() {
      try {
        await apiService.startPrintJob()
        await this.loadPrintStatus()
      } catch (error) {
        this.error = 'Failed to start print job'
      }
    }
  }
}
```

### File Upload with Progress Handling

```javascript
async uploadCliFile(file) {
  this.uploading = true
  this.uploadError = null
  
  try {
    const result = await apiService.uploadCliFile(file)
    this.fileId = result.data.file_id
    this.totalLayers = result.data.total_layers
    await this.showLayerPreview(1)
  } catch (error) {
    this.uploadError = 'Failed to upload CLI file'
    if (error.code === 'ECONNABORTED') {
      this.uploadError = 'Upload timed out. Please try again with a smaller file.'
    }
  } finally {
    this.uploading = false
  }
}
```

### Multi-Layer Print Job Control

```javascript
async sendLayerToDrum(layerNum, drumId) {
  this.sendingLayer = true
  this.sendError = null
  
  try {
    await apiService.sendCliLayerToDrum(this.fileId, layerNum, drumId)
    // Refresh job status after sending layer
    await this.updateJobStatus()
  } catch (error) {
    this.sendError = `Failed to send layer ${layerNum} to drum ${drumId}`
  } finally {
    this.sendingLayer = false
  }
}

async sendLayerRange(startLayer, endLayer, drumId) {
  try {
    await apiService.sendCliLayerRangeToDrum(
      this.fileId, 
      drumId, 
      startLayer, 
      endLayer
    )
    await this.updateJobStatus()
  } catch (error) {
    this.sendError = `Failed to send layers ${startLayer}-${endLayer} to drum ${drumId}`
  }
}
```

### Configuration Management

```javascript
async loadConfiguration() {
  this.loadingConfig = true
  this.configError = null
  
  try {
    const config = await apiService.getConfiguration()
    this.configuration = config
  } catch (error) {
    this.configError = 'Failed to load configuration'
  } finally {
    this.loadingConfig = false
  }
}

async saveConfiguration() {
  this.savingConfig = true
  this.configError = null
  
  try {
    await apiService.setConfiguration(this.configuration)
    this.configSaved = true
    setTimeout(() => {
      this.configSaved = false
    }, 3000)
  } catch (error) {
    this.configError = 'Failed to save configuration'
  } finally {
    this.savingConfig = false
  }
}
```

**Section sources**
- [api.js](file://frontend/src/services/api.js#L58-L587)

## Interceptors and Logging

The API client implements axios interceptors to provide logging and additional processing for all requests and responses.

### Request Interceptor

The request interceptor logs each outgoing request to the console:

```javascript
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)
```

This interceptor:
- Logs the HTTP method and URL of each request
- Allows modification of the request configuration before sending
- Handles request-level errors (e.g., network issues before request is sent)
- Returns the config object to continue the request flow

### Response Interceptor

The response interceptor logs successful responses and handles response errors:

```javascript
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)
```

This interceptor:
- Logs the HTTP status code and URL for successful responses
- Logs error details including status code and message for failed responses
- Provides a centralized location for response error handling
- Returns the response to continue the success flow or rejects the promise for errors

### Benefits of Interceptors

The interceptors provide several benefits:

1. **Debugging**: Comprehensive logging helps diagnose API communication issues
2. **Monitoring**: Visibility into all API traffic for performance analysis
3. **Consistency**: Uniform handling of requests and responses
4. **Extensibility**: Easy to add additional processing (e.g., authentication headers)

The logging is particularly valuable during development and troubleshooting, providing a clear audit trail of all API interactions.

**Section sources**
- [api.js](file://frontend/src/services/api.js#L30-L50)