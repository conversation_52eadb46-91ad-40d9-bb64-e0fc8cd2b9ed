# File Upload Column

<cite>
**Referenced Files in This Document**   
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [api.js](file://frontend/src/services/api.js)
- [print.py](file://backend/app/api/print.py)
- [FileUploadColumn.test.js](file://frontend/src/components/__tests__/FileUploadColumn.test.js)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [PrintView.vue](file://frontend/src/views/PrintView.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Overview](#component-overview)
3. [Core Functionality](#core-functionality)
4. [File Input and Validation](#file-input-and-validation)
5. [Upload Process and Backend Integration](#upload-process-and-backend-integration)
6. [User Interface and Feedback](#user-interface-and-feedback)
7. [Error Handling and Edge Cases](#error-handling-and-edge-cases)
8. [Unit Testing Strategy](#unit-testing-strategy)
9. [Common Issues and Optimization](#common-issues-and-optimization)
10. [Conclusion](#conclusion)

## Introduction

The **FileUploadColumn** component is a critical user interface element in the APIRecoater_Ethernet system that enables operators to manage geometry files for additive manufacturing processes. This component provides a streamlined interface for uploading, downloading, and deleting geometry files (PNG or CLI format) associated with specific recoater drums in a multi-material 3D printing system. The component plays a vital role in the print preparation workflow by facilitating the transfer of design data from the frontend interface to the backend control system.

This documentation provides a comprehensive analysis of the FileUploadColumn.vue component, detailing its architecture, functionality, integration points, and testing methodology. The component serves as a bridge between user interaction and backend services, handling file operations that are essential for configuring print jobs in the recoater system.

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

## Component Overview

The FileUploadColumn component is designed as a self-contained UI module that manages file operations for a specific drum in the recoater system. It follows the Vue 3 Composition API pattern with Pinia for state management, providing a reactive and maintainable architecture.

The component accepts three primary props:
- **drumId**: A numeric identifier (0, 1, or 2) specifying which drum the component manages
- **isConnected**: A boolean indicating the connection status to the recoater system
- **isLoading**: A boolean flag that controls the loading state of UI elements

The component emits three events:
- **upload**: Triggered when a file upload operation is initiated
- **download**: Triggered when a file download operation is requested
- **delete**: Triggered when a file deletion is confirmed

```mermaid
classDiagram
class FileUploadColumn {
+drumId : Number
+isConnected : Boolean
+isLoading : Boolean
-selectedFile : File|null
-fileInput : Ref
-printJobStore : Store
+handleFileSelect(event)
+clearFileSelection()
+formatFileSize(bytes)
+uploadFile()
+downloadFile()
+deleteFile()
}
class PrintJobStore {
+setLastUploadedFile(drumId, fileName, source)
+getLastUploadedFileName(drumId)
}
FileUploadColumn --> PrintJobStore : "uses"
FileUploadColumn --> ApiService : "triggers events handled by"
```

**Diagram sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L320)

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L1-L320)

## Core Functionality

The FileUploadColumn component provides essential file management capabilities for the recoater system, enabling users to interact with geometry files stored on specific drums. The core functionality revolves around three primary operations: upload, download, and delete, each implemented with appropriate validation and user feedback mechanisms.

The component is designed to manage files for a single drum, with the drumId prop determining which drum's files are being managed. This modular design allows multiple instances of the component to be used simultaneously, one for each drum in the system, as seen in the PrintView.vue implementation.

The component maintains internal state through Vue refs, including:
- **selectedFile**: Stores the currently selected file before upload
- **fileInput**: A reference to the file input element for programmatic control
- **printJobStore**: Access to the global Pinia store for tracking upload history

```mermaid
flowchart TD
Start([Component Initialized]) --> CheckConnection["Check isConnected prop"]
CheckConnection --> |Connected| EnableControls["Enable file operations"]
CheckConnection --> |Disconnected| ShowOverlay["Show disabled overlay"]
EnableControls --> HandleFile["Handle file selection"]
HandleFile --> ValidateFile["Validate file type and extension"]
ValidateFile --> |Valid| StoreFile["Store selected file"]
ValidateFile --> |Invalid| ShowError["Show error message"]
StoreFile --> DisplayInfo["Display file information"]
DisplayInfo --> WaitAction["Wait for user action"]
WaitAction --> Upload["Upload button clicked"]
WaitAction --> Download["Download button clicked"]
WaitAction --> Delete["Delete button clicked"]
Upload --> EmitUpload["Emit upload event"]
Download --> EmitDownload["Emit download event"]
Delete --> ConfirmDelete["Show confirmation dialog"]
ConfirmDelete --> |Confirmed| EmitDelete["Emit delete event"]
ConfirmDelete --> |Cancelled| NoAction["No action taken"]
```

**Diagram sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

## File Input and Validation

The file input mechanism in FileUploadColumn implements robust validation to ensure only appropriate files are processed. The component uses a standard HTML file input element with specific attributes to guide user selection:

```html
<input
  :id="`file-input-${drumId}`"
  type="file"
  ref="fileInput"
  @change="handleFileSelect"
  accept=".png,.cli,image/png,application/octet-stream"
  class="form-file-input"
  :disabled="!isConnected || isLoading"
/>
```

The validation process occurs in the handleFileSelect method, which performs two levels of validation:

1. **MIME Type Validation**: Checks if the file's MIME type is in the allowed list ['image/png', 'application/octet-stream']
2. **Extension Validation**: Verifies that the filename ends with either '.png' or '.cli' (case-insensitive)

```javascript
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // Validate file type
    const allowedTypes = ['image/png', 'application/octet-stream']
    const allowedExtensions = ['.png', '.cli']
    const fileName = file.name.toLowerCase()
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
    
    if (!allowedTypes.includes(file.type) && !hasValidExtension) {
      alert('Please select a PNG or CLI file.')
      clearFileSelection()
      return
    }

    selectedFile.value = file
  }
}
```

This dual-validation approach ensures compatibility across different browsers and file systems, as MIME type detection can be unreliable in some environments. The component also provides immediate visual feedback by displaying file information including the filename and formatted file size.

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

## Upload Process and Backend Integration

The file upload process in FileUploadColumn is designed to integrate seamlessly with the backend API through the apiService. When the upload button is clicked, the component emits an upload event containing the drumId and the selected file, which is then handled by the parent component (typically PrintView.vue).

The actual backend integration occurs in the PrintView.vue component, which listens for the upload event and calls the appropriate API service method:

```javascript
const handleDrumUpload = async ({ drumId, file }) => {
  isFileOperationLoading.value = true
  try {
    await apiService.uploadDrumGeometry(drumId, file)
    // Track the uploaded file
    printJobStore.setLastUploadedFile(drumId, file.name, 'direct')
    showMessage(`File uploaded successfully to drum ${drumId}`)
  } catch (error) {
    console.error('Failed to upload file:', error)
    showMessage('Failed to upload file: ' + (error.response?.data?.detail || error.message), true)
  } finally {
    isFileOperationLoading.value = false
  }
}
```

The apiService.uploadDrumGeometry method handles the actual HTTP request, using multipart form data to send the file to the backend:

```javascript
uploadDrumGeometry(drumId, file) {
  const formData = new FormData()
  formData.append('file', file)

  return apiClient.post(`/print/drums/${drumId}/geometry`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

On the backend, the print.py module receives this request at the `/print/drums/{drum_id}/geometry` endpoint, which processes the uploaded file and forwards it to the recoater system:

```python
@router.post("/drums/{drum_id}/geometry", response_model=FileUploadResponse)
async def upload_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    file: UploadFile = File(...),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> FileUploadResponse:
    # Read file data
    file_data = await file.read()

    # Upload to recoater
    result = recoater_client.upload_drum_geometry(
        drum_id=drum_id,
        file_data=file_data,
        content_type=file.content_type or "application/octet-stream"
    )
```

This architecture separates concerns effectively, with the FileUploadColumn handling UI interactions and validation, while the business logic and API communication are managed by dedicated services and the parent component.

```mermaid
sequenceDiagram
participant User as "User"
participant FileUploadColumn as "FileUploadColumn.vue"
participant PrintView as "PrintView.vue"
participant ApiService as "apiService.js"
participant Backend as "print.py"
participant Recoater as "Recoater System"
User->>FileUploadColumn : Selects file
FileUploadColumn->>FileUploadColumn : Validates file type
FileUploadColumn->>FileUploadColumn : Stores file reference
User->>FileUploadColumn : Clicks Upload
FileUploadColumn->>PrintView : Emits upload event
PrintView->>ApiService : Calls uploadDrumGeometry()
ApiService->>Backend : POST /print/drums/{id}/geometry
Backend->>Backend : Processes file upload
Backend->>Recoater : Sends file to recoater
Recoater-->>Backend : Confirmation
Backend-->>ApiService : Response
ApiService-->>PrintView : Promise resolution
PrintView->>PrintView : Updates store and UI
PrintView-->>User : Shows success message
```

**Diagram sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [print.py](file://backend/app/api/print.py#L1-L1012)
- [PrintView.vue](file://frontend/src/views/PrintView.vue#L1-L1463)

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [print.py](file://backend/app/api/print.py#L1-L1012)

## User Interface and Feedback

The FileUploadColumn component provides comprehensive user feedback through multiple visual and interactive elements. The UI is designed to be intuitive and informative, guiding users through the file management process.

### Connection Status and State Management

The component displays a disabled overlay when the system is not connected to the recoater, preventing invalid operations:

```html
<div v-if="!isConnected" class="disabled-overlay">
  <p class="disabled-text">Connect to recoater to manage files</p>
</div>
```

This overlay uses absolute positioning to cover the entire component content, ensuring users cannot interact with disabled controls.

### File Selection and Information Display

When a file is selected, the component provides immediate feedback by displaying file information:

```html
<div v-if="selectedFile" class="file-info">
  Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
</div>
```

The formatFileSize utility method converts raw byte values into human-readable formats (Bytes, KB, MB, GB):

```javascript
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
```

### Button States and Loading Feedback

The component implements sophisticated button state management, disabling buttons when operations are not possible:

```html
<button
  @click="uploadFile"
  :disabled="!isConnected || !selectedFile || isLoading"
  class="btn btn-primary"
>
  <span v-if="isLoading">Uploading...</span>
  <span v-else>Upload</span>
</button>
```

During operations, the isLoading prop controls visual feedback, changing button text to indicate the current operation (e.g., "Uploading..." instead of "Upload").

### Last Uploaded File Tracking

The component displays the last uploaded file for each drum by accessing the printJobStore:

```html
<div class="last-uploaded-file">
  {{ printJobStore.getLastUploadedFileName(drumId) }}
</div>
```

This provides users with immediate feedback about the current state of each drum, enhancing situational awareness.

```mermaid
flowchart TD
A[User Interface States] --> B[Connected State]
A --> C[Disconnected State]
A --> D[Loading State]
B --> E[Enabled Controls]
B --> F[File Selection Available]
B --> G[Upload/Download/Delete Enabled]
C --> H[Disabled Overlay Visible]
C --> I[All Controls Disabled]
C --> J[Connection Message Displayed]
D --> K[Button Text Changes]
D --> L[Loading Indicators]
D --> M[Operations Disabled]
E --> N[File Selected]
N --> O[File Info Displayed]
N --> P[Clear Button Available]
O --> Q[Upload Initiated]
Q --> R[Loading State Activated]
R --> S[Operation Complete]
S --> T[Success/Failure Feedback]
```

**Diagram sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)

## Error Handling and Edge Cases

The FileUploadColumn component implements comprehensive error handling for various edge cases and failure scenarios. The error handling strategy combines client-side validation with user feedback mechanisms to prevent invalid operations and inform users of issues.

### Client-Side Validation

The component performs immediate validation when a file is selected, preventing the upload of unsupported file types:

```javascript
if (!allowedTypes.includes(file.type) && !hasValidExtension) {
  alert('Please select a PNG or CLI file.')
  clearFileSelection()
  return
}
```

This validation uses both MIME type and file extension checking to ensure reliability across different browser implementations.

### Deletion Confirmation

To prevent accidental data loss, the component requires user confirmation before deleting files:

```javascript
const deleteFile = () => {
  if (!confirm(`Are you sure you want to delete the geometry file from drum ${props.drumId}?`)) {
    return
  }
  emit('delete', {
    drumId: props.drumId
  })
}
```

This confirmation dialog ensures that users are aware of the consequences of their actions.

### Parent-Level Error Handling

While FileUploadColumn handles client-side validation, actual API errors are managed by the parent component (PrintView.vue), which provides more comprehensive error feedback:

```javascript
const handleDrumUpload = async ({ drumId, file }) => {
  isFileOperationLoading.value = true
  try {
    await apiService.uploadDrumGeometry(drumId, file)
    printJobStore.setLastUploadedFile(drumId, file.name, 'direct')
    showMessage(`File uploaded successfully to drum ${drumId}`)
  } catch (error) {
    console.error('Failed to upload file:', error)
    showMessage('Failed to upload file: ' + (error.response?.data?.detail || error.message), true)
  } finally {
    isFileOperationLoading.value = false
  }
}
```

This separation of concerns allows FileUploadColumn to focus on UI validation while the parent component handles business logic errors and provides appropriate user feedback.

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [PrintView.vue](file://frontend/src/views/PrintView.vue#L1-L1463)

## Unit Testing Strategy

The FileUploadColumn component is thoroughly tested using Vitest and Vue Test Utils, with a comprehensive suite of tests covering all aspects of functionality. The test file FileUploadColumn.test.js implements a robust testing strategy that validates both the component's behavior and its integration points.

### Test Structure

The tests are organized into logical groups using describe blocks:

```javascript
describe('FileUploadColumn', () => {
  describe('Component Rendering', () => { /* ... */ })
  describe('File Selection', () => { /* ... */ })
  describe('Button States and Behavior', () => { /* ... */ })
  describe('Event Emission', () => { /* ... */ })
  describe('Clear Selection Button', () => { /* ... */ })
  describe('File Size Formatting', () => { /* ... */ })
  describe('Accessibility', () => { /* ... */ })
  describe('Prop Validation', () => { /* ... */ })
})
```

### Key Test Cases

#### Component Rendering Tests
- Verifies correct drum title display for different drum IDs
- Tests disabled overlay visibility based on connection status
- Validates file input attributes and button rendering

#### File Selection Tests
- Tests successful file selection and display
- Validates file type validation with both valid and invalid files
- Ensures proper handling of CLI files with correct extensions
- Tests file selection clearing functionality

#### Button State Tests
- Verifies button disabling when not connected
- Tests button disabling during loading states
- Ensures upload button requires a selected file
- Validates loading text display during operations

#### Event Emission Tests
- Confirms proper upload event emission with correct data
- Validates download event emission
- Tests delete event emission with confirmation
- Ensures delete event is not emitted when confirmation is cancelled

#### Utility Function Tests
- Tests file size formatting for various byte values
- Validates accessibility features like proper labels
- Confirms button styling hierarchy

```mermaid
flowchart TD
A[Unit Test Suite] --> B[Component Rendering]
A --> C[File Selection]
A --> D[Button States]
A --> E[Event Emission]
A --> F[Clear Selection]
A --> G[File Size Formatting]
A --> H[Accessibility]
A --> I[Prop Validation]
B --> B1[Drum title correct]
B --> B2[Disabled overlay]
B --> B3[File input attributes]
B --> B4[Button rendering]
C --> C1[Valid file selection]
C --> C2[Invalid file validation]
C --> C3[CLI file acceptance]
C --> C4[Selection clearing]
D --> D1[Disconnected state]
D --> D2[Loading state]
D --> D3[Upload requirements]
D --> D4[Loading text]
E --> E1[Upload event data]
E --> E2[Download event data]
E --> E3[Delete confirmation]
E --> E4[Cancel deletion]
F --> F1[Clear button visibility]
F --> F2[Clear functionality]
F --> F3[Post-upload clearing]
G --> G1[Bytes formatting]
G --> G2[KB formatting]
G --> G3[MB formatting]
G --> G4[GB formatting]
H --> H1[Form labels]
H --> H2[Button classes]
I --> I1[DrumId validation]
```

**Diagram sources**
- [FileUploadColumn.test.js](file://frontend/src/components/__tests__/FileUploadColumn.test.js#L1-L303)

**Section sources**
- [FileUploadColumn.test.js](file://frontend/src/components/__tests__/FileUploadColumn.test.js#L1-L303)

## Common Issues and Optimization

While the FileUploadColumn component is robust, several potential issues and optimization opportunities exist:

### Common Issues

**Large File Timeouts**: The current implementation uses a default timeout of 10 seconds for API calls, which may be insufficient for large CLI files. This could result in timeout errors during upload.

**Duplicate Submissions**: Although the component disables buttons during operations, network latency could potentially allow duplicate submissions if the UI state doesn't update quickly enough.

**Browser Compatibility**: File type detection can vary between browsers, potentially leading to inconsistent validation results.

### Optimization Strategies

**Chunked Uploads**: For large files, implementing chunked uploads would improve reliability and provide better progress feedback:

```javascript
// Pseudocode for chunked upload
const uploadInChunks = async (file, drumId) => {
  const chunkSize = 1024 * 1024 // 1MB chunks
  const chunks = Math.ceil(file.size / chunkSize)
  
  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize
    const end = Math.min(start + chunkSize, file.size)
    const chunk = file.slice(start, end)
    
    await apiService.uploadChunk(drumId, chunk, i, chunks)
    updateProgress(i / chunks)
  }
}
```

**Client-Side Preprocessing**: For CLI files, implementing client-side parsing could provide immediate feedback on file validity before upload:

```javascript
const validateCliFile = async (file) => {
  const text = await file.text()
  // Check for CLI file structure
  return text.startsWith(';') && text.includes('M106') && text.includes('G1')
}
```

**Enhanced Progress Feedback**: Implementing a progress bar would improve user experience during long uploads:

```html
<div v-if="uploadProgress > 0" class="progress-bar">
  <div class="progress-fill" :style="{ width: `${uploadProgress}%` }"></div>
  <span class="progress-text">{{ Math.round(uploadProgress) }}%</span>
</div>
```

**Error Recovery**: Implementing retry logic for failed uploads would improve reliability in unstable network conditions.

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue#L1-L317)
- [api.js](file://frontend/src/services/api.js#L1-L587)

## Conclusion

The FileUploadColumn component is a well-designed and thoroughly tested UI element that effectively manages file operations for the APIRecoater_Ethernet system. Its modular design, comprehensive validation, and clear separation of concerns make it a reliable component for handling critical file management tasks.

The component successfully integrates with the broader system architecture through well-defined events and service layers, ensuring maintainability and testability. Its user interface provides clear feedback and prevents invalid operations, enhancing the overall user experience.

While the current implementation is robust, opportunities for optimization exist, particularly in handling large files and providing more granular progress feedback. Implementing chunked uploads, client-side preprocessing, and enhanced error recovery would further improve the component's reliability and user experience.

The comprehensive test suite ensures the component's functionality is well-validated, providing confidence in its behavior across various scenarios. This thorough testing approach serves as a model for other components in the system.

Overall, the FileUploadColumn component exemplifies effective Vue component design, balancing functionality, usability, and maintainability in a critical part of the recoater system's user interface.