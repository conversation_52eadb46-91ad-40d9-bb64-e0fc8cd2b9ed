# Drum Control Component

<cite>
**Referenced Files in This Document**   
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)
- [api.js](file://frontend/src/services/api.js#L0-L587)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L0-L849)
- [DrumControl.test.js](file://frontend/src/components/__tests__/DrumControl.test.js#L0-L345)
- [status.js](file://frontend/src/stores/status.js#L0-L260)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Overview](#component-overview)
3. [UI Structure and Controls](#ui-structure-and-controls)
4. [Reactive Properties and State Management](#reactive-properties-and-state-management)
5. [Backend Integration via API Service](#backend-integration-via-api-service)
6. [WebSocket and Real-Time Status Updates](#websocket-and-real-time-status-updates)
7. [Event Handling and User Interaction Flow](#event-handling-and-user-interaction-flow)
8. [Testing Strategy and Component Validation](#testing-strategy-and-component-validation)
9. [Common Issues and Mitigation Techniques](#common-issues-and-mitigation-techniques)
10. [Performance Considerations](#performance-considerations)

## Introduction
The **Drum Control Component** is a critical user interface element in the APIRecoater_Ethernet system, responsible for managing the operation of recoater drums used in additive manufacturing processes. This component enables operators to control drum motion (speed, direction, distance), monitor real-time status, and adjust ejection and suction pressure settings. It integrates tightly with the backend through RESTful API calls and receives live updates via WebSocket connections. Designed as a reusable Vue.js component, it supports multiple drum instances (0–2) and provides visual feedback on system state, connectivity, and errors. This document provides a comprehensive analysis of its implementation, functionality, and integration patterns.

## Component Overview
The `DrumControl.vue` component serves as the primary interface for interacting with individual recoater drums in the system. It displays real-time operational data and provides controls for initiating and canceling drum motion, as well as setting pressure parameters for ejection and suction systems. The component is designed to be modular and reusable, accepting drum-specific data as props and emitting events to communicate actions and errors to parent components.

It plays a central role in the **Recoater View**, allowing precise control over drum subsystems during powder deposition cycles. The component reflects both the current state of the drum (position, running status) and allows configuration of motion parameters based on operational needs (relative, absolute, turns, speed, homing). Additionally, it supports pressure regulation for material handling, ensuring consistent layer formation.

```mermaid
graph TD
A[DrumControl.vue] --> B[UI Template]
A --> C[Script Logic]
A --> D[Styling]
B --> E[Status Display]
B --> F[Motion Controls]
B --> G[Pressure Controls]
C --> H[Reactive State]
C --> I[API Calls via apiService]
C --> J[Event Emission]
H --> K[motionParams]
H --> L[ejectionTarget]
H --> M[suctionTarget]
I --> N[setDrumMotion]
I --> O[cancelDrumMotion]
I --> P[setDrumEjection]
I --> Q[setDrumSuction]
J --> R[motion-started]
J --> S[motion-cancelled]
J --> T[pressure-set]
J --> U[error]
U --> V[Error Display & Auto-Clear]
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

## UI Structure and Controls
The component's user interface is organized into a structured card layout with distinct sections for status, motion control, and pressure management. Each section provides intuitive controls and real-time feedback.

### Status Display
The header displays the drum ID and a status indicator showing whether the drum is "Running" or "Stopped", using a color-coded dot (green for running, gray for stopped).

```html
<div class="status-indicator">
  <div :class="['status-dot', drumStatus?.running ? 'status-running' : 'status-stopped']"></div>
  <span class="status-text">{{ drumStatus?.running ? 'Running' : 'Stopped' }}</span>
</div>
```

### Motion Controls
The motion control section includes:
- **Mode selector**: Dropdown with options: Relative, Absolute, Turns, Speed, Homing
- **Speed input**: Numeric field (mm/s), required for all modes
- **Distance input**: Appears for Relative/Absolute modes
- **Turns input**: Appears for Turns mode
- **Action buttons**: "Start Motion" and "Cancel Motion"

Inputs and buttons are conditionally disabled based on connectivity (`connected`) and current motion state (`drumStatus.running`).

### Pressure Controls
Two pressure subsystems are managed:
- **Ejection Pressure**: Adjustable target with unit selection (Pascal or Bar)
- **Suction Pressure**: Adjustable target in Pascal

Each displays current and target values and includes a "Set" button to apply changes.

### Error Handling UI
An error message area appears when API calls fail, displaying the error detail and automatically clearing after 5 seconds.

```mermaid
flowchart TD
A["Drum Control UI"] --> B["Status Header"]
A --> C["Motion Control Group"]
C --> C1["Mode: Dropdown"]
C --> C2["Speed: Number Input"]
C --> C3["Distance/Turns: Conditional Input"]
C --> C4["Start/Cancel Buttons"]
A --> D["Pressure Controls"]
D --> D1["Ejection: Target + Unit + Set"]
D --> D2["Suction: Target + Set"]
A --> E["Error Message: Auto-Hide"]
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

## Reactive Properties and State Management
The component uses Vue 3’s Composition API with `ref()` to manage its internal reactive state. These properties are not derived from props but represent user input and transient UI state.

### Reactive State Variables
| Variable | Type | Initial Value | Purpose |
|--------|------|---------------|---------|
| `motionParams` | Object | `{mode: 'relative', speed: 30.0, distance: 100.0, turns: 1.0}` | Stores current motion settings |
| `ejectionTarget` | Ref<number> | 200.0 | Target ejection pressure value |
| `ejectionUnit` | Ref<string> | 'pascal' | Unit for ejection pressure |
| `suctionTarget` | Ref<number> | 2.0 | Target suction pressure value |
| `errorMessage` | Ref<string> | '' | Displays API error messages |
| `errorTimeout` | Ref | null | Tracks timeout for auto-clearing errors |

### Prop-Driven State
The component receives operational data via props from parent components or stores:
- `drumId`: Identifies the drum (0–2)
- `drumStatus`: Contains `running`, `position`, `circumference`
- `ejectionData`, `suctionData`: Current and target pressure values
- `connected`: Boolean indicating backend connectivity

This separation ensures the UI reflects real-time system state while allowing user input to be managed independently.

```mermaid
classDiagram
class DrumControl {
+drumId : number
+drumStatus : Object
+ejectionData : Object
+suctionData : Object
+connected : boolean
-motionParams : Ref~Object~
-ejectionTarget : Ref~number~
-ejectionUnit : Ref~string~
-suctionTarget : Ref~number~
-errorMessage : Ref~string~
-errorTimeout : Ref~number~
+startMotion()
+cancelMotion()
+setEjectionPressure()
+setSuctionPressure()
+showError()
}
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

## Backend Integration via API Service
The component interacts with the backend through the centralized `apiService` module, which abstracts Axios calls to the FastAPI server.

### API Endpoints Used
| Method | Purpose | Endpoint |
|-------|--------|----------|
| `setDrumMotion()` | Start drum motion | `POST /recoater/drums/{id}/motion` |
| `cancelDrumMotion()` | Stop ongoing motion | `DELETE /recoater/drums/{id}/motion` |
| `setDrumEjection()` | Set ejection pressure | `PUT /recoater/drums/{id}/ejection` |
| `setDrumSuction()` | Set suction pressure | `PUT /recoater/drums/{id}/suction` |

### Example: Starting Drum Motion
When the user clicks "Start Motion", the component constructs a payload based on the selected mode and calls the API:

```javascript
const startMotion = async () => {
  try {
    const motionData = {
      mode: motionParams.value.mode,
      speed: motionParams.value.speed
    };
    if (['relative', 'absolute'].includes(motionParams.value.mode)) {
      motionData.distance = motionParams.value.distance;
    } else if (motionParams.value.mode === 'turns') {
      motionData.turns = motionParams.value.turns;
    }
    await apiService.setDrumMotion(props.drumId, motionData);
    emit('motion-started', { drumId: props.drumId, motionData });
  } catch (error) {
    showError(error);
    emit('error', error);
  }
};
```

The backend processes this request in `recoater_controls.py`, validating input and forwarding commands to the physical recoater hardware via `RecoaterClient`.

```mermaid
sequenceDiagram
participant User
participant DrumControl
participant apiService
participant Backend
participant Hardware
User->>DrumControl : Click "Start Motion"
DrumControl->>DrumControl : Build motionData object
DrumControl->>apiService : setDrumMotion(drumId, motionData)
apiService->>Backend : POST /recoater/drums/{id}/motion
Backend->>Hardware : Execute motion command
Hardware-->>Backend : Response
Backend-->>apiService : 200 OK + data
apiService-->>DrumControl : Resolve promise
DrumControl->>DrumControl : Emit 'motion-started'
DrumControl-->>User : UI updates (Running state)
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)
- [api.js](file://frontend/src/services/api.js#L0-L587)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L0-L849)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)
- [api.js](file://frontend/src/services/api.js#L0-L587)

## WebSocket and Real-Time Status Updates
While `DrumControl.vue` does not directly manage WebSocket connections, it relies on real-time data updates propagated through the Vuex-like Pinia store (`status.js`). The store maintains a WebSocket connection and broadcasts system state changes to all components.

### Status Store Integration
The `useStatusStore` manages:
- WebSocket lifecycle (connect, disconnect, reconnect)
- Subscription to data types based on current page
- State updates for drum, axis, leveler, and print data

When the store receives a message like:
```json
{
  "type": "status_update",
  "drum_data": {
    "0": { "running": true, "position": 150.2 }
  }
}
```
It updates `drumData.value`, which flows down to `DrumControl` via props when the parent component re-renders.

### Selective Data Subscriptions
To reduce network load, the store only requests relevant data types per page:
- **Recoater Page**: Subscribes to `drum`, `leveler`
- **Print Page**: Subscribes to `print`, `drum`
- **Status Page**: Only `status` by default

This ensures efficient updates without overloading the UI or network.

```mermaid
sequenceDiagram
participant WS[WebSocket]
participant Store[status.js]
participant Parent
participant DrumControl
WS->>Store : {type : "status_update", drum_data : {...}}
Store->>Store : updateDrumData(data)
Store->>Parent : State change detected
Parent->>DrumControl : Update props (drumStatus, ejectionData, etc.)
DrumControl->>DrumControl : Re-render UI
```

**Diagram sources**
- [status.js](file://frontend/src/stores/status.js#L0-L260)
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L0-L260)

## Event Handling and User Interaction Flow
The component uses a clean event-driven architecture, emitting standardized events to notify parent components of user actions and system responses.

### Key Event Handlers
| Handler | Trigger | Emitted Events | Purpose |
|-------|--------|----------------|---------|
| `startMotion()` | "Start Motion" click | `motion-started`, `success`, `error` | Initiate drum movement |
| `cancelMotion()` | "Cancel Motion" click | `motion-cancelled`, `success`, `error` | Stop current motion |
| `setEjectionPressure()` | "Set" in ejection section | `pressure-set`, `success`, `error` | Update ejection target |
| `setSuctionPressure()` | "Set" in suction section | `pressure-set`, `success`, `error` | Update suction target |

### Error Handling Flow
When an API call fails:
1. The error is caught in the handler
2. `showError()` extracts the message (from `error.response.data.detail`)
3. `errorMessage` is set, triggering UI display
4. A 5-second timer is started to auto-clear the message
5. An `error` event is emitted for global handling

This prevents error accumulation and provides immediate feedback.

```mermaid
flowchart TD
A[User Action] --> B{API Call}
B --> C[Success]
C --> D[Emit Success Event]
C --> E[Update UI State]
B --> F[Failure]
F --> G[Extract Error Message]
G --> H[Display in UI]
H --> I[Start 5s Timer]
I --> J{Timer Expired?}
J --> |Yes| K[Clear Message]
J --> |No| L[Wait]
G --> M[Emit Error Event]
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue#L0-L303)

## Testing Strategy and Component Validation
The component is thoroughly tested using Vitest and Vue Test Utils, with a focus on interaction, API integration, and error handling.

### Test Categories
| Category | Key Tests |
|--------|---------|
| **Rendering** | Correct drum ID, status display, pressure values |
| **Controls** | Input visibility, button enable/disable logic |
| **API Calls** | Correct parameters passed to `apiService` |
| **Events** | Proper emission of `motion-started`, `error`, etc. |
| **Error Handling** | Message display, auto-clear, event emission |

### Mocking Strategy
The `apiService` is mocked to simulate API responses:
```javascript
vi.mock('../../services/api', () => ({
  default: {
    setDrumMotion: vi.fn(),
    cancelDrumMotion: vi.fn(),
    setDrumEjection: vi.fn(),
    setDrumSuction: vi.fn()
  }
}))
```

This allows testing component logic without requiring a running backend.

### Example: Testing Motion Start
```javascript
it('calls setDrumMotion API when start motion is clicked', async () => {
  apiService.setDrumMotion.mockResolvedValue({ data: { status: 'motion_created' } })

  await wrapper.find('select').setValue('relative')
  await wrapper.findAll('input')[0].setValue(50.0) // Speed
  await wrapper.findAll('input')[1].setValue(100.0) // Distance

  await wrapper.find('button:contains("Start Motion")').trigger('click')

  expect(apiService.setDrumMotion).toHaveBeenCalledWith(0, {
    mode: 'relative',
    speed: 50.0,
    distance: 100.0
  })
})
```

The test verifies that user input is correctly mapped to API parameters.

```mermaid
graph TD
A[Unit Test] --> B[Mount Component]
B --> C[Set Props]
C --> D[Simulate User Input]
D --> E[Trigger Action]
E --> F[Verify API Call]
F --> G[Check Emitted Events]
G --> H[Assert UI State]
```

**Diagram sources**
- [DrumControl.test.js](file://frontend/src/components/__tests__/DrumControl.test.js#L0-L345)

**Section sources**
- [DrumControl.test.js](file://frontend/src/components/__tests__/DrumControl.test.js#L0-L345)

## Common Issues and Mitigation Techniques
Despite robust design, several issues can arise in production use.

### Command Latency
Due to network or hardware delays, there may be a lag between command issuance and execution.

**Mitigation**:
- Disable controls during motion (`drumStatus.running`)
- Use optimistic UI updates where appropriate
- Implement command timeouts in the backend

### State Inconsistency
The UI may display stale data if WebSocket messages are delayed or dropped.

**Mitigation**:
- Re-fetch status after critical operations
- Use `lastUpdate` timestamp to detect stale data
- Implement heartbeat mechanism in WebSocket

### Error Message Overload
Multiple rapid errors could flood the UI.

**Mitigation**:
- Auto-clear messages after 5 seconds
- Debounce error display
- Aggregate similar errors

### Connection Loss
Network interruptions can disconnect the WebSocket.

**Mitigation**:
- Automatic reconnection with exponential backoff
- Visual indicator of connection status
- Queue critical commands for retry

## Performance Considerations
The component is designed for efficiency, but several factors impact performance.

### Frequent Status Polling
While the component uses WebSocket for real-time updates, unnecessary polling should be avoided.

**Optimization**:
- Subscribe only to needed data types per page
- Use selective reactivity (props update only when changed)
- Avoid deep watchers on large objects

### Efficient Re-Rendering
Vue’s reactivity system ensures minimal re-renders, but best practices include:
- Using `v-if` instead of `v-show` for conditional inputs
- Avoiding inline object creation in templates
- Using `key` attributes for dynamic lists

### Memory Management
The component does not create memory leaks, but:
- Clear timeouts in `beforeUnmount` if needed
- Remove WebSocket event listeners on disconnect
- Avoid storing large data in component state

The current implementation is efficient, with lightweight computed props and minimal DOM updates.