# Real-Time Communication System

<cite>
**Referenced Files in This Document**   
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websockets.py](file://backend/app/websockets.py) - *Extracted from main.py in commit 8c43f1b*
- [main.py](file://backend/app/main.py) - *Refactored to import websockets.py in commit 8c43f1b*
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [api.js](file://frontend/src/services/api.js)
- [status.js](file://frontend/src/stores/status.js)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the extraction of WebSocket functionality from main.py to websockets.py
- Added new section on WebSocketHandler class and its integration
- Updated architecture overview diagram to include WebSocketHandler component
- Updated section sources to reflect file refactoring
- Added references to new websockets.py file throughout documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [WebSocket Connection Management](#websocket-connection-management)
4. [Status Polling and Broadcasting](#status-polling-and-broadcasting)
5. [Frontend WebSocket Integration](#frontend-websocket-integration)
6. [Message Format and Data Schema](#message-format-and-data-schema)
7. [UI Components and Reactive Updates](#ui-components-and-reactive-updates)
8. [Connection Resilience and Reconnection](#connection-resilience-and-reconnection)
9. [Performance and Optimization](#performance-and-optimization)
10. [Sequence Diagram: Real-Time Status Flow](#sequence-diagram-real-time-status-flow)

## Introduction
The Real-Time Communication System in APIRecoater_Ethernet enables bidirectional, low-latency communication between the backend and frontend. It uses WebSocket technology to maintain persistent connections, allowing the backend to push status updates to the frontend as they occur. This system is critical for monitoring the recoater's state, including device positions, error codes, and operational status. The architecture is designed for efficiency, resilience, and scalability, supporting multiple concurrent clients with subscription-based data filtering.

## Architecture Overview
The real-time communication system follows a publish-subscribe pattern with three main components:
1. **WebSocketHandler**: Handles WebSocket connections and message processing (newly separated module)
2. **WebSocketManager**: Manages client connections and message broadcasting
3. **StatusPoller**: Periodically polls device status and triggers updates
4. **Frontend Store**: Subscribes to updates and maintains reactive state

``mermaid
graph TB
subgraph Backend
SP[StatusPollingService]
WM[WebSocketConnectionManager]
WH[WebSocketHandler]
DG[RecoaterDataGatherer]
RC[RecoaterClient]
end
subgraph Frontend
WS[WebSocket Client]
ST[Status Store]
UI[UI Components]
end
SP --> |polls| RC
SP --> |broadcasts| WM
WH --> |handles| WM
WM --> |sends| WS
WS --> |updates| ST
ST --> |reacts to| UI
UI --> |triggers| ST
ST --> |sends subscription| WS
style SP fill:#4CAF50,stroke:#388E3C
style WM fill:#2196F3,stroke:#1976D2
style WH fill:#FF5722,stroke:#D84315
style DG fill:#FF9800,stroke:#F57C00
style RC fill:#9C27B0,stroke:#7B1FA2
style WS fill:#00BCD4,stroke:#0097A7
style ST fill:#E91E63,stroke:#C2185B
style UI fill:#FF5722,stroke:#D84315
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websockets.py](file://backend/app/websockets.py) - *Newly extracted module*
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [status.js](file://frontend/src/stores/status.js)

## WebSocket Connection Management
The WebSocketConnectionManager class handles the lifecycle of WebSocket connections, including connection establishment, disconnection, subscription management, and message broadcasting.

``mermaid
classDiagram
class WebSocketConnectionManager {
+List[WebSocket] active_connections
+Dict[WebSocket, Set[str]] connection_subscriptions
+connect(websocket : WebSocket) void
+disconnect(websocket : WebSocket) void
+update_subscription(websocket : WebSocket, data_types : List[str]) void
+broadcast(message : Dict[str, Any]) void
+get_required_data_types() Set[str]
+connection_count() int
+has_connections() bool
}
WebSocketConnectionManager --> "1" "0..*" WebSocket : manages
```

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L146)

### WebSocket Handler Implementation
The WebSocketHandler class has been extracted into its own module (websockets.py) to improve code organization and separation of concerns. This handler manages the WebSocket endpoint and processes incoming messages for subscription updates.

``mermaid
classDiagram
class WebSocketHandler {
-WebSocketConnectionManager websocket_manager
+websocket_endpoint(websocket : WebSocket) void
+handle_websocket_message(websocket : WebSocket, message_text : str) void
}
WebSocketHandler --> WebSocketConnectionManager : uses
```

**Section sources**
- [websockets.py](file://backend/app/websockets.py#L1-L34) - *Newly created file*
- [main.py](file://backend/app/main.py#L47-L48) - *Updated to use WebSocketHandler*

### Connection Lifecycle
The connection manager handles the complete WebSocket lifecycle:
- **connect**: Accepts new connections and initializes subscription tracking
- **disconnect**: Removes connections and cleans up subscription data
- **broadcast**: Sends messages to all active connections with subscription filtering

### Subscription Management
Clients can subscribe to specific data types based on their current view:
- `status`: Basic system status (always subscribed)
- `axis`: Axis position and motion data
- `drum`: Drum configuration and motion data
- `leveler`: Leveler pressure and sensor data
- `print`: Print job parameters and status

The manager filters messages based on each client's subscriptions, ensuring efficient bandwidth usage.

## Status Polling and Broadcasting
The StatusPollingService runs a background loop that periodically polls the recoater hardware and broadcasts updates through the WebSocket layer.

``mermaid
classDiagram
class StatusPollingService {
-WebSocketConnectionManager websocket_manager
-RecoaterDataGatherer data_gatherer
-float poll_interval
-Task polling_task
-bool _running
+start() void
+stop() void
+_polling_loop() void
+_poll_and_broadcast() void
+_broadcast_connection_error(error : Exception) void
+is_running() bool
+update_poll_interval(interval : float) void
}
StatusPollingService --> WebSocketConnectionManager : uses
StatusPollingService --> RecoaterDataGatherer : uses
StatusPollingService --> "1" "0..*" Task : manages
```

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L124)

### Polling Loop
The polling service operates on a configurable interval (default: 1 second) with the following workflow:
1. Check if polling is already running
2. Execute `_poll_and_broadcast` method
3. Handle connection and API errors
4. Sleep for configured interval
5. Repeat

### Data Gathering Process
The service uses RecoaterDataGatherer to collect data efficiently:
- **Concurrent execution**: Multiple data types are gathered simultaneously using asyncio
- **Conditional gathering**: Only requested data types are collected based on active subscriptions
- **Error isolation**: Failures in one data type don't prevent collection of others

``mermaid
flowchart TD
Start([Polling Loop Start]) --> CheckRunning{Polling Running?}
CheckRunning --> |No| Initialize[Initialize Polling Task]
CheckRunning --> |Yes| Continue[Continue Loop]
Continue --> PollData[_poll_and_broadcast]
PollData --> HasConnections{Active Connections?}
HasConnections --> |No| Sleep[Sleep Interval]
HasConnections --> |Yes| GetSubscriptions[Get Required Data Types]
GetSubscriptions --> GatherData[Gather All Required Data]
GatherData --> ConstructMessage[Construct Status Message]
ConstructMessage --> Broadcast[WebSocket Broadcast]
Broadcast --> Sleep
Sleep --> Continue
PollData --> ConnectionError{Connection Error?}
ConnectionError --> |Yes| BroadcastError[_broadcast_connection_error]
BroadcastError --> Sleep
ConnectionError --> |No| Continue
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L73-L110)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L150-L229)

## Frontend WebSocket Integration
The frontend implements WebSocket communication through the status store, which manages connection state and data updates.

``mermaid
classDiagram
class useStatusStore {
-ref isConnected
-ref recoaterStatus
-ref axisData
-ref drumData
-ref levelerData
-ref printData
-ref lastError
-ref lastUpdate
-ref websocket
-ref currentPage
-ref subscribedDataTypes
+updateStatus(statusData) void
+updateAxisData(newAxisData) void
+updateDrumData(data) void
+updateLevelerData(data) void
+updatePrintData(data) void
+setError(error) void
+setConnectionStatus(connected) void
+setCurrentPage(page) void
+updateDataSubscriptions() void
+setManualSubscriptions(dataTypes) void
+fetchStatus() Promise
+connectWebSocket() void
+disconnectWebSocket() void
}
useStatusStore --> "1" "0..*" WebSocket : manages
useStatusStore --> apiService : uses
```

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L1-L259)

### Connection Management
The status store handles WebSocket connection with the following event handlers:
- **onopen**: Sets connection status and sends initial subscription
- **onmessage**: Parses incoming messages and updates appropriate store properties
- **onclose**: Sets disconnected status and schedules reconnection
- **onerror**: Logs errors and sets error state

### Subscription Strategy
The store implements page-aware subscriptions:
- Always subscribes to basic status data
- Adds page-specific data types based on current view
- Updates subscriptions when navigating between pages
- Sends subscription updates to backend via WebSocket

## Message Format and Data Schema
The system uses a standardized JSON message format for status updates and error alerts.

### Status Update Message
```json
{
  "type": "status_update",
  "data": {
    "state": "ready",
    "timestamp": "2025-07-09T14:30:00Z"
  },
  "axis_data": {
    "x": {"position": 100.5, "running": false},
    "z": {"position": 50.2, "running": true},
    "gripper": {"enabled": true}
  },
  "drum_data": {
    "0": {
      "info": {"id": 0, "name": "Drum A"},
      "motion": {"mode": "position", "speed": 100, "distance": 500},
      "ejection": {"target": 100000, "current": 98000},
      "suction": {"target": 80000, "current": 82000},
      "blade_screws": {"count": 12, "positions": [0.1, 0.2, 0.3]},
      "blade_motion": {"mode": "position", "distance": 10}
    }
  },
  "leveler_data": {
    "pressure": {"target": 150000, "current": 148000},
    "sensor": {"detected": true}
  },
  "print_data": {
    "layer_parameters": {"filling_id": 1, "speed": 200},
    "job_status": {"state": "idle", "progress": 0}
  },
  "timestamp": 1745892600.123
}
```

### Error Alert Message
```json
{
  "type": "connection_error",
  "error": "Failed to connect to recoater hardware: Connection timeout",
  "timestamp": 1745892600.123
}
```

### Message Construction Process
``mermaid
flowchart TD
Start([Data Gathering Complete]) --> ConstructMessage[construct_status_message]
ConstructMessage --> ExtractStatus[Extract main status]
ExtractStatus --> AddAxisData{Include axis_data?}
AddAxisData --> |Yes| GetAxisData[Get axis_data from gathered_data]
AddAxisData --> |No| SetAxisNull[axis_data = null]
GetAxisData --> MergeAxis[Merge into message]
SetAxisNull --> MergeAxis
MergeAxis --> AddDrumData{Include drum_data?}
AddDrumData --> |Yes| GetDrumData[Get drum_data from gathered_data]
AddDrumData --> |No| SetDrumNull[drum_data = null]
GetDrumData --> MergeDrum[Merge into message]
SetDrumNull --> MergeDrum
MergeDrum --> AddLevelerData{Include leveler_data?}
AddLevelerData --> |Yes| GetLevelerData[Get leveler_data from gathered_data]
AddLevelerData --> |No| SetLevelerNull[leveler_data = null]
GetLevelerData --> MergeLeveler[Merge into message]
SetLevelerNull --> MergeLeveler
MergeLeveler --> AddPrintData{Include print_data?}
AddPrintData --> |Yes| GetPrintData[Get print_data from gathered_data]
AddPrintData --> |No| SetPrintNull[print_data = null]
GetPrintData --> MergePrint[Merge into message]
SetPrintNull --> MergePrint
MergePrint --> AddTimestamp[Add timestamp]
AddTimestamp --> ReturnMessage[Return constructed message]
```

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L200-L229)

## UI Components and Reactive Updates
UI components reactively display live device states by subscribing to the status store.

### StatusIndicator Component
The StatusIndicator.vue component provides visual feedback on system connectivity and health.

```vue
<template>
  <div class="status-indicator">
    <div 
      class="status-dot" 
      :class="statusClass"
      :title="statusTooltip"
    ></div>
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>
```

``mermaid
stateDiagram-v2
[*] --> Disconnected
Disconnected --> Connected : WebSocket connected
Connected --> Error : Backend reports error
Error --> Connected : Error resolved
Connected --> Disconnected : WebSocket disconnects
Error --> Disconnected : WebSocket disconnects
state Disconnected {
[*] --> DotRed
[*] --> TextDisconnected
[*] --> TooltipLost
}
state Connected {
[*] --> DotGreen
[*] --> TextConnected
[*] --> TooltipOperational
}
state Error {
[*] --> DotOrange
[*] --> TextError
[*] --> TooltipWithError
}
```

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L99)

### Reactive Update Flow
When a WebSocket message is received:
1. The status store updates its reactive properties
2. Vue's reactivity system detects changes
3. Dependent components automatically re-render
4. UI reflects current device state

This eliminates the need for manual DOM manipulation and ensures consistent state across the application.

## Connection Resilience and Reconnection
The system implements robust connection resilience with automatic reconnection capabilities.

### Reconnection Strategy
The frontend implements a 3-second retry mechanism:
- On WebSocket close, the connection status is updated
- After 3 seconds, a new connection attempt is made
- The process repeats until successful connection

``mermaid
sequenceDiagram
participant Frontend
participant Backend
participant StatusPoller
Frontend->>Backend : Connect WebSocket
Backend-->>Frontend : Connection Established
StatusPoller->>Backend : Poll Device Status
Backend->>Frontend : Broadcast Status Update
Note over Frontend,Backend : Connection stable
Backend->>Frontend : WebSocket Close (network issue)
Frontend->>Frontend : Set disconnected status
Frontend->>Frontend : Schedule reconnection in 3s
Note over Frontend : Wait 3 seconds
Frontend->>Backend : Reconnection Attempt
Backend-->>Frontend : Connection Re-established
Frontend->>Backend : Send subscription update
StatusPoller->>Backend : Poll Device Status
Backend->>Frontend : Broadcast Status Update
```

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L200-L212)

### Error Handling
The system handles various error scenarios:
- **Connection errors**: Reported via dedicated error messages
- **Message parsing errors**: Caught and logged without crashing
- **API errors**: Handled at the data gathering layer
- **Polling failures**: Isolated to prevent system-wide impact

## Performance and Optimization
The real-time communication system incorporates several performance optimizations.

### Connection Management
- **Connection pooling**: Reuses WebSocket connections
- **Subscription filtering**: Reduces bandwidth by sending only relevant data
- **Batched updates**: Combines multiple status changes into single messages

### Data Efficiency
- **Conditional data gathering**: Only collects data types with active subscribers
- **Concurrent data collection**: Uses asyncio for parallel API calls
- **Minimal message size**: JSON format with only necessary fields

### High-Frequency Update Handling
The system is designed to handle high-frequency updates efficiently:
- **Message deduplication**: Not explicitly implemented but naturally handled by Vue's reactivity
- **Update throttling**: Configurable polling interval prevents overwhelming the system
- **Resource cleanup**: Proper cleanup of disconnected clients and tasks

## Sequence Diagram: Real-Time Status Flow
The following sequence diagram illustrates the complete flow from device status change to UI refresh.

``mermaid
sequenceDiagram
participant Device
participant RecoaterClient
participant StatusPoller
participant DataGatherer
participant WebSocketManager
participant WebSocketHandler
participant FrontendWS
participant StatusStore
participant StatusIndicator
loop Polling Interval
StatusPoller->>StatusPoller : _polling_loop()
StatusPoller->>StatusPoller : _poll_and_broadcast()
StatusPoller->>DataGatherer : gather_all_data()
DataGatherer->>RecoaterClient : get_state()
RecoaterClient->>Device : OPCUA Read
Device-->>RecoaterClient : State Data
RecoaterClient-->>DataGatherer : State Response
DataGatherer->>RecoaterClient : get_drum(), get_leveler_pressure(), etc.
RecoaterClient->>Device : Multiple OPCUA Reads
Device-->>RecoaterClient : Multiple Responses
RecoaterClient-->>DataGatherer : Multiple Responses
DataGatherer-->>StatusPoller : All Data
StatusPoller->>DataGatherer : construct_status_message()
DataGatherer-->>StatusPoller : Status Message
StatusPoller->>WebSocketManager : broadcast(message)
WebSocketManager->>WebSocketHandler : Handle broadcast
WebSocketHandler->>FrontendWS : Send JSON Message
FrontendWS->>StatusStore : onmessage(event)
StatusStore->>StatusStore : Parse Message
StatusStore->>StatusStore : updateStatus(), updateAxisData(), etc.
StatusStore->>StatusIndicator : State Changed
StatusIndicator->>StatusIndicator : Re-render Component
end
Note over StatusPoller,WebSocketManager : Background polling every 1 second
Note over StatusStore,StatusIndicator : Reactive update on message receipt
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [websockets.py](file://backend/app/websockets.py) - *Newly extracted module*
- [status.js](file://frontend/src/stores/status.js)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)