# Frontend Components

<cite>
**Referenced Files in This Document**   
- [main.js](file://frontend/src/main.js)
- [App.vue](file://frontend/src/App.vue)
- [router/index.js](file://frontend/src/router/index.js)
- [stores/status.js](file://frontend/src/stores/status.js)
- [services/api.js](file://frontend/src/services/api.js)
- [components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [components/FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The frontend of the APIRecoater_Ethernet system is a Vue.js application that provides a human-machine interface (HMI) for controlling a recoater system used in additive manufacturing. This document details the implementation of key frontend components that manage hardware subsystems such as drums, hoppers, and levelers, as well as orchestrate multi-layer print jobs. The application uses Vue 3 with the Composition API, Pinia for state management, and communicates with a backend via REST API and WebSocket for real-time status updates. The UI is organized into views that compose reusable components, which bind to shared stores for consistent state across the application.

## Project Structure
The frontend directory follows a standard Vue.js project structure with clear separation of concerns. Components are organized by feature, with views composing these components into full pages. Services handle API communication, while stores manage application state. The router defines navigation between different views.

```mermaid
graph TB
subgraph "Frontend Structure"
A[frontend] --> B[src]
B --> C[components]
B --> D[views]
B --> E[stores]
B --> F[services]
B --> G[router]
C --> H[DrumControl.vue]
C --> I[HopperControl.vue]
C --> J[LevelerControl.vue]
C --> K[MultiLayerJobControl.vue]
C --> L[FileUploadColumn.vue]
D --> M[PrintView.vue]
D --> N[RecoaterView.vue]
E --> O[printJobStore.js]
E --> P[status.js]
F --> Q[api.js]
G --> R[index.js]
end
```

**Diagram sources**
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [frontend/src/components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [frontend/src/components/FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [frontend/src/views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [frontend/src/views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)
- [frontend/src/router/index.js](file://frontend/src/router/index.js)

**Section sources**
- [frontend/src/main.js](file://frontend/src/main.js)
- [frontend/src/App.vue](file://frontend/src/App.vue)

## Core Components
The frontend application is built around several core components that manage specific hardware subsystems and workflows. The DrumControl, HopperControl, and LevelerControl components provide direct control over their respective hardware units, exposing parameters such as motion, pressure, and position. The MultiLayerJobControl component orchestrates complex multi-material print jobs by coordinating the upload and execution of CLI files across multiple drums. The FileUploadColumn component handles the ingestion of CLI files from the command line interface. These components are composed within views such as PrintView and RecoaterView, which provide the complete user interface for different operational modes.

**Section sources**
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [frontend/src/components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [frontend/src/components/FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [frontend/src/views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [frontend/src/views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)

## Architecture Overview
The frontend architecture follows a component-based design with centralized state management. The application is initialized in main.js, which creates the Vue app, registers Pinia for state management, and installs the router. The App.vue component serves as the root layout, providing navigation and a header with a status indicator. Views are routed via Vue Router, with each view importing and composing relevant components. State is managed in Pinia stores, with status.js handling real-time system status via WebSocket, and printJobStore.js managing multi-layer job state. Components interact with the backend through api.js, which exports a service object with methods for each API endpoint.

```mermaid
graph TD
A[User Interaction] --> B[Vue Component]
B --> C[Pinia Store]
C --> D[API Service]
D --> E[Backend REST API]
D --> F[WebSocket]
F --> C
E --> D
C --> B
B --> A
subgraph "Frontend"
B
C
D
end
subgraph "Backend"
E
F
end
```

**Diagram sources**
- [frontend/src/main.js](file://frontend/src/main.js)
- [frontend/src/App.vue](file://frontend/src/App.vue)
- [frontend/src/router/index.js](file://frontend/src/router/index.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

## Detailed Component Analysis

### DrumControl Analysis
The DrumControl component manages a single drum unit in the recoater system. It displays real-time status information such as position and circumference, and provides controls for motion and pressure settings. The component accepts props for drum status, motion data, ejection and suction pressure data, and connection status. It emits events for motion start/cancel and pressure changes, allowing parent components to respond to user actions.

#### Component Structure
```mermaid
classDiagram
class DrumControl {
+drumId : Number
+drumStatus : Object
+motionData : Object
+ejectionData : Object
+suctionData : Object
+connected : Boolean
+motionParams : Object
+ejectionTarget : Number
+ejectionUnit : String
+suctionTarget : Number
+errorMessage : String
+startMotion() : Promise
+cancelMotion() : Promise
+setEjectionPressure() : Promise
+setSuctionPressure() : Promise
}
DrumControl --> apiService : "uses"
apiService --> "/recoater/drums/{id}/motion" : "POST"
apiService --> "/recoater/drums/{id}/motion" : "DELETE"
apiService --> "/recoater/drums/{id}/ejection" : "PUT"
apiService --> "/recoater/drums/{id}/suction" : "PUT"
```

**Diagram sources**
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)

### HopperControl Analysis
The HopperControl component manages the blade screws on a hopper unit. It displays the position of each screw and provides controls for collective and individual screw motion. The component supports different motion modes (relative, absolute, homing) and allows users to set motion parameters and start/cancel operations.

#### Component Structure
```mermaid
classDiagram
class HopperControl {
+drumId : Number
+bladeScrews : Array
+connected : Boolean
+collectiveMotion : Object
+individualMotion : Object
+isBladeRunning : Computed
+getScrewRunning(id) : Function
+formatPosition(pos) : Function
+startCollectiveMotion() : Promise
+cancelCollectiveMotion() : Promise
+startIndividualMotion(id) : Promise
+cancelIndividualMotion(id) : Promise
}
HopperControl --> apiService : "uses"
apiService --> "/recoater/drums/{id}/blade/screws/motion" : "POST"
apiService --> "/recoater/drums/{id}/blade/screws/motion" : "DELETE"
apiService --> "/recoater/drums/{id}/blade/screws/{screwId}/motion" : "POST"
apiService --> "/recoater/drums/{id}/blade/screws/{screwId}/motion" : "DELETE"
```

**Diagram sources**
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)

### LevelerControl Analysis
The LevelerControl component manages the leveler subsystem, providing pressure control and displaying sensor status. It allows users to set a target pressure and displays current, target, and maximum pressure values. The component also shows the state of the magnetic sensor.

#### Component Implementation
```mermaid
sequenceDiagram
participant User
participant LevelerControl
participant apiService
participant Backend
User->>LevelerControl : Click "Set" button
LevelerControl->>LevelerControl : Validate target pressure
alt Valid pressure
LevelerControl->>apiService : setLevelerPressure(target)
apiService->>Backend : PUT /recoater/leveler/pressure
Backend-->>apiService : 200 OK
apiService-->>LevelerControl : Success response
LevelerControl->>LevelerControl : Emit "pressure-set" event
LevelerControl->>User : Show success message
else Invalid pressure
LevelerControl->>User : Show validation error
end
```

**Diagram sources**
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)

### MultiLayerJobControl Analysis
The MultiLayerJobControl component orchestrates multi-material print jobs across three drums. It manages the upload of CLI files, tracks job status, and provides controls to start and cancel jobs. The component integrates with the printJobStore to maintain job state and uses auto-refresh to keep the status current.

#### Job Orchestration Flow
```mermaid
flowchart TD
Start([Start]) --> UploadFiles["Upload CLI Files to 3 Drums"]
UploadFiles --> CheckRequirements["Check Requirements"]
CheckRequirements --> |All files uploaded| ReadyToStart["Ready to Start Job"]
CheckRequirements --> |Missing files| WaitUpload["Wait for Uploads"]
ReadyToStart --> StartJob["Click 'Start Job'"]
StartJob --> CallAPI["Call startMultiMaterialJob API"]
CallAPI --> Backend["Backend Processes Job"]
Backend --> UpdateStatus["Update Job Status"]
UpdateStatus --> DisplayStatus["Display Status in UI"]
DisplayStatus --> AutoRefresh["Auto-refresh every 5s"]
AutoRefresh --> UpdateStatus
StartJob --> |Cancel Job| Cancel["Call cancelMultiMaterialJob API"]
Cancel --> ClearFiles["Clear Uploaded Files"]
ClearFiles --> ReadyToStart
```

**Diagram sources**
- [frontend/src/components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)

### FileUploadColumn Analysis
The FileUploadColumn component handles the upload of CLI files for print jobs. It provides a file input interface and manages the upload process, including error handling and success feedback. The component is used within the MultiLayerJobControl to allow users to upload files for each drum.

**Section sources**
- [frontend/src/components/FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)

## Dependency Analysis
The frontend components have a clear dependency hierarchy, with views depending on components, components depending on stores and services, and stores depending on services for API communication. The Pinia stores (status.js and printJobStore.js) serve as the single source of truth for application state, with components subscribing to store state and dispatching actions to modify it. The api.js service provides a centralized interface to the backend API, ensuring consistent request formatting and error handling.

```mermaid
graph TD
A[PrintView] --> B[MultiLayerJobControl]
A --> C[FileUploadColumn]
D[RecoaterView] --> E[DrumControl]
D --> F[HopperControl]
D --> G[LevelerControl]
B --> H[printJobStore]
C --> H
E --> I[statusStore]
F --> I
G --> I
H --> J[apiService]
I --> J
J --> K[Backend API]
J --> L[WebSocket]
```

**Diagram sources**
- [frontend/src/views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [frontend/src/views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [frontend/src/components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
- [frontend/src/components/FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

## Performance Considerations
The application handles frequent status updates through WebSocket connections, with the status store managing real-time data subscriptions based on the current page. This selective subscription approach reduces unnecessary network traffic and processing overhead. For large job workflows, the MultiLayerJobControl component uses auto-refresh with a 5-second interval to balance real-time feedback with performance. Components use Vue's reactivity system efficiently, with computed properties for derived state and proper cleanup of intervals in lifecycle hooks. The use of Pinia stores centralizes state management, preventing redundant API calls and ensuring consistent data across components.

## Troubleshooting Guide
Common issues in the frontend application include component state desynchronization, WebSocket connection problems, and API request failures. For state desynchronization, ensure that components are properly subscribed to store state and that store actions are correctly updating state. For WebSocket issues, check the connection URL and ensure the backend WebSocket server is running. For API failures, verify the request payload and check backend logs for errors. The application includes error handling in all API service methods, with error messages displayed to users and logged to the console. The status store's reconnection logic automatically attempts to reconnect WebSocket connections if they are lost.

**Section sources**
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)
- [frontend/src/components/LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [frontend/src/components/DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [frontend/src/components/HopperControl.vue](file://frontend/src/components/HopperControl.vue)

## Conclusion
The frontend of APIRecoater_Ethernet is a well-structured Vue.js application that effectively manages complex hardware control and job orchestration workflows. By using a component-based architecture with centralized state management, the application provides a responsive and reliable interface for operating the recoater system. The integration of REST API and WebSocket communication enables real-time monitoring and control, while thoughtful performance optimizations ensure smooth operation even with frequent status updates. The modular design makes the application maintainable and extensible, providing a solid foundation for future enhancements.