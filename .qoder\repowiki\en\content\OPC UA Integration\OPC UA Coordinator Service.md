# OPC UA Coordinator Service

<cite>
**Referenced Files in This Document**   
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [main.py](file://backend/app/main.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Responsibilities](#core-responsibilities)
3. [Architecture Overview](#architecture-overview)
4. [Connection Management](#connection-management)
5. [Variable Coordination and State Synchronization](#variable-coordination-and-state-synchronization)
6. [Event-Driven Architecture with WebsocketManager](#event-driven-architecture-with-websocketmanager)
7. [Command Routing and API Integration](#command-routing-and-api-integration)
8. [Error Handling and Resilience](#error-handling-and-resilience)
9. [Performance and Thread Safety](#performance-and-thread-safety)
10. [Troubleshooting Guide](#troubleshooting-guide)

## Introduction

The OPCUACoordinator service is the central orchestrator for all OPC UA interactions within the APIRecoater_Ethernet system. It acts as a high-level abstraction layer between the application logic and the underlying OPC UA communication infrastructure, managing session lifecycle, variable subscriptions, and command routing. This service ensures synchronized state changes across multiple components by interfacing with services such as StatusPoller and RecoaterControlsAPI. The coordinator implements robust reconnection logic, error handling, and event broadcasting via WebsocketManager to support real-time monitoring and control of the recoater system.

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)

## Core Responsibilities

The OPCUACoordinator manages seven key coordination variables used in multi-material print jobs:
- **job_active**: Indicates whether a print job is currently active
- **total_layers**: Total number of layers in the current job
- **current_layer**: Current layer being processed
- **recoater_ready_to_print**: Status indicating readiness for printing
- **recoater_layer_complete**: Flag set when layer deposition is complete
- **backend_error**: Error flag for backend system issues
- **plc_error**: Error flag for PLC communication problems

These variables are synchronized between the backend and the PLC to ensure consistent state across the system.

```mermaid
classDiagram
class OPCUACoordinator {
-config : Config
-server_manager : OPCUAServerManager
-_connected : bool
-_monitoring_task : Task
-_event_handlers : Dict[str, List[Callable]]
+connect() bool
+disconnect() bool
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+is_connected() bool
+get_server_status() Dict[str, Any]
}
class OPCUAServerManager {
+is_running : bool
+start_server() Coroutine
+stop_server() Coroutine
+write_variable(name, value) bool
+read_variable(name) Any
+get_variable_names() List[str]
}
OPCUACoordinator --> OPCUAServerManager : "uses"
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)

## Architecture Overview

The OPCUACoordinator integrates with multiple system components to provide a unified interface for OPC UA operations. It leverages the OPCUAServerManager for low-level protocol communication and uses WebsocketManager to broadcast state changes to connected clients. The service is accessed through REST APIs exposed by RecoaterControlsAPI, enabling web-based control and monitoring.

```mermaid
graph TB
subgraph "Frontend"
UI[Web Interface]
WSClient[WebSocket Client]
end
subgraph "Backend"
API[RecoaterControlsAPI]
OPCUA[OPCUACoordinator]
ServerMgr[OPCUAServerManager]
WSManager[WebsocketManager]
end
UI --> API
API --> OPCUA
OPCUA --> ServerMgr
OPCUA --> WSManager
WSClient --> WSManager
WSManager --> WSClient
style OPCUA fill:#f9f,stroke:#333
style WSManager fill:#bbf,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L146)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L0-L799)

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L146)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L0-L799)

## Connection Management

The OPCUACoordinator manages its connection lifecycle through explicit connect() and disconnect() methods. When initialized, it references a configuration object and an OPCUAServerManager instance responsible for the actual server operations.

### Connection Flow
```mermaid
sequenceDiagram
participant Client
participant Coordinator as OPCUACoordinator
participant ServerMgr as OPCUAServerManager
Client->>Coordinator : connect()
Coordinator->>Coordinator : Check _connected flag
alt Already connected
Coordinator-->>Client : Return True
else Not connected
Coordinator->>ServerMgr : Start server (async task)
loop Wait for server readiness
Coordinator->>ServerMgr : is_running?
ServerMgr-->>Coordinator : False
end
Coordinator->>ServerMgr : is_running?
ServerMgr-->>Coordinator : True
Coordinator->>Coordinator : Set _connected = True
Coordinator->>Coordinator : Start _monitoring_loop()
Coordinator-->>Client : Return True
end
```

The connection process includes:
1. Checking existing connection status
2. Starting the OPC UA server if not already running
3. Waiting up to 10 seconds for server readiness
4. Initializing the monitoring loop
5. Setting internal connection state

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L245-L284)

## Variable Coordination and State Synchronization

The coordinator provides methods to read and write coordination variables, ensuring proper synchronization between the backend and PLC systems.

### Job State Management
```mermaid
flowchart TD
Start([set_job_active]) --> Write1["write_variable('job_active', True)"]
Write1 --> Write2["write_variable('total_layers', total_layers)"]
Write2 --> Write3["write_variable('current_layer', 0)"]
Write3 --> End([Return Success])
Start2([set_job_inactive]) --> Write4["write_variable('job_active', False)"]
Write4 --> Write5["write_variable('total_layers', 0)"]
Write5 --> Write6["write_variable('current_layer', 0)"]
Write6 --> Write7["write_variable('recoater_ready_to_print', False)"]
Write7 --> Write8["write_variable('recoater_layer_complete', False)"]
Write8 --> End2([Return Success])
```

The service also supports subscription to variable changes, allowing other components to register event handlers that are triggered when specific variables change value.

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L320-L465)

## Event-Driven Architecture with WebsocketManager

The OPCUACoordinator works in conjunction with WebsocketManager to enable real-time updates to connected clients. While the coordinator itself does not directly call broadcast(), it triggers events that propagate through the system to generate WebSocket messages.

### WebSocket Integration
```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant WSManager as WebsocketManager
Frontend->>Backend : Connect to /ws
Backend->>WSManager : connect(websocket)
WSManager-->>Backend : Accept connection
Backend-->>Frontend : Connection established
loop Periodic Updates
Backend->>WSManager : broadcast(message)
WSManager->>WSManager : _filter_message_for_connection()
WSManager->>Frontend : Send filtered message
end
Frontend->>Backend : Send subscription update
Backend->>WSManager : update_subscription(websocket, data_types)
WSManager-->>Backend : Subscription updated
```

The WebsocketManager maintains:
- **active_connections**: List of active WebSocket connections
- **connection_subscriptions**: Mapping of connections to their subscribed data types
- **broadcast()**: Method to send messages to all connected clients
- **_filter_message_for_connection()**: Filters messages based on subscription preferences

Clients can subscribe to different data types including 'status', 'axis', 'drum', 'leveler', and 'print' data.

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L146)
- [main.py](file://backend/app/main.py#L105-L138)

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L146)
- [main.py](file://backend/app/main.py#L105-L138)

## Command Routing and API Integration

The OPCUACoordinator is accessed through the RecoaterControlsAPI, which exposes REST endpoints for various control operations. API requests are translated into OPC UA method calls via the coordinator.

### API to OPC UA Flow
```mermaid
sequenceDiagram
participant Client
participant API as RecoaterControlsAPI
participant Coordinator as OPCUACoordinator
participant ServerMgr as OPCUAServerManager
Client->>API : POST /recoater/drum/0/motion
API->>API : Validate request (DrumMotionRequest)
API->>Coordinator : set_drum_motion(drum_id, mode, speed, distance)
Coordinator->>ServerMgr : write_variable("drum0_speed", speed)
ServerMgr-->>Coordinator : Success
Coordinator->>Coordinator : _trigger_event_handlers()
Coordinator-->>API : Response
API-->>Client : JSON response with status
```

Example code showing how an API request triggers OPC UA operations:

```python
@router.post("/drums/{drum_id}/motion")
async def set_drum_motion(
    motion_request: DrumMotionRequest,
    drum_id: int,
    client: RecoaterClient = Depends(get_recoater_client)
):
    # This call ultimately routes through OPCUACoordinator
    response_data = client.set_drum_motion(
        drum_id=drum_id,
        mode=motion_request.mode,
        speed=motion_request.speed,
        distance=motion_request.distance,
        turns=motion_request.turns
    )
    return response
```

**Section sources**
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L0-L799)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)

## Error Handling and Resilience

The coordinator implements comprehensive error handling strategies to ensure system reliability.

### Error Management Methods
- **set_backend_error(error: bool)**: Sets the backend error flag and logs appropriately
- **set_plc_error(error: bool)**: Sets the PLC error flag and logs appropriately  
- **clear_error_flags()**: Clears both backend and PLC error flags
- Internal try-except blocks in all public methods to prevent unhandled exceptions

The service also handles connection-related errors gracefully:
- Returns False for write/read operations when not connected
- Logs warnings for attempted operations on disconnected coordinator
- Implements proper cleanup during disconnection (cancelling tasks, stopping server)

### Monitoring Loop Error Handling
```mermaid
flowchart TD
A[_monitoring_loop] --> B{connected?}
B --> |Yes| C[Wait 1 second]
C --> D{Cancelled?}
D --> |Yes| E[Log debug message]
D --> |No| F{Exception?}
F --> |Yes| G[Log error]
F --> |No| A
B --> |No| H[Exit loop]
```

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L420-L465)

## Performance and Thread Safety

The OPCUACoordinator is designed with performance and concurrency in mind:

### Subscription Management
- Uses a dictionary of lists (`_event_handlers`) to store multiple handlers per variable
- Efficient O(1) lookup for event handler retrieval
- Asynchronous execution of event handlers to prevent blocking

### Connection Pooling Considerations
While the current implementation does not use traditional connection pooling, it employs a singleton-like pattern through the shared OPCUAServerManager instance, ensuring only one server connection is maintained.

### Thread Safety Analysis
- The service is primarily async-cooperative rather than multi-threaded
- All operations are awaitable coroutines
- The _monitoring_task is properly cancellable
- Event handler execution is isolated to prevent one failing handler from affecting others

Potential race conditions are mitigated by:
- Atomic connection state flag (_connected)
- Proper asyncio task cancellation
- Dictionary access protected by Python's GIL for basic operations

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)

## Troubleshooting Guide

### Common Issues and Solutions

#### Session Expiration
**Symptoms**: Connection drops after period of inactivity  
**Diagnosis**: Check server logs for disconnection messages  
**Solution**: Ensure heartbeat mechanism is active; verify server keep-alive settings

#### Subscription Loss
**Symptoms**: Clients stop receiving updates  
**Diagnosis**: Verify subscription state in WebsocketManager.connection_subscriptions  
**Solution**: Implement re-subscription logic in client; check for WebSocket disconnections

#### Race Conditions
**Symptoms**: Inconsistent variable states, missed events  
**Prevention**: 
- Ensure all coordinator methods are called asynchronously
- Avoid parallel calls to set_job_active/set_job_inactive
- Use clear_error_flags() instead of individual flag resets when possible

#### Connection Failures
**Symptoms**: connect() returns False  
**Diagnosis**: 
- Check if OPC UA server is running
- Verify endpoint configuration
- Ensure port 8000 is not blocked

**Recovery Procedure**:
```python
# Example recovery sequence
await coordinator.disconnect()
# Wait briefly
await asyncio.sleep(2)
# Reconnect
success = await coordinator.connect()
if not success:
    logger.critical("Failed to recover OPC UA connection")
```

#### Debugging Tips
1. Enable detailed logging to trace coordinator operations
2. Monitor WebsocketManager.connection_count to verify client connections
3. Use get_server_status() to check coordinator health
4. Validate variable values with read_variable() after write operations

**Section sources**
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L218-L584)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L146)