# OPC UA Simulation and Testing

<cite>
**Referenced Files in This Document**   
- [opcua_simulator.py](file://opcua_simulator.py)
- [test_opcua_client.py](file://test_opcua_client.py)
- [simple_connection_test.py](file://simple_connection_test.py)
- [backend/app/config/opcua_config.py](file://backend\app\config\opcua_config.py)
- [config.py](file://config.py)
- [backend/infrastructure/mock_recoater_client/mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py) - *Refactored in commit 532239de*
- [backend/infrastructure/mock_recoater_client/mock_blade_controls.py](file://backend\infrastructure\mock_recoater_client\mock_blade_controls.py) - *Extracted in commit 532239de*
- [backend/infrastructure/mock_recoater_client/mock_drum_controls.py](file://backend\infrastructure\mock_recoater_client\mock_drum_controls.py) - *Extracted in commit 532239de*
- [backend/infrastructure/mock_recoater_client/mock_leveler_controls.py](file://backend\infrastructure\mock_recoater_client\mock_leveler_controls.py) - *Extracted in commit 532239de*
- [backend/infrastructure/mock_recoater_client/mock_print_controls.py](file://backend\infrastructure\mock_recoater_client\mock_print_controls.py) - *Extracted in commit 532239de*
- [backend/infrastructure/mock_recoater_client/mock_async_client.py](file://backend\infrastructure\mock_recoater_client\mock_async_client.py) - *Extracted in commit 532239de*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the refactoring of the monolithic mock_recoater_client into a modular package structure
- Added new section on Mock Recoater Client Architecture to document the extracted modules
- Enhanced the Client Testing Framework section to include details about the modular mock client
- Added diagram visualizing the new mock client architecture
- Updated section sources to include all relevant mock_recoater_client module files
- Added references to the mock client's integration with test fixtures in conftest.py

## Table of Contents
1. [Introduction](#introduction)
2. [OPC UA Simulator Architecture](#opc-ua-simulator-architecture)
3. [Recoater Device Emulation](#recoater-device-emulation)
4. [Simulator Configuration and Setup](#simulator-configuration-and-setup)
5. [Mock Recoater Client Architecture](#mock-recoater-client-architecture)
6. [Client Testing Framework](#client-testing-framework)
7. [Connectivity Validation](#connectivity-validation)
8. [End-to-End Testing Workflow](#end-to-end-testing-workflow)
9. [Simulation Limitations and Bridging Strategies](#simulation-limitations-and-bridging-strategies)
10. [Advanced Testing Patterns](#advanced-testing-patterns)
11. [Conclusion](#conclusion)

## Introduction
The APIRecoater_Ethernet repository includes comprehensive OPC UA simulation and testing capabilities designed to support development and integration testing for recoater devices. The system provides a full-featured OPC UA simulator that emulates realistic device behavior, enabling developers to test client applications without requiring physical hardware. This documentation details the architecture, configuration, and usage of the OPC UA simulation framework, including the simulator implementation, client testing tools, and validation methodologies. The simulation environment allows for comprehensive testing of connectivity, subscription behavior, fault tolerance, and edge cases under various network conditions.

## OPC UA Simulator Architecture

``mermaid
classDiagram
class OPCUAMachineSimulator {
+string machine_type
+int port
+int namespace_index
+Server server
+dict nodes
+bool running
+__init__(machine_type, port, namespace_index)
+start_server() void
+stop_server() void
+_create_machine_nodes(namespace_idx) void
+_simulate_machine_data() void
+_update_3d_printer_data(time_step) void
+_update_cnc_machine_data(time_step) void
+_update_power_meter_data(time_step) void
}
class Server {
+init() void
+set_endpoint(endpoint) void
+set_server_name(name) void
+register_namespace(uri) int
+start() void
+stop() void
}
OPCUAMachineSimulator --> Server : "uses"
OPCUAMachineSimulator --> Node : "creates"
class Node {
+add_folder(namespace_idx, name) Node
+add_variable(namespace_idx, name, value, variant_type) Node
+set_writable() void
+write_value(value) void
+read_value() any
}
class ua {
+VariantType : enum
}
OPCUAMachineSimulator --> ua : "uses"
```

**Diagram sources**
- [opcua_simulator.py](file://opcua_simulator.py#L16-L313)

**Section sources**
- [opcua_simulator.py](file://opcua_simulator.py#L16-L313)

## Recoater Device Emulation

The OPC UA simulator framework provides a flexible architecture for emulating industrial machines, though it requires configuration to specifically emulate a recoater device. The `OPCUAMachineSimulator` class serves as the foundation for creating simulated OPC UA servers that behave like real industrial equipment. While the simulator includes predefined templates for 3D printers, CNC machines, and power meters, it can be configured to represent a recoater by defining appropriate node structures and data dynamics.

The simulator creates a hierarchical node structure organized by machine type, with variables grouped into logical folders such as "Environment," "Status," and "Process." For a recoater emulation, this structure would include variables for layer progress, recoater position, drum pressure, and job status. The simulator supports writable variables, allowing client applications to modify values as they would with a real device. Data dynamics are simulated through periodic updates that introduce realistic patterns such as sine waves with noise for temperature variations, incremental progress for build completion, and state transitions based on operational conditions.

The simulation engine runs asynchronously, updating node values at regular intervals (every second by default) to create dynamic behavior that mimics real-world machine operation. This allows client applications to test subscription mechanisms, data change notifications, and real-time monitoring capabilities. The simulator also supports multiple concurrent instances, enabling testing of distributed systems with several simulated devices.

## Simulator Configuration and Setup

``mermaid
flowchart TD
Start([Start Simulation]) --> LoadConfig["Load Configuration from config.yaml"]
LoadConfig --> ValidateConfig{"Configuration Valid?"}
ValidateConfig --> |No| ShowError["Display Error: config.yaml not found"]
ValidateConfig --> |Yes| CreateSimulators["Create Simulator Instances"]
CreateSimulators --> StartServers["Start All Simulators"]
StartServers --> DisplayInfo["Display Connection Information"]
DisplayInfo --> MonitorLoop["Monitor for Shutdown Request"]
MonitorLoop --> |Ctrl+C Received| StopServers["Stop All Simulators"]
StopServers --> End([Simulation Ended])
style Start fill:#4CAF50,stroke:#388E3C
style End fill:#4CAF50,stroke:#388E3C
style ShowError fill:#F44336,stroke:#D32F2F
```

**Diagram sources**
- [opcua_simulator.py](file://opcua_simulator.py#L298-L374)

**Section sources**
- [opcua_simulator.py](file://opcua_simulator.py#L298-L374)
- [backend/app/config/opcua_config.py](file://backend\app\config\opcua_config.py#L101-L136)

The OPC UA simulator is configured through a YAML configuration file that specifies the machine types and port numbers for each simulated server. The main entry point is the `main()` function in `opcua_simulator.py`, which loads the configuration from `config.yaml` and starts the specified simulators. Each simulator instance is created with a machine type, port number, and optional namespace index, allowing multiple devices to run simultaneously on different ports.

The simulator uses environment variables to configure OPC UA server settings, with defaults provided in `backend/app/config/opcua_config.py`. Key configuration parameters include:
- **OPCUA_SERVER_ENDPOINT**: The network endpoint for the server (default: "opc.tcp://0.0.0.0:4843/recoater/server/")
- **OPCUA_SERVER_NAME**: The server name visible to clients (default: "Recoater Multi-Material Coordination Server")
- **OPCUA_NAMESPACE_URI**: The namespace URI for the server's address space
- **OPCUA_NAMESPACE_IDX**: The namespace index (default: 2)
- **Security settings**: Security policy and mode (default: None for internal networks)
- **Connection timeouts**: Connection and session timeout values

To set up the simulator for recoater testing, developers need to create a `config.yaml` file that defines a server with a machine type appropriate for recoater emulation. The simulator can then be started by running `python opcua_simulator.py`, which will initialize the specified servers and display connection information. Clients can connect to the simulators using the provided endpoints, with each server running on its designated port.

## Mock Recoater Client Architecture

``mermaid
classDiagram
class MockRecoaterClient {
+string base_url
+float timeout
+dict _state
+dict _drum_states
+dict _current_layers
+bool _job_active
+__init__(base_url, timeout)
+get_state() dict
+get_config() dict
+set_config(config) dict
+get_status() dict
+start_job(job_data) dict
+stop_job() dict
+pause_job() dict
+resume_job() dict
+get_axis_position(axis) dict
+move_axis(axis, position, speed) dict
+home_axis(axis) dict
+get_axis_status(axis) dict
+get_gripper_state() dict
+get_drums() list
+get_drum(drum_id) dict
+set_state(action) dict
+health_check() bool
}
class MockBladeControlMixin {
+get_blade_screws_info(drum_id) dict
+get_blade_screws_motion(drum_id) dict
+set_blade_screws_motion(drum_id, mode, distance) dict
+cancel_blade_screws_motion(drum_id) dict
+get_blade_screw_info(drum_id, screw_id) dict
+get_blade_screw_motion(drum_id, screw_id) dict
+set_blade_screw_motion(drum_id, screw_id, distance) dict
+cancel_blade_screw_motion(drum_id, screw_id) dict
}
class MockDrumControlMixin {
+get_drum_motion(drum_id) dict
+set_drum_motion(drum_id, mode, speed, distance, turns) dict
+cancel_drum_motion(drum_id) dict
+get_drum_ejection(drum_id, unit) dict
+set_drum_ejection(drum_id, target, unit) dict
+get_drum_suction(drum_id) dict
+set_drum_suction(drum_id, target) dict
}
class MockLevelerControlMixin {
+get_leveler_pressure() dict
+set_leveler_pressure(target) dict
+get_leveler_sensor() dict
}
class MockPrintControlMixin {
+get_layer_parameters() dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) dict
+get_layer_preview() bytes
+start_print_job() dict
+cancel_print_job() dict
+get_print_job_status() dict
}
class MockAsyncClientMixin {
+upload_cli_data(drum_id, cli_data) dict
+get_drum_status(drum_id) dict
+get_multimaterial_status() dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
+set_multimaterial_job_active(active) void
+advance_layer(drum_id) void
+reset_multimaterial_state() void
}
class MockFileManagementMixin {
+upload_drum_geometry(drum_id, file_data, content_type) dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) dict
+list_drum_geometries() dict
}
MockRecoaterClient --|> MockBladeControlMixin : "inherits"
MockRecoaterClient --|> MockDrumControlMixin : "inherits"
MockRecoaterClient --|> MockLevelerControlMixin : "inherits"
MockRecoaterClient --|> MockPrintControlMixin : "inherits"
MockRecoaterClient --|> MockAsyncClientMixin : "inherits"
MockRecoaterClient --|> MockFileManagementMixin : "inherits"
```

**Diagram sources**
- [backend/infrastructure/mock_recoater_client/mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py#L26-L406)
- [backend/infrastructure/mock_recoater_client/mock_blade_controls.py](file://backend\infrastructure\mock_recoater_client\mock_blade_controls.py#L10-L186)
- [backend/infrastructure/mock_recoater_client/mock_drum_controls.py](file://backend\infrastructure\mock_recoater_client\mock_drum_controls.py#L10-L160)
- [backend/infrastructure/mock_recoater_client/mock_leveler_controls.py](file://backend\infrastructure\mock_recoater_client\mock_leveler_controls.py#L10-L64)
- [backend/infrastructure/mock_recoater_client/mock_print_controls.py](file://backend\infrastructure\mock_recoater_client\mock_print_controls.py#L10-L152)
- [backend/infrastructure/mock_recoater_client/mock_async_client.py](file://backend\infrastructure\mock_recoater_client\mock_async_client.py#L10-L151)

**Section sources**
- [backend/infrastructure/mock_recoater_client/mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py#L26-L406)
- [backend/infrastructure/mock_recoater_client/mock_blade_controls.py](file://backend\infrastructure\mock_recoater_client\mock_blade_controls.py#L10-L186)
- [backend/infrastructure/mock_recoater_client/mock_drum_controls.py](file://backend\infrastructure\mock_recoater_client\mock_drum_controls.py#L10-L160)
- [backend/infrastructure/mock_recoater_client/mock_leveler_controls.py](file://backend\infrastructure\mock_recoater_client\mock_leveler_controls.py#L10-L64)
- [backend/infrastructure/mock_recoater_client/mock_print_controls.py](file://backend\infrastructure\mock_recoater_client\mock_print_controls.py#L10-L152)
- [backend/infrastructure/mock_recoater_client/mock_async_client.py](file://backend\infrastructure\mock_recoater_client\mock_async_client.py#L10-L151)
- [backend/infrastructure/mock_recoater_client/__init__.py](file://backend\infrastructure\mock_recoater_client\__init__.py#L1-L26)

The mock_recoater_client has been refactored into a modular package structure to improve testability and maintainability. The monolithic implementation has been split into dedicated modules for specific control functions, each implemented as a mixin class that can be composed into the main MockRecoaterClient class. This modular approach allows for more focused testing and easier maintenance of individual functionality areas.

The refactored architecture consists of the following components:
- **mock_client.py**: The main MockRecoaterClient class that inherits from all mixins and provides the complete interface
- **mock_blade_controls.py**: Handles blade control methods including screw position, motion, and status
- **mock_drum_controls.py**: Manages drum motion, ejection pressure, and suction pressure control
- **mock_leveler_controls.py**: Controls leveler pressure and sensor state
- **mock_print_controls.py**: Manages print job operations, layer parameters, and preview generation
- **mock_async_client.py**: Provides asynchronous methods for multi-material coordination and state monitoring
- **mock_file_management.py**: Handles CLI file uploads, downloads, and management

The MockRecoaterClient class uses multiple inheritance to combine all these mixins, creating a comprehensive mock implementation that simulates the full hardware API. This approach allows developers to test specific functionality in isolation while maintaining the complete interface for integration testing. The modular structure also enables selective mocking of specific subsystems when needed for focused testing scenarios.

## Client Testing Framework

``mermaid
classDiagram
class OPCUAClientTester {
+string endpoint
+Client client
+__init__(endpoint)
+connect() bool
+disconnect() void
+get_server_info() dict
+browse_node(node, max_depth, current_depth) dict
+find_coordination_variables() dict
+test_write_operations() dict
}
class Client {
+connect() void
+disconnect() void
+get_server_node() Node
+get_root_node() Node
+get_objects_node() Node
}
OPCUAClientTester --> Client : "uses"
OPCUAClientTester --> Node : "interacts with"
class Node {
+read_display_name() string
+read_node_class() string
+read_value() any
+read_data_type() string
+get_children() list
}
class main {
+main() void
}
main --> OPCUAClientTester : "uses"
```

**Diagram sources**
- [test_opcua_client.py](file://test_opcua_client.py#L16-L246)

**Section sources**
- [test_opcua_client.py](file://test_opcua_client.py#L16-L246)
- [backend/infrastructure/mock_recoater_client/mock_client.py](file://backend\infrastructure\mock_recoater_client\mock_client.py#L26-L406)
- [backend/tests/conftest.py](file://backend\tests\conftest.py#L90-L127)

The client testing framework provides comprehensive tools for validating OPC UA server functionality, including connectivity, data access, and write operations. The `OPCUAClientTester` class serves as the primary testing interface, offering methods to connect to servers, retrieve server information, browse node structures, and test variable operations. The framework is designed to validate both basic connectivity and more complex interactions with the server's address space.

The testing process begins with establishing a connection to the OPC UA server using the configured endpoint. Once connected, the tester retrieves basic server information such as server arrays, namespace arrays, and root node details. It then performs a recursive browse of the server's node structure, limited to a configurable depth, to map the address space hierarchy. This browsing capability allows developers to discover variables and understand the server's organizational structure without prior knowledge of its configuration.

A key feature of the testing framework is its ability to locate and test coordination variables, which are critical for recoater operation. The `find_coordination_variables()` method searches for variables related to job control, such as "job_active," "total_layers," "current_layer," and error flags. It also tests write operations on writable variables like "plc_error" and "backend_error," verifying that clients can successfully modify server state and that changes are properly reflected when reading back the values. The test results are displayed with clear success/failure indicators, making it easy to identify issues with server functionality.

The framework integrates with the refactored mock_recoater_client package through test fixtures that replace the real client with the mock implementation. In conftest.py, the dependency injection system is configured to provide the MockRecoaterClient instance when tests request a recoater client dependency. This allows API tests to validate endpoint behavior without requiring physical hardware. The mock client's modular structure enables targeted testing of specific functionality areas while maintaining the complete interface for integration testing.

## Connectivity Validation

``mermaid
flowchart TD
Start([Start Connection Test]) --> MakeRequest["Send GET request to /state endpoint"]
MakeRequest --> CheckResponse{"Response Status Code?"}
CheckResponse --> |200 OK| ParseJSON["Parse JSON Response"]
ParseJSON --> DisplaySuccess["Display System Information"]
DisplaySuccess --> ShowRaw["Show Raw API Response"]
CheckResponse --> |Other Status| ShowError["Display Error Message"]
ShowError --> CheckException{"Exception Type?"}
CheckException --> |ConnectionError| ShowNetworkError["Display Network Troubleshooting"]
CheckException --> |Timeout| ShowTimeoutError["Display Timeout Message"]
CheckException --> |Other| ShowUnexpectedError["Display Generic Error"]
style Start fill:#4CAF50,stroke:#388E3C
style DisplaySuccess fill:#4CAF50,stroke:#388E3C
style ShowError fill:#F44336,stroke:#D32F2F
```

**Diagram sources**
- [simple_connection_test.py](file://simple_connection_test.py#L45-L108)

**Section sources**
- [simple_connection_test.py](file://simple_connection_test.py#L45-L108)
- [config.py](file://config.py#L1-L16)

The repository includes multiple layers of connectivity validation, starting with the simplest possible test and progressing to comprehensive functionality verification. The `simple_connection_test.py` script provides the most basic reachability check by making a single HTTP GET request to the `/state` endpoint of the recoater API. This test verifies network connectivity, server responsiveness, and basic API functionality without requiring OPC UA protocol knowledge.

The simple connection test uses the `requests` library to communicate with the recoater system, with configuration parameters defined in `config.py`. The primary settings are `API_BASE_URL`, which specifies the server address and port, and `API_TIMEOUT`, which defines the maximum time to wait for a response. When executed, the script attempts to connect to the specified endpoint and reports success or failure with detailed error messages. In case of connection errors, it provides troubleshooting suggestions such as checking the IP address, verifying network setup, and ensuring the USB3-to-RJ45 adapter is functioning.

For OPC UA-specific connectivity validation, the `test_opcua_client.py` script provides more comprehensive testing. It establishes a proper OPC UA connection to the server and performs multiple validation steps, including server information retrieval, node browsing, and write operation testing. This multi-layered approach to connectivity validation allows developers to quickly identify whether issues are related to basic network connectivity or more specific OPC UA protocol problems. The combination of simple HTTP testing and comprehensive OPC UA testing provides a complete toolkit for diagnosing connection issues at different levels of the communication stack.

## End-to-End Testing Workflow

To perform end-to-end testing with the OPC UA simulator, follow this step-by-step workflow:

1. **Configure the simulator**: Create a `config.yaml` file in the repository root directory with the following content:
```yaml
development:
  opcua_simulation:
    servers:
      - machine_type: "recoater"
        port: 4843
```

2. **Start the simulator**: Run the OPC UA simulator using the command:
```bash
python opcua_simulator.py
```
The simulator will start and display connection information for the running servers.

3. **Run the basic connectivity test**: Execute the simple connection test to verify basic API reachability:
```bash
python simple_connection_test.py
```
This test will confirm whether the system can communicate with the server at the network level.

4. **Validate OPC UA functionality**: Run the comprehensive client test to verify OPC UA server functionality:
```bash
python test_opcua_client.py
```
This test will connect to the OPC UA server, browse the node structure, find coordination variables, and test write operations.

5. **Monitor test results**: The client test will display detailed results including server information, coordination variables with their current values, write operation test outcomes, and a partial view of the objects node structure.

6. **Test specific scenarios**: Modify the simulator configuration or client test parameters to validate specific use cases, such as testing with different machine types, ports, or network conditions.

7. **Stop the simulator**: Press Ctrl+C in the simulator terminal to gracefully shut down all running servers.

This end-to-end testing workflow enables developers to systematically validate the entire communication stack, from basic network connectivity to full OPC UA protocol functionality. By following this process, teams can ensure that client applications will work correctly with both simulated and real recoater devices.

## Simulation Limitations and Bridging Strategies

While the OPC UA simulator provides valuable testing capabilities, it has several limitations compared to real hardware that developers should understand:

1. **Simplified data dynamics**: The simulator uses basic mathematical patterns (sine waves, random fluctuations) to generate data, which may not fully capture the complex physical behaviors of real recoater systems.

2. **Limited error simulation**: The current implementation focuses on normal operation and does not comprehensively simulate hardware faults, sensor failures, or mechanical issues that could occur with real equipment.

3. **No physical constraints**: The simulator does not model physical limitations such as acceleration/deceleration profiles, mechanical tolerances, or thermal dynamics that affect real recoater performance.

4. **Simplified timing**: The simulation updates data at fixed intervals (every second), which may not reflect the actual timing characteristics of real-time control systems.

To bridge the gap between simulation and real hardware, consider the following strategies:

1. **Enhanced data modeling**: Extend the simulator to include more sophisticated physical models that better represent recoater dynamics, such as mechanical response times, thermal gradients, and material flow characteristics.

2. **Fault injection**: Implement configurable fault modes that can simulate common hardware issues like sensor drift, communication errors, or mechanical jams, allowing client applications to test their error handling capabilities.

3. **Real-time synchronization**: Modify the simulation timing to better match the real device's update rates and response times, ensuring that client applications experience similar timing behavior.

4. **Hybrid testing**: Use the simulator for initial development and basic functionality testing, then transition to real hardware for final validation and performance testing.

5. **Parameter calibration**: When possible, calibrate the simulator parameters based on measurements from real devices to improve the accuracy of the simulation.

## Advanced Testing Patterns

For comprehensive testing of OPC UA clients, implement the following advanced patterns:

1. **Network partitioning tests**: Use network simulation tools to create intermittent connectivity, packet loss, or latency spikes to test client resilience under poor network conditions.

2. **Malformed response testing**: Modify the simulator to occasionally send malformed or unexpected data to verify that client applications handle such scenarios gracefully.

3. **High-load scenarios**: Connect multiple client instances simultaneously to test the server's performance under load and verify that subscription mechanisms work correctly with many subscribers.

4. **Subscription stress testing**: Create clients that establish numerous subscriptions to test the server's ability to manage subscription overhead and deliver notifications efficiently.

5. **State transition testing**: Systematically test all possible state transitions in the recoater workflow, including edge cases like pausing and resuming jobs, handling errors during operation, and recovering from power failures.

6. **Security testing**: When security is enabled, test authentication mechanisms, certificate validation, and access control to ensure proper security implementation.

7. **Long-duration testing**: Run extended tests over hours or days to identify memory leaks, resource exhaustion, or other issues that only appear after prolonged operation.

These advanced testing patterns help ensure that client applications are robust, reliable, and capable of handling the full range of conditions they may encounter in production environments.

## Conclusion
The OPC UA simulation and testing framework in APIRecoater_Ethernet provides a comprehensive environment for developing and validating client applications without requiring physical hardware. The modular simulator architecture allows for flexible configuration and extension to emulate various industrial devices, including recoaters. Combined with the layered testing approach—from basic connectivity checks to comprehensive functionality validation—this framework enables thorough testing of OPC UA clients under various conditions. By understanding the simulation capabilities, configuration options, and testing methodologies described in this documentation, developers can effectively leverage these tools to build robust, reliable applications that will perform well with both simulated and real recoater systems.