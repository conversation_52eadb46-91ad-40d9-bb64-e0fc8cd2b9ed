# Hopper Control Component

<cite>
**Referenced Files in This Document**   
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
- [HopperControl.test.js](file://frontend/src/components/__tests__/HopperControl.test.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Overview](#component-overview)
3. [UI Structure and Layout](#ui-structure-and-layout)
4. [Reactive Data and Computed Properties](#reactive-data-and-computed-properties)
5. [Motion Control Logic](#motion-control-logic)
6. [API Integration and REST Calls](#api-integration-and-rest-calls)
7. [Event Emission and State Management](#event-emission-and-state-management)
8. [Error Handling and User Feedback](#error-handling-and-user-feedback)
9. [Unit Testing Practices](#unit-testing-practices)
10. [Potential Issues and Optimization](#potential-issues-and-optimization)

## Introduction
The HopperControl component is a critical UI element in the APIRecoater_Ethernet system, responsible for managing material feed mechanisms in 3D printing processes. This component provides operators with precise control over hopper blade screws, enabling both collective and individual screw adjustments. It serves as the primary interface for monitoring hopper levels, initiating refill operations, and tracking operational status. The component integrates with backend services through REST APIs and maintains state consistency via the Pinia store system. This documentation provides a comprehensive analysis of its implementation, functionality, and integration patterns.

## Component Overview
The HopperControl component is implemented as a Vue.js single-file component that manages the control interface for material hopper systems. It receives critical parameters through props including the drum ID, blade screw status, and connection state. The component provides two primary control modes: collective blade motion for synchronized screw adjustments and individual screw control for precise, independent adjustments. It displays real-time position data for each screw and provides visual feedback on operational status through color-coded indicators. The component's design follows a card-based layout pattern consistent with other control components in the system, ensuring a uniform user experience across different machine subsystems.

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L1-L212)

## UI Structure and Layout
The component's template is structured as a control card with a header and content section. The header displays the hopper identifier and operational status, while the content section contains multiple control groups.

```mermaid
flowchart TD
A[Hopper Control Card] --> B[Card Header]
A --> C[Card Content]
B --> D[Hopper ID Display]
B --> E[Status Indicator]
C --> F[Blade Screws Status Grid]
C --> G[Collective Blade Motion Group]
C --> H[Individual Screw Control Group]
C --> I[Error Message Display]
G --> J[Mode Selection]
G --> K[Distance Input]
G --> L[Start/Cancel Buttons]
H --> M[Screw 0 Controls]
H --> N[Screw 1 Controls]
M --> O[Distance Input]
M --> P[Move Button]
M --> Q[Stop Button]
```

The blade screws status is displayed in a grid format showing each screw's position in micrometers. The collective blade motion section includes a mode selector (relative, absolute, homing) and distance input, with the distance field conditionally hidden when homing mode is selected. The individual screw control section provides separate controls for each screw, allowing independent movement commands. An error message area appears at the bottom when API calls fail or other errors occur.

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L1-L212)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L1-L212)

## Reactive Data and Computed Properties
The component utilizes Vue's Composition API with reactive references and computed properties to manage its state. Key reactive data includes motion parameters for both collective and individual screw operations, error messages, and timeout handlers for automatic error clearance.

```mermaid
classDiagram
class HopperControl {
-collectiveMotion : Ref{Object}
-individualMotion : Ref{Object}
-errorMessage : Ref{String}
-errorTimeout : Ref{Number}
+isBladeRunning : Computed{Boolean}
+getScrewRunning(screwId) : Function
+formatPosition(pos) : Function
+startCollectiveMotion() : Promise
+cancelCollectiveMotion() : Promise
+startIndividualMotion(screwId) : Promise
+cancelIndividualMotion(screwId) : Promise
}
```

The `collectiveMotion` reference stores the current mode and distance for synchronized screw movement, initialized with default values of 'relative' mode and 1000.0 µm distance. The `individualMotion` reference maintains distance settings for each screw, initialized to 500.0 µm for both screws. Two key computed properties enhance the component's functionality: `isBladeRunning` determines if any screw is currently in motion by checking the running status of all screws, and `getScrewRunning` returns the running status of a specific screw by ID.

The `formatPosition` function ensures consistent display of position values by formatting numeric inputs to one decimal place or providing a default '0.0' value for non-numeric inputs. This function is used in the template to display screw positions with consistent precision.

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L81-L99)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L81-L148)

## Motion Control Logic
The component implements four primary motion control functions that handle user interactions and coordinate with backend systems. These functions follow a consistent pattern of API calls wrapped in try-catch blocks for error handling.

```mermaid
sequenceDiagram
participant User as "User"
participant HopperControl as "HopperControl Component"
participant ApiService as "apiService"
participant Backend as "Backend API"
User->>HopperControl : Click Start Motion
HopperControl->>HopperControl : Prepare motionData object
HopperControl->>ApiService : setBladeScrewsMotion(drumId, motionData)
ApiService->>Backend : POST /recoater/drums/{drumId}/blade/screws/motion
Backend-->>ApiService : 200 OK
ApiService-->>HopperControl : Success response
HopperControl->>HopperControl : Emit motion-started event
HopperControl->>HopperControl : Emit success event
HopperControl-->>User : Visual feedback
```

The `startCollectiveMotion` function initiates synchronized movement of all blade screws. It constructs a motion data object containing the selected mode and distance (if not in homing mode), then calls the `setBladeScrewsMotion` API method with the current drum ID and motion parameters. Upon success, it emits 'motion-started' and 'success' events to notify parent components.

The `cancelCollectiveMotion` function stops all ongoing collective motion by calling the `cancelBladeScrewsMotion` API method. Similarly, `startIndividualMotion` and `cancelIndividualMotion` functions handle individual screw operations by calling their respective API methods with the specific screw ID.

All motion functions include comprehensive error handling that captures API response details and displays user-friendly error messages.

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L100-L147)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L100-L147)

## API Integration and REST Calls
The HopperControl component interacts with the backend system through the centralized api.js service, which provides a consistent interface for all HTTP requests. This service uses Axios with interceptors for logging and error handling.

```mermaid
sequenceDiagram
participant HopperControl as "HopperControl"
participant ApiService as "apiService"
participant Axios as "Axios Client"
participant Backend as "Backend Server"
HopperControl->>ApiService : setBladeScrewsMotion()
ApiService->>Axios : POST /recoater/drums/{id}/blade/screws/motion
Axios->>Axios : Request interceptor logs call
Axios->>Backend : HTTP Request
Backend-->>Axios : HTTP Response
Axios->>Axios : Response interceptor logs result
Axios-->>ApiService : Response or Error
ApiService-->>HopperControl : Promise resolution
```

The api.js service defines specific methods for blade screw operations:
- `setBladeScrewsMotion(drumId, motionData)`: Initiates collective motion
- `cancelBladeScrewsMotion(drumId)`: Cancels collective motion
- `setBladeScrewMotion(drumId, screwId, motionData)`: Initiates individual screw motion
- `cancelBladeScrewMotion(drumId, screwId)`: Cancels individual screw motion

These methods map directly to REST endpoints in the backend API, following a consistent pattern of POST requests for initiating actions and DELETE requests for cancellation. The axios instance is configured with a base URL of '/api/v1' and a 10-second timeout, with request and response interceptors that log all API interactions for debugging purposes.

**Diagram sources**
- [api.js](file://frontend/src/services/api.js#L0-L587)

**Section sources**
- [api.js](file://frontend/src/services/api.js#L250-L315)

## Event Emission and State Management
The HopperControl component communicates with parent components through event emissions, following Vue's event-driven architecture. It emits four types of events that enable coordination with the global application state.

```mermaid
flowchart LR
A[HopperControl] --> |motion-started| B[Parent Component]
A --> |motion-cancelled| B
A --> |success| B
A --> |error| B
B --> C[printJobStore]
C --> D[Global State]
B --> E[UI Feedback]
```

The 'motion-started' and 'motion-cancelled' events provide detailed information about the operation, including the drum ID, operation type (collective or individual), and motion parameters. The 'success' and 'error' events deliver user feedback messages that can be displayed in global notification systems.

These events integrate with the printJobStore.js state management system, which maintains the overall print job state across the application. While HopperControl does not directly modify the store, its events can trigger store updates in parent components like RecoaterView.vue, ensuring that hopper operations are reflected in the global job status.

The component's props (drumId, bladeScrews, connected) are typically provided by a parent component that subscribes to the status store, creating a unidirectional data flow from global state to component props to user interface.

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L100-L147)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L0-L320)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L100-L147)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js#L0-L320)

## Error Handling and User Feedback
The component implements a robust error handling system that provides immediate visual feedback to users when operations fail. This system follows a consistent pattern across all motion control functions.

```mermaid
flowchart TD
A[API Call Failure] --> B[Error Object]
B --> C{Error Structure}
C --> D[response.data.detail]
C --> E[error.message]
C --> F[Default Message]
D --> G[Use response detail]
E --> H[Use error message]
F --> I[Use 'An error occurred']
G --> J[Set errorMessage]
H --> J
I --> J
J --> K[Display in UI]
K --> L[Auto-clear after 5s]
```

The `showError` function extracts meaningful error messages from API responses, prioritizing the `response.data.detail` field when available, falling back to the generic `error.message`, and providing a default message if neither is present. The error message is displayed in a prominent red banner at the bottom of the control card.

A timeout mechanism automatically clears error messages after 5 seconds to prevent persistent error displays that could interfere with subsequent operations. If a new error occurs before the timeout completes, the previous timeout is cleared and a new one is set, ensuring that the most recent error is always displayed for the full duration.

This approach balances the need for immediate error notification with the requirement to keep the interface clean and usable after transient errors.

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L88-L97)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L88-L97)

## Unit Testing Practices
The HopperControl component is thoroughly tested using Vitest and Vue Test Utils, with a comprehensive suite of tests that validate both rendering behavior and functional logic.

```mermaid
flowchart TD
A[Unit Test Suite] --> B[Component Rendering]
A --> C[Collective Motion Controls]
A --> D[Individual Screw Controls]
A --> E[Motion API Calls]
A --> F[Event Emission]
A --> G[Error Handling]
B --> H[Correct title rendering]
B --> I[Blade screws display]
B --> J[Status indicator]
B --> K[Disabled controls when disconnected]
E --> L[API call parameters]
E --> M[Correct drum ID]
E --> N[Correct motion data]
F --> O[Event payload structure]
F --> P[Event parameter accuracy]
G --> Q[Error message display]
G --> R[Auto-clear functionality]
```

The test suite uses mocking to isolate the component from external dependencies, particularly the apiService module. The mock implementation tracks function calls and can be configured to simulate both successful and failed API responses.

Key testing practices include:
- Verification of correct component rendering with default props
- Testing of conditional UI elements (e.g., distance input visibility based on mode)
- Validation of API call parameters and arguments
- Confirmation of proper event emission with correct payloads
- Testing of error handling and message display
- Verification of automatic error clearance after timeout

The tests cover edge cases such as disconnected states (where all controls are disabled) and different motion modes (where the distance input is hidden in homing mode). The use of fake timers in the error handling tests allows precise control over the timeout mechanism, ensuring reliable testing of the auto-clear functionality.

**Diagram sources**
- [HopperControl.test.js](file://frontend/src/components/__tests__/HopperControl.test.js#L0-L343)

**Section sources**
- [HopperControl.test.js](file://frontend/src/components/__tests__/HopperControl.test.js#L0-L343)

## Potential Issues and Optimization
While the HopperControl component is well-designed, several potential issues and optimization opportunities exist.

### Sensor Data Mismatches
The component relies on prop updates from parent components for blade screw position and running status. If there are delays in sensor data updates or network latency, the displayed state may temporarily mismatch the actual machine state. Implementing a timestamp-based freshness indicator could help operators identify potentially stale data.

### Delayed Actuation Feedback
The current implementation provides feedback only after API calls complete, which may create a perception of sluggish response. Adding optimistic UI updates (immediately showing the expected state change) with rollback capability on failure could improve perceived responsiveness.

### Rendering Optimization
The component uses Vue's reactivity system efficiently, but during continuous monitoring, unnecessary re-renders could occur. Since the component primarily responds to user interactions and prop changes, the current implementation is already optimized. However, for high-frequency updates, consider implementing a custom `shouldUpdate` check or using Vue's `v-memo` directive on stable portions of the template.

### Enhancement Opportunities
1. **Debounced Inputs**: For distance inputs, implement debouncing to prevent excessive API calls during rapid value changes
2. **Batch Operations**: Add functionality for batch operations across multiple hoppers
3. **Presets**: Implement motion presets for common operations to reduce input errors
4. **Visual Feedback**: Enhance the status indicator with more granular states (e.g., "Initializing", "Moving", "Complete")

The component's current design follows Vue best practices with proper separation of concerns, making it maintainable and extensible for future enhancements.

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue#L1-L212)
- [HopperControl.test.js](file://frontend/src/components/__tests__/HopperControl.test.js#L0-L343)