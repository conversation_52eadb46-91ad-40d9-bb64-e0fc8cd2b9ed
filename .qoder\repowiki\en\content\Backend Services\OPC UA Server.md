# OPC UA Server

<cite>
**Referenced Files in This Document**   
- [opcua_server.py](file://backend\app\services\opcua_server.py) - *Updated with type coercion fixes in commit e3fbefd*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Configuration definitions for OPC UA server*
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py) - *Coordinator interface for OPC UA communication*
- [test_opcua_infrastructure.py](file://backend\tests\test_opcua_infrastructure.py) - *Test cases for OPC UA components*
</cite>

## Update Summary
**Changes Made**   
- Updated **Performance Considerations** section to reflect type coercion improvements in variable write operations
- Added detailed explanation of type coercion mechanism in **OPCUAServerManager Analysis**
- Enhanced **Variable Write Optimization** flowchart to include new type coercion logic
- Updated **Troubleshooting Guide** with information about BadTypeMismatch error prevention
- Added reference to recent commit fixing type coercion issues

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The OPC UA Server component in the APIRecoater_Ethernet system provides a simulated OPC UA server instance for development, testing, and demonstration purposes. It enables seamless communication between the backend application and the TwinCAT PLC by exposing mock nodes that emulate real-world device behavior. This documentation details the implementation, configuration, and integration of the OPC UA server, focusing on its role in multi-material print job coordination. The server exposes coordination variables for job control, recoater coordination, and error handling, allowing for bidirectional communication between the FastAPI backend and the PLC client.

## Project Structure
The OPC UA server implementation is organized within the backend services of the APIRecoater_Ethernet application. The core components are located in the `backend/app/services/` and `backend/app/config/` directories, with supporting test files in the `backend/tests/` directory.

``mermaid
graph TD
subgraph "Backend"
subgraph "Services"
OPCUAServerManager[OPCUAServerManager]
OPCUACoordinator[OPCUACoordinator]
end
subgraph "Config"
OPCUAConfig[opcua_config.py]
end
subgraph "Tests"
OPCUATests[test_opcua_infrastructure.py]
end
end
OPCUAServerManager --> OPCUAConfig
OPCUACoordinator --> OPCUAServerManager
OPCUACoordinator --> OPCUAConfig
OPCUATests --> OPCUAServerManager
OPCUATests --> OPCUACoordinator
style OPCUAServerManager fill:#f9f,stroke:#333
style OPCUACoordinator fill:#bbf,stroke:#333
style OPCUAConfig fill:#9f9,stroke:#333
style OPCUATests fill:#f96,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

## Core Components
The OPC UA server implementation consists of three primary components: the server manager, configuration definitions, and coordinator interface. These components work together to provide a robust simulation environment for OPC UA communication.

### OPCUAServerManager
The `OPCUAServerManager` class is responsible for managing the OPC UA server lifecycle and variable operations. It initializes the server, creates coordination variables, and handles read/write operations.

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L189-L519)

### OPCUA Configuration
The `opcua_config.py` file defines the server configuration and coordination variables. It includes data classes for server settings and variable definitions, along with helper functions for configuration management.

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L293)

### OPCUACoordinator
The `OPCUACoordinator` class provides a high-level interface for OPC UA communication, abstracting the low-level server operations and offering convenience methods for common coordination tasks.

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L589)

## Architecture Overview
The OPC UA server architecture follows a layered design pattern, with clear separation between the protocol layer, business logic layer, and application layer. This design enables both low-level control and high-level coordination.

``mermaid
graph TD
subgraph "Application Layer"
API[FastAPI Endpoints]
end
subgraph "Business Logic Layer"
Coordinator[OPCUACoordinator]
end
subgraph "Protocol Layer"
ServerManager[OPCUAServerManager]
AsyncUAServer[asyncua.Server]
end
subgraph "External"
PLC[TwinCAT PLC]
end
API --> Coordinator
Coordinator --> ServerManager
ServerManager --> AsyncUAServer
AsyncUAServer < --> PLC
style API fill:#f9f,stroke:#333
style Coordinator fill:#bbf,stroke:#333
style ServerManager fill:#f96,stroke:#333
style AsyncUAServer fill:#9f9,stroke:#333
style PLC fill:#ff9,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)

## Detailed Component Analysis

### OPCUAServerManager Analysis
The `OPCUAServerManager` class implements the core functionality of the OPC UA server, managing server lifecycle and variable operations.

#### Class Diagram
``mermaid
classDiagram
class OPCUAServerManager {
+config : OPCUAServerConfig
+server : Optional[Server]
+namespace_idx : int
+variable_nodes : Dict[str, Any]
+_running : bool
+_restart_count : int
+_heartbeat_task : Optional[asyncio.Task]
+__init__(config)
+start_server() bool
+stop_server() None
+write_variable(name, value) bool
+read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
-_create_coordination_variables() None
-_get_ua_data_type(data_type) ua.VariantType
-_heartbeat_loop() None
-_cleanup() None
-_handle_server_error(error) None
}
class OPCUAServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+certificate_path : str
+private_key_path : str
+connection_timeout : float
+session_timeout : float
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
class CoordinationVariable {
+name : str
+node_id : str
+data_type : str
+initial_value : Any
+writable : bool
+description : str
}
OPCUAServerManager --> OPCUAServerConfig : "uses"
OPCUAServerManager --> CoordinationVariable : "creates"
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L189-L519)
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L293)

#### Server Initialization Sequence
``mermaid
sequenceDiagram
participant App as "Application"
participant Coordinator as "OPCUACoordinator"
participant ServerManager as "OPCUAServerManager"
participant AsyncUA as "asyncua.Server"
App->>Coordinator : connect()
Coordinator->>ServerManager : start_server()
ServerManager->>AsyncUA : Server()
ServerManager->>AsyncUA : init()
ServerManager->>AsyncUA : set_endpoint()
ServerManager->>AsyncUA : set_server_name()
ServerManager->>AsyncUA : register_namespace()
ServerManager->>ServerManager : _create_coordination_variables()
ServerManager->>AsyncUA : start()
ServerManager->>ServerManager : _heartbeat_loop()
ServerManager-->>Coordinator : True
Coordinator-->>App : True
Note over App,AsyncUA : Server successfully started with coordination variables
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L200-L250)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L50-L80)

### OPCUA Configuration Analysis
The configuration system provides a flexible way to define OPC UA server settings and coordination variables, with support for environment variable overrides.

#### Configuration Flowchart
``mermaid
flowchart TD
Start([Configuration Start]) --> LoadEnv["Load Environment Variables"]
LoadEnv --> CheckVar{"Variable Exists?"}
CheckVar --> |Yes| UseEnv["Use Environment Value"]
CheckVar --> |No| UseDefault["Use Default Value"]
UseEnv --> ApplyConfig["Apply to Configuration"]
UseDefault --> ApplyConfig
ApplyConfig --> NextVar["Process Next Variable"]
NextVar --> MoreVars{"More Variables?"}
MoreVars --> |Yes| CheckVar
MoreVars --> |No| EndConfig["Configuration Complete"]
EndConfig --> End([Configuration End])
style Start fill:#f9f,stroke:#333
style End fill:#f9f,stroke:#333
style ApplyConfig fill:#bbf,stroke:#333
```

**Diagram sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L150-L293)

### OPCUACoordinator Analysis
The `OPCUACoordinator` class provides a high-level interface for OPC UA communication, simplifying common coordination tasks.

#### Job Management Sequence
``mermaid
sequenceDiagram
participant API as "FastAPI Endpoint"
participant Coordinator as "OPCUACoordinator"
participant ServerManager as "OPCUAServerManager"
participant PLC as "TwinCAT PLC"
API->>Coordinator : set_job_active(total_layers=150)
Coordinator->>Coordinator : Write job_active=True
Coordinator->>ServerManager : write_variable("job_active", True)
ServerManager->>ServerManager : Validate and coerce value
ServerManager->>ServerManager : Write to OPC UA node
ServerManager-->>Coordinator : Success
Coordinator->>Coordinator : Write total_layers=150
Coordinator->>ServerManager : write_variable("total_layers", 150)
ServerManager->>ServerManager : Validate and coerce value
ServerManager->>ServerManager : Write to OPC UA node
ServerManager-->>Coordinator : Success
Coordinator->>Coordinator : Write current_layer=0
Coordinator->>ServerManager : write_variable("current_layer", 0)
ServerManager->>ServerManager : Validate and coerce value
ServerManager->>ServerManager : Write to OPC UA node
ServerManager-->>Coordinator : Success
Coordinator-->>API : True
PLC->>ServerManager : Read variables
ServerManager-->>PLC : Return current values
Note over API,PLC : Job successfully activated with coordination variables
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L150-L180)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L300-L400)

## Dependency Analysis
The OPC UA server components have a clear dependency hierarchy, with the coordinator depending on the server manager, and both depending on the configuration module.

``mermaid
graph TD
OPCUACoordinator --> OPCUAServerManager
OPCUACoordinator --> OPCUAConfig
OPCUAServerManager --> OPCUAConfig
OPCUATests --> OPCUAServerManager
OPCUATests --> OPCUACoordinator
OPCUAServerManager --> asyncua
OPCUACoordinator --> asyncua
style OPCUACoordinator fill:#bbf,stroke:#333
style OPCUAServerManager fill:#f96,stroke:#333
style OPCUAConfig fill:#9f9,stroke:#333
style OPCUATests fill:#f96,stroke:#333
style asyncua fill:#ff9,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)

## Performance Considerations
The OPC UA server implementation includes several performance considerations to ensure reliable operation in a production environment.

### Server Restart Mechanism
The server includes an auto-restart mechanism that attempts to recover from failures, with configurable parameters for restart delay and maximum attempts.

``mermaid
stateDiagram-v2
[*] --> Stopped
Stopped --> Starting : start_server()
Starting --> Running : Success
Starting --> Failed : Exception
Running --> Stopped : stop_server()
Failed --> Restarting : auto_restart enabled
Restarting --> Starting : restart_delay elapsed
Restarting --> MaxAttempts : max_restart_attempts reached
MaxAttempts --> [*]
note right of Failed
Error logged and restart
attempt counter incremented
end note
note right of Restarting
Wait restart_delay seconds
before attempting restart
end note
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L450-L480)

### Variable Write Optimization
The variable write operation includes type coercion and validation to prevent BadTypeMismatch errors, ensuring compatibility between the backend and PLC. The updated implementation in commit `e3fbefd` adds explicit type coercion for all variable writes.

``mermaid
flowchart TD
Start([Write Variable]) --> CheckRunning{"Server Running?"}
CheckRunning --> |No| ReturnFalse["Return False"]
CheckRunning --> |Yes| CheckVariable{"Variable Exists?"}
CheckVariable --> |No| ReturnFalse
CheckVariable --> |Yes| GetExpectedType["Get Expected Data Type"]
GetExpectedType --> CoerceValue["Coerce Value to Expected Type"]
CoerceValue --> CreateVariant["Create UA Variant"]
CreateVariant --> WriteValue["Write Value to Node"]
WriteValue --> LogUpdate["Log Variable Update"]
LogUpdate --> ReturnTrue["Return True"]
ReturnFalse --> End([End])
ReturnTrue --> End
style Start fill:#f9f,stroke:#333
style End fill:#f9f,stroke:#333
style ReturnTrue fill:#9f9,stroke:#333
style ReturnFalse fill:#f96,stroke:#333
```

**Diagram sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L350-L400)

## Troubleshooting Guide
This section provides guidance for common issues encountered when working with the OPC UA server.

### Connection Issues
When the OPC UA server fails to start or the PLC cannot connect, check the following:

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L200-L250)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L50-L80)

### Variable Access Problems
When reading or writing variables fails, verify the following:
- The server is running before attempting variable operations
- The variable name exists in the COORDINATION_VARIABLES list
- The value type matches the expected data type defined in configuration
- The type coercion mechanism in `write_variable()` is functioning correctly (added in commit `e3fbefd` to prevent BadTypeMismatch errors)

**Section sources**
- [opcua_server.py](file://backend\app\services\opcua_server.py#L350-L400)
- [opcua_config.py](file://backend\app\config\opcua_config.py#L177-L232)

## Conclusion
The OPC UA server implementation in APIRecoater_Ethernet provides a comprehensive simulation environment for development, testing, and demonstration purposes. By exposing mock nodes for sensors, actuators, and control methods as defined in opcua_config.py, it effectively emulates real-world device behavior. The integration with OPCUACoordinator enables seamless switching between real and simulated environments, making it ideal for automated testing and training scenarios. The configuration options for simulation parameters, fault injection, and timing delays provide flexibility for various use cases. Recent updates have improved reliability by adding type coercion to prevent BadTypeMismatch errors during variable writes. While this implementation serves well for development and testing, it should be noted that production OPC UA infrastructure would require additional security measures, performance optimizations, and redundancy features.