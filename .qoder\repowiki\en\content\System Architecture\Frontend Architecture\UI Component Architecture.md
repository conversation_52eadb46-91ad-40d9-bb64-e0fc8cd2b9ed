# UI Component Architecture

<cite>
**Referenced Files in This Document**   
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Organization and Composition](#component-organization-and-composition)
3. [Control-Specific Components](#control-specific-components)
   - [DrumControl](#drumcontrol)
   - [HopperControl](#hoppercontrol)
   - [LevelerControl](#levelercontrol)
4. [Utility Components](#utility-components)
   - [FileUploadColumn](#fileuploadcolumn)
   - [JobProgressDisplay](#jobprogressdisplay)
   - [CriticalErrorModal](#criticalerrormodal)
5. [State Management and Data Flow](#state-management-and-data-flow)
6. [Event Emission and Communication Patterns](#event-emission-and-communication-patterns)
7. [Responsive Design and Accessibility](#responsive-design-and-accessibility)
8. [Testing and Reusability](#testing-and-reusability)
9. [Integration with Global Stores and API Services](#integration-with-global-stores-and-api-services)
10. [Conclusion](#conclusion)

## Introduction
The UI component system in APIRecoater_Ethernet is a Vue.js-based architecture designed to provide an intuitive and responsive interface for controlling a multi-material recoater system. This documentation details the structure, functionality, and integration patterns of the core UI components, focusing on control-specific components (DrumControl, HopperControl, LevelerControl) and utility components (FileUploadColumn, JobProgressDisplay, CriticalErrorModal). The system leverages Vue 3's Composition API, Pinia for state management, and a centralized API service layer to ensure maintainability, reusability, and real-time responsiveness.

## Component Organization and Composition

The frontend components are organized within the `frontend/src/components` directory, following a feature-based structure. Components are designed as self-contained units with clear responsibilities, utilizing Vue's single-file component (SFC) pattern. They are composed within views such as `RecoaterView.vue` and `PrintView.vue`, which orchestrate the overall layout and data flow.

The component hierarchy is designed for reusability and consistency. A common card-based design pattern is used across control components (`DrumControl`, `HopperControl`, `LevelerControl`), featuring a header with a title and status indicator, and a content area with grouped controls and information displays. This ensures a uniform user experience and simplifies styling and maintenance.

```mermaid
graph TD
A[RecoaterView/PrintView] --> B[DrumControl]
A --> C[HopperControl]
A --> D[LevelerControl]
A --> E[FileUploadColumn]
A --> F[JobProgressDisplay]
A --> G[CriticalErrorModal]
B --> H[apiService]
C --> H
D --> H
E --> H
F --> I[printJobStore]
G --> I
H --> J[Backend API]
I --> J
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)

## Control-Specific Components

### DrumControl
The `DrumControl` component provides a comprehensive interface for managing a specific recoater drum. It displays real-time status information and offers controls for motion and pressure systems.

**Props Interface**
- `drumId`: Number (required) - Identifies the drum instance (0, 1, or 2).
- `drumStatus`: Object - Contains current drum status (position, circumference, running state).
- `motionData`: Object - Holds motion parameters (not directly used in current implementation).
- `ejectionData`: Object - Contains ejection pressure data (current value, target, unit).
- `suctionData`: Object - Contains suction pressure data (current value, target).
- `connected`: Boolean - Indicates connection status to the backend.

**Reactive State**
The component uses `ref` from Vue's Composition API to manage its internal state:
- `motionParams`: An object containing the current motion mode (relative, absolute, turns, speed, homing), speed, distance, and turns.
- `ejectionTarget` and `ejectionUnit`: Track the user-input target pressure and unit for ejection.
- `suctionTarget`: Tracks the user-input target pressure for suction.
- `errorMessage`: A string that displays transient error messages, automatically clearing after 5 seconds.

**Computed Properties and Watchers**
While this component primarily uses reactive refs, it relies on the `drumStatus` prop, which is likely a computed property from a store in the parent view. No explicit watchers are defined, as state updates are driven by prop changes and user interactions.

**Event Emission Patterns**
The component emits several events to communicate with parent components:
- `motion-started`: Fired when a motion command is successfully sent.
- `motion-cancelled`: Fired when a motion cancellation command is successfully sent.
- `pressure-set`: Fired when a pressure setting command is successfully sent.
- `success`: Provides user feedback for successful operations.
- `error`: Propagates error information for handling by parent components.

**Visual Feedback and Accessibility**
The component provides visual feedback through:
- A status indicator (green dot for running, gray for stopped).
- Disabled states for controls when the drum is running or disconnected.
- Error messages displayed in a red banner that auto-dismisses.
- Hover effects on buttons and cards for interactive feedback.
Accessibility is supported through semantic HTML, ARIA labels on interactive elements, and clear visual hierarchies.

```mermaid
classDiagram
class DrumControl {
+drumId : Number
+drumStatus : Object
+connected : Boolean
-motionParams : Ref~Object~
-ejectionTarget : Ref~Number~
-ejectionUnit : Ref~String~
-suctionTarget : Ref~Number~
-errorMessage : Ref~String~
+startMotion() : Promise
+cancelMotion() : Promise
+setEjectionPressure() : Promise
+setSuctionPressure() : Promise
-showError(error) : void
}
DrumControl --> apiService : "uses"
```

**Diagram sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)

### HopperControl
The `HopperControl` component manages the blade screws associated with a recoater drum's hopper. It supports both collective (all screws together) and individual screw control.

**Props Interface**
- `drumId`: Number (required) - Identifies the drum instance.
- `bladeScrews`: Array - Contains status objects for each blade screw (id, position, running state).
- `connected`: Boolean - Indicates connection status.

**Reactive State**
- `collectiveMotion`: An object with mode and distance for collective motion.
- `individualMotion`: An object with distance settings for individual screws (indexed by screw ID).
- `errorMessage`: A string for displaying transient error messages.

**Computed Properties**
- `isBladeRunning`: A computed property that returns `true` if any blade screw is currently in motion.
- `getScrewRunning(id)`: A function that returns the running state of a specific screw.

**Event Emission Patterns**
The component emits standard events:
- `motion-started`: When a motion command (collective or individual) is sent.
- `motion-cancelled`: When a motion cancellation command is sent.
- `success`: For successful operations.
- `error`: For error propagation.

**Visual Feedback and Accessibility**
Visual feedback includes a status indicator based on the `isBladeRunning` computed state, disabled controls during motion, and error messages. The layout clearly separates collective and individual controls, enhancing usability.

```mermaid
classDiagram
class HopperControl {
+drumId : Number
+bladeScrews : Array
+connected : Boolean
-collectiveMotion : Ref~Object~
-individualMotion : Ref~Object~
-errorMessage : Ref~String~
+startCollectiveMotion() : Promise
+cancelCollectiveMotion() : Promise
+startIndividualMotion(screwId) : Promise
+cancelIndividualMotion(screwId) : Promise
-showError(error) : void
isBladeRunning : Computed<boolean>
getScrewRunning(id) : boolean
}
HopperControl --> apiService : "uses"
```

**Diagram sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)

**Section sources**
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)

### LevelerControl
The `LevelerControl` component manages the pressure system for the leveler, a critical component for ensuring uniform material distribution.

**Props Interface**
- `pressureData`: Object - Contains current pressure, target, and maximum values.
- `sensorData`: Object - Contains the state of the magnetic sensor.
- `connected`: Boolean - Indicates connection status.

**Reactive State**
- `targetPressure`: A ref for the user-input target pressure.
- `isSettingPressure`: A boolean ref that disables controls during an API call.
- `errorMessage`: A string for transient error messages.

**Computed Properties**
- `isValidPressure`: A computed property that validates the `targetPressure` against the minimum (0) and maximum values from `pressureData`.

**Event Emission Patterns**
- `pressure-set`: Emitted when a new pressure target is successfully set.
- `success`: For user feedback.
- `error`: For error propagation.

**Visual Feedback and Accessibility**
The component provides a clear visual indication of the sensor state with a status dot. The "Set" button displays "Setting..." during the API call, and is disabled if the input is invalid or the system is busy. Error messages are prominently displayed.

```mermaid
classDiagram
class LevelerControl {
+pressureData : Object
+sensorData : Object
+connected : Boolean
-targetPressure : Ref~Number~
-isSettingPressure : Ref~Boolean~
-errorMessage : Ref~String~
+setPressure() : Promise
-showError(error) : void
isValidPressure : Computed<boolean>
}
LevelerControl --> apiService : "uses"
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)

## Utility Components

### FileUploadColumn
The `FileUploadColumn` component facilitates the upload, download, and deletion of geometry files (PNG or CLI) for a specific drum.

**Props Interface**
- `drumId`: Number (required, validated to 0, 1, or 2).
- `isConnected`: Boolean - Controls the enabled state of all actions.
- `isLoading`: Boolean - Indicates global loading state, disabling all buttons.

**Reactive State**
- `selectedFile`: A ref that holds the currently selected file object.
- `fileInput`: A ref to the file input DOM element, used to clear the selection programmatically.
- `printJobStore`: A Pinia store instance for accessing the last uploaded file name.

**Event Emission Patterns**
- `upload`: Emitted with the drum ID and file object when the upload button is clicked.
- `download`: Emitted with the drum ID when the download button is clicked.
- `delete`: Emitted with the drum ID when the delete button is confirmed.

**Visual Feedback and Accessibility**
The component uses a disabled overlay when disconnected. File selection is clearly indicated, and a confirmation dialog prevents accidental deletion. Button states (uploading, downloading, deleting) are reflected in their text.

**Section sources**
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)

### JobProgressDisplay
The `JobProgressDisplay` component provides a real-time overview of an active multi-material print job.

**Reactive State and Computed Properties**
This component relies entirely on the `printJobStore` for its state. Key computed properties from the store include:
- `jobProgress`: Calculated percentage based on current and total layers.
- `allDrumsReady`: True if all drums are ready for printing.
- `hasActiveJob`: True if a job is currently active.

**Lifecycle and Data Flow**
The component uses `onMounted` and `onUnmounted` hooks to set up and tear down a polling interval. It calls `printJobStore.fetchJobStatus()` every 2 seconds to keep the UI updated during an active job, demonstrating a pattern for real-time data synchronization.

**Visual Feedback and Accessibility**
The component features a progress bar, detailed drum status cards with color-coded borders, and a time information section. It gracefully handles the "no active job" state with a clear message. The UI is responsive, adapting to smaller screens.

```mermaid
sequenceDiagram
participant JobProgressDisplay
participant printJobStore
participant apiService
participant Backend
loop Every 2 seconds when job is active
JobProgressDisplay->>printJobStore : fetchJobStatus()
printJobStore->>apiService : getMultiMaterialJobStatus()
apiService->>Backend : GET /api/v1/multi-material-job/status
Backend-->>apiService : Job status data
apiService-->>printJobStore : Response
printJobStore->>printJobStore : updateJobStatus(data)
printJobStore-->>JobProgressDisplay : Updated state
end
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [api.js](file://frontend/src/services/api.js)

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)

### CriticalErrorModal
The `CriticalErrorModal` component displays a full-screen modal when a critical system error occurs, preventing further operations until resolved.

**Reactive State**
The component's visibility is controlled by `printJobStore.hasCriticalError`. It uses the store's `errorFlags` to determine the type of error (backend or PLC) and display appropriate information.

**Event Emission Patterns**
This component does not emit events. Instead, it calls actions directly on the `printJobStore`:
- `clearErrorFlagsAPI()`: To attempt to clear the error flags via the API.
- `closeCriticalModal()`: To acknowledge the error without clearing it.

**Visual Feedback and Accessibility**
The modal uses a high-contrast design with a red gradient header. It prevents closing by clicking the overlay, ensuring the user acknowledges the error. The content is structured with clear headings, impact statements, and action steps. It is fully accessible with proper ARIA roles and keyboard navigation.

**Section sources**
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)

## State Management and Data Flow
The application uses Pinia for global state management. The `printJobStore` is the central store that holds the state for the multi-material job, error flags, and file upload history.

**State Properties**
- `multiMaterialJob`: An object containing the job's active state, ID, progress, status, and drum-specific statuses.
- `errorFlags`: An object tracking backend and PLC errors, along with a message and a flag to show the critical modal.
- `uploadedFiles` and `lastUploadedFiles`: Objects tracking file metadata for each drum.

**Computed Properties**
The store defines numerous computed properties (e.g., `isJobActive`, `hasCriticalError`, `canStartJob`) that encapsulate business logic, making it reusable across components and keeping the template logic simple.

**Actions**
The store provides both synchronous actions (e.g., `setErrorFlags`, `setFileUploaded`) for updating local state and asynchronous actions (e.g., `startMultiMaterialJob`, `fetchJobStatus`) that interact with the API service. This separation of concerns ensures that components only need to call high-level actions without knowing the underlying API details.

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

## Event Emission and Communication Patterns
The component system employs a clear communication pattern:
1. **Parent-to-Child**: Data is passed down via props. Parent views (e.g., `RecoaterView`) fetch data from the store or API and pass it to child components.
2. **Child-to-Parent**: Child components emit events for significant actions (e.g., `upload`, `motion-started`). The parent component listens for these events and responds by updating its state or calling store actions.
3. **Global State**: Components directly interact with the Pinia store for read operations (using `computed` properties) and sometimes for write operations (calling store actions directly, as seen in `CriticalErrorModal`).
4. **API Integration**: All API calls are funneled through the `apiService` module, which provides a consistent interface and handles logging and error handling.

This pattern ensures loose coupling between components while maintaining a predictable data flow.

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [FileUploadColumn.vue](file://frontend/src/components/FileUploadColumn.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)

## Responsive Design and Accessibility
The UI components are designed with responsiveness in mind:
- **Grid Layouts**: Components use CSS Grid (`info-grid`, `drum-grid`) to create flexible, responsive layouts that adapt to different screen sizes.
- **Media Queries**: CSS includes media queries (e.g., `@media (max-width: 768px)`) to adjust layouts, font sizes, and spacing on mobile devices.
- **Flexbox**: Flexbox is used extensively for alignment and spacing within components.

Accessibility features include:
- **Semantic HTML**: Proper use of headings, labels, and form elements.
- **ARIA Attributes**: Roles (`role="dialog"`, `aria-modal="true"`) and labels (`aria-labelledby`, `aria-label`) are used, especially in the `CriticalErrorModal`.
- **Keyboard Navigation**: Interactive elements are focusable and operable via keyboard.
- **Color Contrast**: Sufficient contrast between text and background colors.
- **Focus States**: Visible focus indicators on interactive elements.

**Section sources**
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)

## Testing and Reusability
The components are designed for high reusability:
- **Self-Contained**: Each component manages its own state and logic.
- **Clear Props/Events**: Well-defined interfaces make components easy to integrate into different views.
- **Consistent Styling**: Shared CSS patterns ensure visual consistency.

The presence of test files (e.g., `DrumControl.test.js`) in the `__tests__` directory indicates a commitment to testing. These tests likely use Vue Test Utils and Vitest to verify component rendering, user interactions, and event emissions, ensuring reliability and preventing regressions.

**Section sources**
- [DrumControl.test.js](file://frontend/src/components/__tests__/DrumControl.test.js)
- [HopperControl.test.js](file://frontend/src/components/__tests__/HopperControl.test.js)
- [LevelerControl.test.js](file://frontend/src/components/__tests__/LevelerControl.test.js)

## Integration with Global Stores and API Services
All components integrate seamlessly with the global state and API services:
- **Pinia Store**: Components import and use the `printJobStore` for accessing shared state (e.g., `JobProgressDisplay`, `CriticalErrorModal`) or for tracking history (e.g., `FileUploadColumn`).
- **API Service**: Components import the `apiService` module to make HTTP requests. This centralization allows for easy mocking in tests and consistent error handling.

The `apiService` module acts as a facade for the backend API, defining methods for every endpoint (e.g., `setDrumMotion`, `startMultiMaterialJob`). This abstraction layer decouples the UI components from the specifics of the HTTP client (axios), making the codebase more maintainable and testable.

**Section sources**
- [api.js](file://frontend/src/services/api.js)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [DrumControl.vue](file://frontend/src/components/DrumControl.vue)
- [HopperControl.vue](file://frontend/src/components/HopperControl.vue)
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)

## Conclusion
The UI component system in APIRecoater_Ethernet is a well-structured, maintainable, and user-friendly architecture. It effectively leverages Vue 3's Composition API, Pinia for state management, and a centralized API service to create a responsive and reliable interface for controlling a complex recoater system. The clear separation of concerns, consistent design patterns, and emphasis on accessibility and responsiveness make it a robust foundation for the application. Future enhancements could include more sophisticated error recovery workflows and enhanced real-time monitoring visualizations.