# Status API

<cite>
**Referenced Files in This Document**   
- [status.py](file://backend/app/api/status.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [StatusView.vue](file://frontend/src/views/StatusView.vue)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)
- [status.js](file://frontend/src/stores/status.js)
- [api.js](file://frontend/src/services/api.js)
- [test_status_api.py](file://backend/tests/test_status_api.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [API Endpoints](#api-endpoints)
3. [Status Data Aggregation](#status-data-aggregation)
4. [WebSocket Real-Time Updates](#websocket-real-time-updates)
5. [Frontend Integration](#frontend-integration)
6. [Testing and Error Scenarios](#testing-and-error-scenarios)
7. [Usage Examples](#usage-examples)

## Introduction

The Status API provides real-time monitoring and health information for the recoater system. It enables clients to retrieve system status, perform health checks, and control server state. The API integrates with OPC UA servers through a polling service and delivers real-time updates via WebSocket connections. This documentation details the endpoints, data flow, caching behavior, and frontend integration.

**Section sources**
- [status.py](file://backend/app/api/status.py#L1-L154)

## API Endpoints

### GET /api/status/
Retrieves the current status of the recoater system.

**Response Schema:**
```json
{
  "connected": true,
  "recoater_status": {
    "state": "ready",
    "timestamp": "2025-07-09T14:30:00Z"
  },
  "backend_status": "operational"
}
```

**Response Codes:**
- `200 OK`: Normal operation with full status
- `200 OK` with partial data: Connection to recoater failed (degraded state)
- `502 Bad Gateway`: Recoater API error
- `500 Internal Server Error`: Unexpected backend error

### GET /api/status/health
Performs a health check of the recoater connection.

**Response Schema:**
```json
{
  "backend_healthy": true,
  "recoater_healthy": true,
  "overall_healthy": true
}
```

**Response Codes:**
- `200 OK`: Always returns 200, even with errors (provides error details in response)

### POST /api/status/state
Controls the server state (restart or shutdown).

**Request Parameters:**
- `action`: "restart" or "shutdown"

**Response Schema:**
```json
{
  "success": true,
  "action": "restart",
  "status": "accepted",
  "message": "Server restart command accepted"
}
```

**Response Codes:**
- `200 OK`: Command accepted
- `400 Bad Request`: Invalid action
- `503 Service Unavailable`: Connection error
- `500 Internal Server Error`: Unexpected error

```mermaid
sequenceDiagram
participant Client
participant StatusAPI
participant RecoaterClient
Client->>StatusAPI : GET /api/status/
StatusAPI->>RecoaterClient : get_state()
alt Success
RecoaterClient-->>StatusAPI : Status data
StatusAPI-->>Client : 200 OK with full status
end
alt Connection Error
RecoaterClient--xStatusAPI : ConnectionError
StatusAPI-->>Client : 200 OK with partial status
end
alt API Error
RecoaterClient--xStatusAPI : APIError
StatusAPI-->>Client : 502 Bad Gateway
end
```

**Diagram sources**
- [status.py](file://backend/app/api/status.py#L25-L75)

**Section sources**
- [status.py](file://backend/app/api/status.py#L25-L154)

## Status Data Aggregation

The status data is aggregated from OPC UA servers through the `StatusPollingService` which runs in the background and periodically polls the recoater hardware.

### Architecture

```mermaid
classDiagram
class StatusPollingService {
+websocket_manager : WebSocketConnectionManager
+data_gatherer : RecoaterDataGatherer
+poll_interval : float
+_running : bool
+start() : Task
+stop() : void
+_polling_loop() : void
+_poll_and_broadcast() : void
}
class RecoaterDataGatherer {
+gather_all_data(client, types) : Dict
+construct_status_message(data) : Dict
+gather_single_drum_data(client, id) : Dict
+gather_all_drum_data(client) : Dict
+gather_leveler_data(client) : Dict
+gather_print_data(client) : Dict
+gather_axis_data(client) : Dict
}
class WebSocketConnectionManager {
+active_connections : List[WebSocket]
+connection_subscriptions : Dict[WebSocket, Set[str]]
+broadcast(message) : void
+get_required_data_types() : Set[str]
}
StatusPollingService --> WebSocketConnectionManager : "uses"
StatusPollingService --> RecoaterDataGatherer : "uses"
RecoaterDataGatherer --> RecoaterClient : "gathers from"
WebSocketConnectionManager --> Client : "broadcasts to"
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L15-L124)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L15-L229)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L15-L146)

### Polling Process

1. The `StatusPollingService` runs a background loop (`_polling_loop`) at a configurable interval (default: 1.0 second)
2. In each cycle, it calls `_poll_and_broadcast` to:
   - Get required data types from active WebSocket connections
   - Gather all required data through `RecoaterDataGatherer`
   - Construct a status message and broadcast via `WebSocketConnectionManager`
3. The polling interval can be configured via the `WEBSOCKET_POLL_INTERVAL` environment variable

The `RecoaterDataGatherer` efficiently gathers data by:
- Using `asyncio.to_thread` for synchronous client calls
- Gathering data types conditionally based on client subscriptions
- Providing safe API call wrappers with error handling

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L15-L124)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L15-L229)

## WebSocket Real-Time Updates

The system provides real-time status updates through WebSocket connections, complementing the REST polling approach.

### Connection Management

The `WebSocketConnectionManager` handles:
- Connection lifecycle (accept, disconnect)
- Subscription management for different data types
- Message broadcasting with subscription filtering
- Automatic reconnection attempts

Clients can subscribe to specific data types:
- `status`: Basic system status (always included)
- `axis`: Axis control data
- `drum`: Drum and blade status
- `leveler`: Leveler pressure and sensor data
- `print`: Print job and layer parameters

### Message Flow

```mermaid
sequenceDiagram
participant Client
participant WebSocketManager
participant StatusPoller
participant DataGatherer
StatusPoller->>DataGatherer : gather_all_data()
DataGatherer-->>StatusPoller : Collected data
StatusPoller->>StatusPoller : construct_status_message()
StatusPoller->>WebSocketManager : broadcast(message)
WebSocketManager->>WebSocketManager : _filter_message_for_connection()
WebSocketManager->>Client : send_json(filtered_message)
Client->>WebSocketManager : connect()
WebSocketManager->>Client : accept()
WebSocketManager->>StatusPoller : update_required_data_types()
Client->>WebSocketManager : update_subscription(['drum', 'leveler'])
WebSocketManager->>StatusPoller : update_required_data_types()
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L15-L146)
- [status_poller.py](file://backend/app/services/status_poller.py#L15-L124)

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L15-L146)
- [status_poller.py](file://backend/app/services/status_poller.py#L15-L124)

## Frontend Integration

The frontend components display live system health and integrate with the Status API through a Pinia store.

### Component Hierarchy

```mermaid
graph TD
StatusView[StatusView.vue] --> StatusIndicator[StatusIndicator.vue]
StatusView --> StatusStore[status.js]
StatusIndicator --> StatusStore
StatusStore --> ApiService[api.js]
StatusStore --> WebSocket[WebSocket]
ApiService --> Backend[Status API]
```

### Key Components

**StatusIndicator.vue**: Displays a status dot and text in the application header
- Shows connection status (Connected/Disconnected/Error)
- Provides tooltip with detailed error information
- Manages WebSocket connection lifecycle

**StatusView.vue**: Full status page with detailed system information
- Displays connection status, system information, and error details
- Provides manual refresh button
- Includes server control buttons (restart/shutdown)
- Automatically refreshes on mount

### State Management

The `status.js` Pinia store manages:
- Connection status and health
- Current recoater status and error state
- WebSocket connection and message handling
- Page-aware data subscriptions

When a page is active, it updates subscriptions to receive only relevant data types, optimizing network usage.

```mermaid
classDiagram
class StatusStore {
+isConnected : boolean
+recoaterStatus : object
+lastError : string
+lastUpdate : Date
+websocket : WebSocket
+currentPage : string
+subscribedDataTypes : Set
+fetchStatus() : Promise
+connectWebSocket() : void
+disconnectWebSocket() : void
+setCurrentPage(page) : void
}
class ApiService {
+getStatus() : Promise
+getHealth() : Promise
+restartServer() : Promise
+shutdownServer() : Promise
}
StatusStore --> ApiService : "uses"
StatusStore --> WebSocket : "manages"
StatusIndicator --> StatusStore : "reads"
StatusView --> StatusStore : "reads/writes"
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L99)
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L1-L318)
- [status.js](file://frontend/src/stores/status.js#L1-L259)
- [api.js](file://frontend/src/services/api.js#L1-L586)

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L99)
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L1-L318)
- [status.js](file://frontend/src/stores/status.js#L1-L259)
- [api.js](file://frontend/src/services/api.js#L1-L586)

## Testing and Error Scenarios

The Status API includes comprehensive test coverage for various scenarios.

### Test Cases

```mermaid
flowchart TD
Start([Test Cases]) --> Success["200 OK: Full status data"]
Start --> ConnectionError["200 OK: Connection error (partial data)"]
Start --> APIError["502 Bad Gateway: API error"]
Start --> UnexpectedError["500 Internal Server Error"]
Start --> HealthCheckSuccess["200 OK: Healthy system"]
Start --> HealthCheckUnhealthy["200 OK: Unhealthy recoater"]
Start --> HealthCheckError["200 OK: Health check error"]
Success --> Assert["Assert: connected=true, full status"]
ConnectionError --> AssertPartial["Assert: connected=false, error info"]
APIError --> Assert502["Assert: status=502, detail contains error"]
UnexpectedError --> Assert500["Assert: status=500, detail contains error"]
HealthCheckSuccess --> AssertHealthy["Assert: all healthy=true"]
HealthCheckUnhealthy --> AssertRecoaterUnhealthy["Assert: recoater_healthy=false"]
HealthCheckError --> AssertBackendHealthy["Assert: backend_healthy=true, error present"]
```

### Error Conditions

The API handles the following error conditions:

**Backend Unavailability (503 Service Unavailable):**
- Occurs when the recoater server cannot be reached
- The API returns 503 with connection error details
- Frontend automatically attempts reconnection

**Degraded States:**
- When the recoater connection fails, the API returns 200 OK with partial data
- The response includes `"connected": false` and error details
- This allows clients to distinguish between backend issues and recoater connectivity issues

**Caching Behavior:**
- The API does not cache status data
- Each request polls the recoater hardware in real-time
- WebSocket service maintains real-time updates without polling from clients

**Section sources**
- [test_status_api.py](file://backend/tests/test_status_api.py#L1-L141)

## Usage Examples

### curl Commands

**Get System Status:**
```bash
curl -X GET "http://localhost:8000/api/v1/status/" -H "accept: application/json"
```

**Perform Health Check:**
```bash
curl -X GET "http://localhost:8000/api/v1/status/health" -H "accept: application/json"
```

**Restart Server:**
```bash
curl -X POST "http://localhost:8000/api/v1/status/state?action=restart" -H "accept: application/json"
```

**Shutdown Server:**
```bash
curl -X POST "http://localhost:8000/api/v1/status/state?action=shutdown" -H "accept: application/json"
```

### Example Responses

**Normal Operation (200 OK):**
```json
{
  "connected": true,
  "recoater_status": {
    "state": "ready",
    "timestamp": "2025-07-09T14:30:00Z"
  },
  "backend_status": "operational"
}
```

**Degraded State - Connection Error (200 OK):**
```json
{
  "connected": false,
  "recoater_status": null,
  "backend_status": "operational",
  "error": "Failed to connect to recoater hardware",
  "error_details": "Connection timed out"
}
```

**Backend Unavailability (503 Service Unavailable):**
```json
{
  "detail": "Connection error: Failed to connect to recoater hardware"
}
```

**Polling Recommendations:**
- For real-time updates, use WebSocket connections instead of frequent polling
- If polling is necessary, limit to once every 5-10 seconds to avoid overwhelming the system
- The WebSocket service polls every 1.0 second by default (configurable via WEBSOCKET_POLL_INTERVAL)