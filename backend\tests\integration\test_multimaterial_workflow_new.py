"""
Integration tests aligned with 3-drum, per-drum caching workflow.
- Validates drum-specific upload with real 3MSpiral_2.cli
- Validates CLI parsing and ASCII generation on sample file
- Validates drum cache status aggregation (max_layers, per-drum info)

Note: Legacy file-id based upload and per-layer send endpoints are removed.
"""

import os
from io import BytesIO
from pathlib import Path
from fastapi.testclient import TestClient

from app.main import app
from app.dependencies import initialize_recoater_client, initialize_multilayer_job_manager


class TestMultiMaterialWorkflowNew:
    def setup_method(self):
        # Ensure dev mode so MockRecoaterClient is used if needed
        os.environ["DEVELOPMENT_MODE"] = "true"
        initialize_recoater_client()
        initialize_multilayer_job_manager()
        self.client = TestClient(app)

    @staticmethod
    def _resolve_test_cli_path() -> Path:
        candidates = [
            Path("tests/3MSpiral_2.cli"),
            Path("backend/tests/3MSpiral_2.cli"),
            Path("../tests/3MSpiral_2.cli"),
            Path("../../tests/3MSpiral_2.cli"),
        ]
        for p in candidates:
            if p.exists():
                return p
        raise FileNotFoundError("3MSpiral_2.cli not found in expected locations")

    def test_drum_specific_upload_and_cache_status(self):
        # Read actual CLI file
        test_file_path = self._resolve_test_cli_path()
        with open(test_file_path, "rb") as f:
            content = f.read()

        # Upload to drum 1
        files = {"file": ("3MSpiral_2.cli", BytesIO(content), "application/octet-stream")}
        upload_resp = self.client.post("/api/v1/print/cli/upload/1", files=files)
        assert upload_resp.status_code == 200
        data = upload_resp.json()
        assert data["success"] is True
        assert data["filename"].endswith("3MSpiral_2.cli")
        assert data["total_layers"] >= 1

        # Check drum cache status
        cache_resp = self.client.get("/api/v1/print/cli/drum-cache-status")
        assert cache_resp.status_code == 200
        cache = cache_resp.json()
        assert cache["has_cached_files"] is True
        assert cache["max_layers"] == data["total_layers"]
        assert cache["drums"]["1"]["cached"] is True
        assert cache["drums"]["1"]["layer_count"] == data["total_layers"]

    def test_ascii_cli_generation_with_3mspiral_file(self):
        # Validate parser can parse and generate ASCII for a single layer and a short range
        from infrastructure.cli_editor.editor import Editor
        test_file_path = self._resolve_test_cli_path()
        with open(test_file_path, "rb") as f:
            content = f.read()
        parser = Editor()
        parsed = parser.parse(content)
        assert len(parsed.layers) >= 1

        # Single layer
        first_layer = parsed.layers[0]
        single_bytes = parser.generate_single_layer_ascii_cli(first_layer, parsed.header_lines)
        text = single_bytes.decode("ascii")
        assert "$$HEADERSTART" in text
        assert "$$ASCII" in text
        assert "$$LAYERS/000001" in text
        assert "$$GEOMETRYEND" in text

        # Small range (first 2 or 3 layers depending on file)
        n = min(3, len(parsed.layers))
        rng_bytes = parser.generate_ascii_cli_from_layer_range(parsed.layers[:n], parsed.header_lines)
        rng_text = rng_bytes.decode("ascii")
        assert "$$HEADERSTART" in rng_text
        assert "$$ASCII" in rng_text
        assert f"$$LAYERS/{n:06d}" in rng_text
        assert "$$GEOMETRYEND" in rng_text

