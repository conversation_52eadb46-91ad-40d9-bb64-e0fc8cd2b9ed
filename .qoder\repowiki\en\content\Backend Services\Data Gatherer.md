# Data Gatherer

<cite>
**Referenced Files in This Document**   
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status.py](file://backend/app/api/status.py#L1-L155)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L294)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L1-L200)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction

The DataGatherer service in APIRecoater_Ethernet is a critical component responsible for collecting, aggregating, and exposing operational telemetry from various system components. It enables real-time monitoring of recoater hardware status, including drum systems, levelers, print jobs, and axis controls. The service works in conjunction with the StatusPoller to periodically collect data and broadcast it via WebSocket to connected clients, such as the frontend HMI (Human-Machine Interface). This documentation provides a comprehensive analysis of the DataGatherer's architecture, functionality, integration points, and performance characteristics.

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

## Project Structure

The APIRecoater_Ethernet project follows a modular structure with clear separation of concerns between backend services, API endpoints, and frontend components. The DataGatherer resides within the backend services layer and interacts with multiple components across the system.

```mermaid
graph TD
subgraph "Frontend"
UI[Vue.js Components]
WSClient[WebSocket Client]
end
subgraph "Backend"
API[FastAPI Endpoints]
Services[Services]
Dependencies[Dependency Injection]
end
subgraph "Hardware Interface"
OPCUA[OPC UA Server]
Client[Recoater Client]
end
UI --> WSClient
WSClient --> API
API --> Services
Services --> Client
Services --> OPCUA
Dependencies --> Services
```

**Diagram sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

## Core Components

The DataGatherer service consists of several key components that work together to collect and distribute system telemetry:

- **RecoaterDataGatherer**: Main class responsible for collecting hardware status data
- **StatusPollingService**: Manages periodic polling and WebSocket broadcasting
- **WebSocketConnectionManager**: Handles WebSocket connections and message distribution
- **OPCUAServerManager**: Exposes coordination variables via OPC UA protocol

The service uses asynchronous programming with asyncio to efficiently handle concurrent data collection without blocking the event loop. It employs the `asyncio.to_thread` utility to wrap synchronous client calls, ensuring non-blocking execution.

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

## Architecture Overview

The DataGatherer operates within a producer-consumer architecture where it produces telemetry data that is consumed by various clients including the web frontend and external PLC systems.

```mermaid
sequenceDiagram
participant Poller as StatusPollingService
participant Gatherer as RecoaterDataGatherer
participant Client as RecoaterClient
participant WSManager as WebSocketManager
participant Frontend as Frontend HMI
Poller->>Gatherer : gather_all_data()
Gatherer->>Client : get_state()
Gatherer->>Client : get_drums()
Gatherer->>Client : get_leveler_pressure()
Gatherer->>Client : get_layer_parameters()
Client-->>Gatherer : Hardware Status
Gatherer-->>Poller : Aggregated Data
Poller->>Gatherer : construct_status_message()
Poller->>WSManager : broadcast(message)
WSManager->>Frontend : Send JSON Message
```

**Diagram sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

## Detailed Component Analysis

### DataGatherer Implementation

The RecoaterDataGatherer class implements a comprehensive data collection system that gathers telemetry from multiple hardware components.

#### Data Collection Methods

```mermaid
classDiagram
class RecoaterDataGatherer {
+gather_all_data(client, required_data_types) Dict[str, Any]
+gather_all_drum_data(client) Optional[Dict[str, Any]]
+gather_single_drum_data(client, drum_id) Dict[str, Any]
+gather_leveler_data(client) Optional[Dict[str, Any]]
+gather_print_data(client) Optional[Dict[str, Any]]
+gather_axis_data(client) Optional[Dict[str, Any]]
+construct_status_message(gathered_data) Dict[str, Any]
-_safe_api_call(method, *args) Optional[Any]
}
```

**Diagram sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)

The `gather_all_data` method serves as the primary entry point, coordinating the collection of various data types based on client subscriptions. It uses conditional task creation to only gather data that is actually needed, optimizing performance and reducing unnecessary hardware queries.

```python
async def gather_all_data(self, client: Any, required_data_types: Set[str]) -> Dict[str, Any]:
    # Always get basic status
    main_status = await asyncio.to_thread(client.get_state)

    # Conditionally gather other data types based on subscriptions
    gather_tasks = []

    if "axis" in required_data_types:
        gather_tasks.append(self.gather_axis_data(client))
    else:
        gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

    # Similar conditional gathering for drum, leveler, and print data
    axis_data, drum_data, leveler_data, print_data = await asyncio.gather(*gather_tasks)

    return {
        "status": main_status,
        "axis_data": axis_data,
        "drum_data": drum_data,
        "leveler_data": leveler_data,
        "print_data": print_data
    }
```

The `_safe_api_call` method provides a consistent error handling pattern for all hardware API interactions, catching connection and API errors and returning None instead of propagating exceptions.

#### Drum Data Collection

The service collects detailed information about each drum system, including motion, ejection, suction, and blade screw parameters:

```python
async def gather_single_drum_data(self, client: Any, drum_id: int) -> Dict[str, Any]:
    info = await asyncio.to_thread(client.get_drum, drum_id)
    
    if info is None:
        return {}
        
    return {
        "info": info,
        "motion": await self._safe_api_call(client.get_drum_motion, drum_id),
        "ejection": await self._safe_api_call(client.get_drum_ejection, drum_id),
        "suction": await self._safe_api_call(client.get_drum_suction, drum_id),
        "blade_screws": await self._safe_api_call(client.get_blade_screws_info, drum_id),
        "blade_motion": await self._safe_api_call(client.get_blade_screws_motion, drum_id),
    }
```

This hierarchical data structure enables comprehensive monitoring of each drum's operational state.

### Status Polling Service

The StatusPollingService manages the periodic collection and distribution of system status data.

```mermaid
flowchart TD
Start([Start Polling]) --> CheckRunning{"Is Running?"}
CheckRunning --> |No| Exit
CheckRunning --> |Yes| PollLoop["_polling_loop()"]
PollLoop --> HasConnections{"Has Connections?"}
HasConnections --> |No| Sleep["Wait poll_interval"]
HasConnections --> |Yes| GetRequiredTypes["Get Required Data Types"]
GetRequiredTypes --> GatherData["gather_all_data()"]
GatherData --> ConstructMessage["construct_status_message()"]
ConstructMessage --> Broadcast["broadcast()"]
Broadcast --> Sleep
Sleep --> PollLoop
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

The service implements a robust error handling strategy, with separate handling for connection errors and unexpected exceptions. It also provides a mechanism to broadcast connection errors to all connected clients, enabling the frontend to display appropriate error messages.

### WebSocket Integration

The WebSocketConnectionManager facilitates real-time data distribution to connected clients with subscription-based filtering.

```mermaid
sequenceDiagram
participant Client as Frontend
participant Manager as WebSocketManager
participant Gatherer as DataGatherer
participant Poller as StatusPoller
Client->>Manager : Connect
Manager->>Client : Accept
Client->>Manager : update_subscription(["drum", "print"])
Manager->>Poller : has_connections = True
Poller->>Gatherer : gather_all_data(required_data_types={"drum", "print"})
Gatherer-->>Poller : Aggregated Data
Poller->>Manager : broadcast(filtered_message)
Manager->>Client : Send Filtered Data
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)

The `_filter_message_for_connection` method ensures that each client only receives data it has subscribed to, reducing network bandwidth and client-side processing overhead.

### OPC UA Server Integration

The OPCUAServerManager exposes coordination variables to external PLC systems using the OPC UA industrial protocol.

```mermaid
classDiagram
class OPCUAServerManager {
+start_server() bool
+stop_server() None
+write_variable(name, value) bool
+read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
-_create_coordination_variables() None
-_get_ua_data_type(data_type) ua.VariantType
-_heartbeat_loop() None
-_cleanup() None
-_handle_server_error(error) None
}
```

**Diagram sources**
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)

The server hosts seven coordination variables that enable bidirectional communication between the backend and PLC:

- **Job Control**: job_active, total_layers, current_layer
- **Recoater Coordination**: recoater_ready_to_print, recoater_layer_complete
- **Error Handling**: backend_error, plc_error

These variables serve as shared "mailboxes" for coordination, enabling real-time communication without polling delays.

## Dependency Analysis

The DataGatherer service has well-defined dependencies on other components within the system.

```mermaid
graph TD
DataGatherer[data_gatherer.py] --> StatusPoller[status_poller.py]
StatusPoller --> WebSocketManager[websocket_manager.py]
StatusPoller --> DataGatherer
DataGatherer --> RecoaterClient[recoater_client.py]
StatusPoller --> RecoaterClient
WebSocketManager --> FastAPI[fastapi.WebSocket]
OPCUAServerManager[opcua_server.py] --> asyncua.Server
OPCUAServerManager --> opcua_config.py
DataGatherer --> logging
StatusPoller --> logging
WebSocketManager --> logging
OPCUAServerManager --> logging
```

**Diagram sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)

The service follows the dependency inversion principle, with the StatusPollingService depending on the RecoaterDataGatherer abstraction rather than the reverse. This design enables easier testing and potential replacement of the data gathering implementation.

## Performance Considerations

The DataGatherer service implements several performance optimizations:

1. **Concurrent Data Collection**: Uses `asyncio.gather()` to collect multiple data types simultaneously
2. **Conditional Gathering**: Only collects data types that are currently subscribed to
3. **Non-blocking I/O**: Wraps synchronous client calls with `asyncio.to_thread()`
4. **Subscription Filtering**: Reduces network traffic by filtering messages based on client subscriptions
5. **Error Resilience**: Individual data collection failures do not prevent other data from being gathered

The polling interval is configurable via the `WEBSOCKET_POLL_INTERVAL` environment variable (default: 1.0 seconds), allowing adjustment based on performance requirements and network conditions.

For high-frequency metrics, the service could be extended with sampling strategies such as:
- **Downsampling**: Collecting data at high frequency but broadcasting at lower frequency
- **Delta Encoding**: Only transmitting changes rather than full state
- **Aggregation**: Computing statistics (min, max, average) over time windows

Memory usage is optimized by reusing data structures and avoiding unnecessary data retention. The service does not implement long-term data storage, focusing instead on real-time telemetry distribution.

## Troubleshooting Guide

Common issues and their solutions:

**Connection Errors**
- **Symptom**: Repeated "API call failed" log messages
- **Cause**: Network issues or hardware unavailability
- **Solution**: Verify network connectivity and hardware power status

**Missing Data in Frontend**
- **Symptom**: Some data types not appearing in HMI
- **Cause**: Client not subscribed to required data types
- **Solution**: Ensure frontend calls `update_subscription` with appropriate data types

**OPC UA Connection Issues**
- **Symptom**: PLC cannot connect to OPC UA server
- **Cause**: Firewall blocking port 4843 or incorrect endpoint configuration
- **Solution**: Verify `OPCUA_SERVER_ENDPOINT` environment variable and firewall settings

**High CPU Usage**
- **Symptom**: Elevated CPU utilization
- **Cause**: Polling interval set too low
- **Solution**: Increase `WEBSOCKET_POLL_INTERVAL` value

The service includes comprehensive logging that can be used for diagnosis. Setting the logging level to DEBUG will provide detailed information about data collection operations and timing.

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L230)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L525)

## Conclusion

The DataGatherer service in APIRecoater_Ethernet provides a robust, efficient, and extensible solution for collecting and distributing operational telemetry. Its modular design separates concerns between data collection, polling management, and client communication, enabling maintainability and testability. The service effectively balances real-time performance with resource efficiency through concurrent data collection and subscription-based filtering.

Key strengths include:
- Comprehensive hardware monitoring across multiple subsystems
- Real-time WebSocket broadcasting for immediate UI updates
- Industrial protocol support via OPC UA for PLC integration
- Resilient error handling and automatic recovery
- Configurable polling frequency for performance tuning

Potential enhancement areas:
- Implement data retention and historical logging
- Add support for custom metrics from external sensors
- Integrate with external analytics platforms
- Implement more sophisticated sampling strategies for high-frequency data
- Add data validation and quality indicators

The service successfully fulfills its role as the central telemetry collection point in the APIRecoater_Ethernet system, enabling effective monitoring and control of the recoater hardware.