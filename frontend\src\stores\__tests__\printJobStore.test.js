import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { usePrintJobStore } from '../printJobStore'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    startMultiMaterialJob: vi.fn(),
    cancelMultiMaterialJob: vi.fn(),
    clearErrorFlags: vi.fn(),
    getMultiMaterialJobStatus: vi.fn(),
    getDrumStatus: vi.fn(),
    uploadCliFile: vi.fn()
  }
}))

describe('Print Job Store', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = usePrintJobStore()
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      expect(store.multiMaterialJob.isActive).toBe(false)
      expect(store.multiMaterialJob.jobId).toBe(null)
      expect(store.multiMaterialJob.currentLayer).toBe(0)
      expect(store.multiMaterialJob.totalLayers).toBe(0)
      expect(store.multiMaterialJob.status).toBe('idle')
      expect(store.errorFlags.backendError).toBe(false)
      expect(store.errorFlags.plcError).toBe(false)
      expect(store.uploadedFiles[0]).toBe(null)
      expect(store.uploadedFiles[1]).toBe(null)
      expect(store.uploadedFiles[2]).toBe(null)
    })

    it('should have correct computed properties for initial state', () => {
      expect(store.isJobActive).toBe(false)
      expect(store.hasActiveJob).toBe(false)
      expect(store.hasErrors).toBe(false)
      expect(store.hasCriticalError).toBe(false)
      expect(store.allDrumsReady).toBe(false) // All drums start as ready=false, so this should be false
      expect(store.allFilesUploaded).toBe(false)
      expect(store.canStartJob).toBe(false)
      expect(store.jobProgress).toBe(0)
    })
  })

  describe('File Upload Management', () => {
    it('should set uploaded file correctly', () => {
      const fileData = {
        fileId: 'test-file-id',
        fileName: 'test.cli',
        layerCount: 100
      }

      store.setFileUploaded(0, fileData)

      expect(store.uploadedFiles[0]).toEqual(fileData)
      expect(store.uploadedFiles[1]).toBe(null)
      expect(store.uploadedFiles[2]).toBe(null)
    })

    it('should clear uploaded files', () => {
      // Set some files first
      store.setFileUploaded(0, { fileId: 'test1', fileName: 'test1.cli', layerCount: 50 })
      store.setFileUploaded(1, { fileId: 'test2', fileName: 'test2.cli', layerCount: 75 })

      store.clearUploadedFiles()

      expect(store.uploadedFiles[0]).toBe(null)
      expect(store.uploadedFiles[1]).toBe(null)
      expect(store.uploadedFiles[2]).toBe(null)
    })

    it('should compute allFilesUploaded correctly', () => {
      expect(store.allFilesUploaded).toBe(false)

      // Upload files for all drums
      store.setFileUploaded(0, { fileId: 'test1', fileName: 'test1.cli', layerCount: 50 })
      store.setFileUploaded(1, { fileId: 'test2', fileName: 'test2.cli', layerCount: 50 })
      store.setFileUploaded(2, { fileId: 'test3', fileName: 'test3.cli', layerCount: 50 })

      expect(store.allFilesUploaded).toBe(true)
    })
  })

  describe('Error Flag Management', () => {
    it('should set error flags correctly', () => {
      store.setErrorFlags(true, false, 'Backend error occurred')

      expect(store.errorFlags.backendError).toBe(true)
      expect(store.errorFlags.plcError).toBe(false)
      expect(store.errorFlags.errorMessage).toBe('Backend error occurred')
      expect(store.errorFlags.showCriticalModal).toBe(true)
      expect(store.hasErrors).toBe(true)
      expect(store.hasCriticalError).toBe(true)
    })

    it('should clear error flags correctly', () => {
      // Set errors first
      store.setErrorFlags(true, true, 'Multiple errors')

      store.clearErrorFlags()

      expect(store.errorFlags.backendError).toBe(false)
      expect(store.errorFlags.plcError).toBe(false)
      expect(store.errorFlags.errorMessage).toBe('')
      expect(store.errorFlags.showCriticalModal).toBe(false)
      expect(store.hasErrors).toBe(false)
      expect(store.hasCriticalError).toBe(false)
    })

    it('should close critical modal without clearing error flags', () => {
      store.setErrorFlags(true, false, 'Backend error')
      expect(store.hasCriticalError).toBe(true)

      store.closeCriticalModal()

      expect(store.errorFlags.showCriticalModal).toBe(false)
      expect(store.hasCriticalError).toBe(false)
      // Error flags should still be set
      expect(store.errorFlags.backendError).toBe(true)
      expect(store.hasErrors).toBe(true)
    })
  })

  describe('Job Status Management', () => {
    it('should update job status correctly', () => {
      const statusData = {
        jobId: 'test-job-123',
        isActive: true,
        currentLayer: 5,
        totalLayers: 100,
        status: 'printing',
        progress_percentage: 5.0
      }

      store.updateJobStatus(statusData)

      expect(store.multiMaterialJob.jobId).toBe('test-job-123')
      expect(store.multiMaterialJob.isActive).toBe(true)
      expect(store.multiMaterialJob.currentLayer).toBe(5)
      expect(store.multiMaterialJob.totalLayers).toBe(100)
      expect(store.multiMaterialJob.status).toBe('printing')
      expect(store.multiMaterialJob.progressPercentage).toBe(5.0)
      expect(store.jobProgress).toBe(5)
    })

    it('should update drum status correctly', () => {
      const drumData = {
        ready: true,
        uploaded: true,
        status: 'ready',
        errorMessage: ''
      }

      store.updateDrumStatus(1, drumData)

      expect(store.multiMaterialJob.drums[1].ready).toBe(true)
      expect(store.multiMaterialJob.drums[1].uploaded).toBe(true)
      expect(store.multiMaterialJob.drums[1].status).toBe('ready')
      expect(store.multiMaterialJob.drums[1].errorMessage).toBe('')
    })

    it('should reset job state correctly', () => {
      // Set some job state first
      store.updateJobStatus({
        jobId: 'test-job',
        isActive: true,
        currentLayer: 10,
        totalLayers: 50,
        status: 'printing'
      })

      store.resetJobState()

      expect(store.multiMaterialJob.isActive).toBe(false)
      expect(store.multiMaterialJob.jobId).toBe(null)
      expect(store.multiMaterialJob.currentLayer).toBe(0)
      expect(store.multiMaterialJob.totalLayers).toBe(0)
      expect(store.multiMaterialJob.status).toBe('idle')
    })
  })

  describe('API Actions', () => {
    beforeEach(() => {
      // Set up files for all drums to enable job start
      store.setFileUploaded(0, { fileId: 'file1', fileName: 'test1.cli', layerCount: 50 })
      store.setFileUploaded(1, { fileId: 'file2', fileName: 'test2.cli', layerCount: 50 })
      store.setFileUploaded(2, { fileId: 'file3', fileName: 'test3.cli', layerCount: 50 })
    })

    it('should start multi-material job successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          job_id: 'new-job-123',
          message: 'Job started successfully'
        }
      }
      apiService.startMultiMaterialJob.mockResolvedValue(mockResponse)

      const result = await store.startMultiMaterialJob()

      expect(apiService.startMultiMaterialJob).toHaveBeenCalled()
      expect(store.multiMaterialJob.jobId).toBe('new-job-123')
      expect(store.multiMaterialJob.isActive).toBe(true)
      expect(store.multiMaterialJob.status).toBe('uploading')
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle job start failure', async () => {
      const mockError = new Error('Job start failed')
      apiService.startMultiMaterialJob.mockRejectedValue(mockError)

      await expect(store.startMultiMaterialJob()).rejects.toThrow('Job start failed')
      expect(store.isStartingJob).toBe(false)
    })

    it('should cancel multi-material job successfully', async () => {
      // Set up active job first
      store.multiMaterialJob.jobId = 'active-job'
      store.multiMaterialJob.isActive = true

      const mockResponse = {
        data: {
          success: true,
          message: 'Job cancelled successfully'
        }
      }
      apiService.cancelMultiMaterialJob.mockResolvedValue(mockResponse)

      const result = await store.cancelMultiMaterialJob()

      expect(apiService.cancelMultiMaterialJob).toHaveBeenCalled()
      expect(store.multiMaterialJob.isActive).toBe(false)
      expect(store.multiMaterialJob.jobId).toBe(null)
      expect(result).toEqual(mockResponse.data)
    })

    it('should clear error flags via API successfully', async () => {
      // Set error flags first
      store.setErrorFlags(true, true, 'Test error')

      const mockResponse = {
        data: {
          success: true,
          message: 'Error flags cleared'
        }
      }
      apiService.clearErrorFlags.mockResolvedValue(mockResponse)

      const result = await store.clearErrorFlagsAPI()

      expect(apiService.clearErrorFlags).toHaveBeenCalled()
      expect(store.errorFlags.backendError).toBe(false)
      expect(store.errorFlags.plcError).toBe(false)
      expect(store.errorFlags.showCriticalModal).toBe(false)
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch job status successfully', async () => {
      const mockResponse = {
        data: {
          jobId: 'test-job',
          isActive: true,
          currentLayer: 3,
          totalLayers: 20,
          status: 'printing'
        }
      }
      apiService.getMultiMaterialJobStatus.mockResolvedValue(mockResponse)

      const result = await store.fetchJobStatus()

      expect(apiService.getMultiMaterialJobStatus).toHaveBeenCalled()
      expect(store.multiMaterialJob.jobId).toBe('test-job')
      expect(store.multiMaterialJob.currentLayer).toBe(3)
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch drum status successfully', async () => {
      const mockResponse = {
        data: {
          drumId: 1,
          ready: true,
          uploaded: true,
          status: 'ready'
        }
      }
      apiService.getDrumStatus.mockResolvedValue(mockResponse)

      const result = await store.fetchDrumStatus(1)

      expect(apiService.getDrumStatus).toHaveBeenCalledWith(1)
      expect(store.multiMaterialJob.drums[1].ready).toBe(true)
      expect(store.multiMaterialJob.drums[1].uploaded).toBe(true)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Computed Properties', () => {
    it('should compute canStartJob correctly', () => {
      expect(store.canStartJob).toBe(false)

      // Upload all files
      store.setFileUploaded(0, { fileId: 'file1', fileName: 'test1.cli', layerCount: 50 })
      store.setFileUploaded(1, { fileId: 'file2', fileName: 'test2.cli', layerCount: 50 })
      store.setFileUploaded(2, { fileId: 'file3', fileName: 'test3.cli', layerCount: 50 })

      expect(store.canStartJob).toBe(true)

      // Set error flags
      store.setErrorFlags(true, false, 'Error')
      expect(store.canStartJob).toBe(false)

      // Clear errors but set job as active
      store.clearErrorFlags()
      store.multiMaterialJob.isActive = true
      expect(store.canStartJob).toBe(false)
    })

    it('should compute allDrumsReady correctly', () => {
      expect(store.allDrumsReady).toBe(false) // All drums start with ready=false

      // Set some drums ready
      store.updateDrumStatus(0, { ready: true })
      store.updateDrumStatus(1, { ready: true })
      expect(store.allDrumsReady).toBe(false) // Drum 2 still not ready

      store.updateDrumStatus(2, { ready: true })
      expect(store.allDrumsReady).toBe(true) // All drums ready
    })

    it('should compute jobProgress correctly', () => {
      expect(store.jobProgress).toBe(0)

      store.updateJobStatus({
        progress_percentage: 25
      })

      expect(store.jobProgress).toBe(25)

      store.updateJobStatus({
        progress_percentage: 0
      })

      expect(store.jobProgress).toBe(0)
    })
  })
})
