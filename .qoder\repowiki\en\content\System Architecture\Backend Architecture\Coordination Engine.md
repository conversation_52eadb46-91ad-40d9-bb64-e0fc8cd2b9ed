# Coordination Engine

<cite>
**Referenced Files in This Document**   
- [coordination_engine.py](file://backend/app/services/coordination_engine.py) - *Updated in recent commit*
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py) - *Updated in recent commit*
- [cli_parser.py](file://backend/services/cli_parser.py) - *Updated in recent commit*
- [empty_layer.cli](file://backend/app/templates/empty_layer.cli) - *Updated in recent commit*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect improvements in CLI parsing logic and empty layer template usage
- Added details about the integration between MultilayerJobManager and CliParserService
- Enhanced explanation of empty layer template handling for single/dual material printing
- Updated section sources to reflect modified files in recent commits

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The Coordination Engine is the central logic hub responsible for orchestrating multilayer print jobs in a multi-material 3-drum system. It coordinates between job specifications defined in `multilayer_job.py`, hardware control commands via the recoater client, and real-time status updates from both the Aerosint server and OPC UA interface. This document provides a comprehensive analysis of its architecture, state management, synchronization mechanisms, error recovery procedures, and integration with the MultilayerJobManager for full job lifecycle control.

## Project Structure
The Coordination Engine resides within the backend services layer of the application, specifically at `backend/app/services/coordination_engine.py`. It operates alongside other critical components such as the `MultilayerJobManager` and `OPCUACoordinator`, forming the core automation logic for multi-material printing workflows.

``mermaid
graph TD
subgraph "Frontend"
UI[User Interface]
JobControl[MultiLayerJobControl.vue]
end
subgraph "Backend Services"
CoordEngine[MultiMaterialCoordinationEngine]
JobManager[MultiMaterialJobManager]
RecoaterClient[RecoaterClient]
OPCUACoordinator[OPCUACoordinator]
end
subgraph "External Systems"
AerosintServer[Aerosint SPD Recoater]
PLC[PLC System]
end
UI --> JobControl --> JobManager
JobManager --> CoordEngine
CoordEngine --> RecoaterClient --> AerosintServer
CoordEngine --> OPCUACoordinator --> PLC
OPCUACoordinator --> CoordEngine
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

## Core Components
The Coordination Engine's primary responsibility is to manage the execution flow of multi-material print jobs by synchronizing layer uploads across three drums, monitoring hardware readiness, and coordinating with the PLC system through OPC UA signals. It interacts closely with the `MultiMaterialJobManager` to access job specifications and update job status, while using the `RecoaterClient` to send CLI data and retrieve real-time drum states.

Key responsibilities include:
- **Job Orchestration**: Managing the progression of layers across all active drums
- **State Management**: Tracking coordination state through a well-defined state machine
- **Hardware Synchronization**: Ensuring all drums are ready before signaling print initiation
- **Error Handling**: Detecting and recovering from hardware or communication failures
- **OPC UA Integration**: Communicating job progress and error states with the PLC

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

## Architecture Overview
The Coordination Engine implements a state-driven architecture that controls the workflow of multi-material print jobs. It operates as an asynchronous service that processes one layer at a time across all three drums, ensuring proper sequencing and synchronization.

``mermaid
graph TB
subgraph "Coordination Engine"
CE[MultiMaterialCoordinationEngine]
CL[Coordination Loop]
SM[State Machine]
end
subgraph "Supporting Services"
JM[JobManager]
RC[RecoaterClient]
OC[OPCUACoordinator]
end
User --> |Start Job| JM --> |Job State| CE
CE --> |Upload CLI| RC --> Aerosint
CE --> |Set Ready Flag| OC --> PLC
OC --> |Completion Signal| CE
CE --> |Update Progress| OC
CE --> |Error Flags| OC
CL --> SM
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

## Detailed Component Analysis

### Coordination Engine Analysis
The `MultiMaterialCoordinationEngine` class is the central component responsible for automating multi-material print jobs. It manages the entire lifecycle from job start to completion, handling layer-by-layer execution with precise coordination between hardware components.

#### State Machine Implementation
The engine uses a finite state machine to manage job execution, with clearly defined states that ensure orderly progression through the print process.

``mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> UPLOADING : start_multimaterial_job()
UPLOADING --> WAITING_FOR_READY : uploads complete
WAITING_FOR_READY --> PRINTING : all drums ready
PRINTING --> WAITING_FOR_COMPLETION : set_recoater_ready_to_print(true)
WAITING_FOR_COMPLETION --> UPLOADING : layer complete
WAITING_FOR_COMPLETION --> ERROR : timeout or error flag
UPLOADING --> ERROR : upload failure
WAITING_FOR_READY --> ERROR : timeout
ERROR --> IDLE : clear_errors()
UPLOADING --> IDLE : stop_job()
WAITING_FOR_READY --> IDLE : stop_job()
PRINTING --> IDLE : stop_job()
WAITING_FOR_COMPLETION --> IDLE : stop_job()
UPLOADING --> COMPLETE : final layer processed
COMPLETE --> IDLE : job cleanup
state CoordinationState {
IDLE
UPLOADING
WAITING_FOR_READY
PRINTING
WAITING_FOR_COMPLETION
ERROR
COMPLETE
}
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L33)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L25-L33)

#### Job Initiation Sequence
When a job is started, the coordination engine follows a strict sequence to initialize the printing process.

``mermaid
sequenceDiagram
participant JM as JobManager
participant CE as CoordinationEngine
participant OC as OPCUACoordinator
participant RC as RecoaterClient
JM->>CE : start_multimaterial_job(job_state)
CE->>CE : Validate state (must be IDLE)
CE->>CE : Set current_job, state=UPLOADING
CE->>OC : set_job_active(total_layers)
CE->>OC : update_layer_progress(current_layer)
CE->>OC : clear_error_flags()
CE->>CE : create_task(_coordination_loop)
CE-->>JM : return True
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L72-L108)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L72-L108)

#### Layer Transition Workflow
The core of the coordination engine is the layer transition workflow, which processes each layer across all drums in a synchronized manner.

``mermaid
sequenceDiagram
participant CE as CoordinationEngine
participant RC as RecoaterClient
participant OC as OPCUACoordinator
participant Aerosint as Aerosint Server
CE->>CE : _process_layer_cycle_all_drums()
CE->>CE : state=UPLOADING
loop For each drum (0,1,2)
CE->>RC : upload_cli_data(drum_id, cli_data)
RC->>Aerosint : POST /drums/{id}/geometry
Aerosint-->>RC : 200 OK
RC-->>CE : Success
CE->>CE : await sleep(2.0) between drums
end
CE->>CE : state=WAITING_FOR_READY
loop Poll every 1s for 30s
CE->>RC : get_drum_status(drum_id)
RC->>Aerosint : GET /drums/{id}
Aerosint-->>RC : state=ready
RC-->>CE : ready=true
end
CE->>OC : set_recoater_ready_to_print(true)
CE->>CE : state=WAITING_FOR_COMPLETION
loop Poll every 1s for 300s
OC->>OC : get_backend_error()
OC->>OC : get_plc_error()
alt error detected
CE->>CE : _set_error_state()
exit
end
alt timeout > 5s
CE->>CE : layer complete (simplified)
break
end
end
CE->>OC : set_recoater_layer_complete(true)
CE->>OC : set_recoater_layer_complete(false)
CE->>OC : set_recoater_ready_to_print(false)
CE->>CE : current_layer++
CE->>OC : update_layer_progress()
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L200-L245)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L200-L245)

### Multilayer Job Manager Analysis
The `MultiMaterialJobManager` works in tandem with the coordination engine to manage job lifecycle operations including creation, execution, pausing, and cancellation.

#### Job Lifecycle Management
The job manager handles the complete lifecycle of multi-material print jobs, interfacing with both the coordination engine and the frontend.

``mermaid
stateDiagram-v2
[*] --> IDLE
IDLE --> CREATED : create_job(file_ids)
CREATED --> RUNNING : start_job()
RUNNING --> RUNNING : upload_layer_to_all_drums()
RUNNING --> PAUSED : pause_job()
PAUSED --> RUNNING : resume_job()
RUNNING --> CANCELLED : cancel_job()
PAUSED --> CANCELLED : cancel_job()
RUNNING --> COMPLETED : final layer processed
CANCELLED --> IDLE : cleanup
COMPLETED --> IDLE : cleanup
ERROR --> IDLE : clear_error_flags()
class CREATED JobStatus.CREATED
class RUNNING JobStatus.RUNNING
class PAUSED JobStatus.PAUSED
class CANCELLED JobStatus.CANCELLED
class COMPLETED JobStatus.COMPLETED
class ERROR JobStatus.ERROR
```

**Diagram sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

**Section sources**
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)

### Hardware Control Analysis
The `RecoaterClient` provides the interface to the physical recoater hardware, handling all communication with the Aerosint SPD Recoater API.

#### Hardware Synchronization Mechanisms
The coordination engine ensures proper synchronization between different hardware components through carefully timed operations and status polling.

``mermaid
flowchart TD
A[Start Layer Cycle] --> B[Upload to Drum 0]
B --> C[Wait 2s]
C --> D[Upload to Drum 1]
D --> E[Wait 2s]
E --> F[Upload to Drum 2]
F --> G[Wait for All Drums Ready]
G --> H{All Ready?}
H --> |Yes| I[Signal Ready to PLC]
H --> |No| J[Timeout/Error]
I --> K[Wait for Completion Signal]
K --> L{Completed?}
L --> |Yes| M[Signal Layer Complete]
L --> |No| N[Timeout/Error]
M --> O[Advance to Next Layer]
```

**Diagram sources**
- [recoater_client.py](file://backend/services/recoater_client.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L250-L300)

**Section sources**
- [recoater_client.py](file://backend/services/recoater_client.py)
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L250-L300)

## Dependency Analysis
The Coordination Engine has well-defined dependencies on several key components, creating a layered architecture that separates concerns while enabling tight integration.

``mermaid
graph TD
CoordEngine[MultiMaterialCoordinationEngine] --> JobManager[MultiMaterialJobManager]
CoordEngine --> RecoaterClient[RecoaterClient]
CoordEngine --> OPCUACoordinator[OPCUACoordinator]
JobManager --> CliParser[CliParserService]
JobManager --> RecoaterClient
JobManager --> OPCUACoordinator
RecoaterClient --> Requests[requests library]
OPCUACoordinator --> OPCUA[OPC UA Client]
style CoordEngine fill:#f9f,stroke:#333
style JobManager fill:#bbf,stroke:#333
style RecoaterClient fill:#bfb,stroke:#333
style OPCUACoordinator fill:#fbb,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L80-L95)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L25-L40)

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L80-L95)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L25-L40)

## Performance Considerations
The Coordination Engine is designed with performance and reliability in mind, implementing several key patterns to ensure robust operation:

- **Asynchronous Processing**: All operations are non-blocking, allowing the engine to handle multiple tasks concurrently
- **Retry Logic**: The `RecoaterClient` implements retry mechanisms for transient network failures
- **Rate Limiting**: 2-second delays between drum uploads prevent server overload
- **Timeout Configuration**: Configurable timeouts for readiness (30s) and completion (300s) prevent indefinite blocking
- **Error Tracking**: The engine tracks error counts and supports a configurable maximum retry count

The coordination loop processes layers sequentially but handles all operations asynchronously, ensuring that the system remains responsive even during long print jobs.

## Troubleshooting Guide
Common issues and their resolution strategies:

**Section sources**
- [coordination_engine.py](file://backend/app/services/coordination_engine.py#L433-L472)
- [multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py#L380-L400)

### Job Start Failures
- **Symptom**: `CoordinationError: Cannot start job: engine in state CoordinationState.UPLOADING`
- **Cause**: Attempting to start a new job while another is already running
- **Resolution**: Stop the current job first using `stop_job()` before starting a new one

### Drum Readiness Timeouts
- **Symptom**: "Timeout waiting for drums to be ready (30s)"
- **Cause**: One or more drums failed to reach ready state within the timeout period
- **Resolution**: 
  1. Check physical drum status
  2. Verify network connectivity to Aerosint server
  3. Inspect drum-specific error messages
  4. Consider increasing `ready_timeout` if hardware response is consistently slow

### Layer Completion Timeouts
- **Symptom**: "Timeout waiting for layer completion (300s)"
- **Cause**: PLC failed to signal completion or error flags were raised
- **Resolution**:
  1. Check PLC status and error logs
  2. Verify OPC UA connection
  3. Inspect backend and PLC error flags
  4. Ensure proper mechanical operation of recoater system

### Error Recovery
The system provides error recovery through:
- `clear_errors()`: Resets the coordination engine from ERROR state to IDLE
- `cancel_job()`: Terminates the current job and resets state
- Automatic error flag clearing in OPC UA when errors are resolved

## Conclusion
The Coordination Engine serves as the central orchestrator for multi-material 3-drum printing systems, providing robust job management, hardware synchronization, and error handling capabilities. Its state-driven architecture ensures reliable operation through well-defined state transitions, while its integration with the MultilayerJobManager and OPCUACoordinator enables seamless coordination between software and hardware components.

Key strengths include:
- Clear separation of concerns between job management and coordination logic
- Comprehensive error handling and recovery mechanisms
- Asynchronous design for responsive operation
- Tight integration with both hardware (via RecoaterClient) and control systems (via OPC UA)
- Support for single, dual, and triple material printing configurations

The engine effectively addresses the challenges of multi-material printing by ensuring synchronized layer progression across all drums while maintaining communication with the PLC system for coordinated print operations.