# Frontend Architecture

<cite>
**Referenced Files in This Document**   
- [main.js](file://frontend/src/main.js)
- [App.vue](file://frontend/src/App.vue)
- [router/index.js](file://frontend/src/router/index.js)
- [stores/status.js](file://frontend/src/stores/status.js)
- [stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [services/api.js](file://frontend/src/services/api.js)
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)
- [views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [components/StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)
- [components/MultiLayerJobControl.vue](file://frontend/src/components/MultiLayerJobControl.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Application Bootstrap Process](#application-bootstrap-process)
4. [Component Hierarchy and Navigation](#component-hierarchy-and-navigation)
5. [State Management with Pinia](#state-management-with-pinia)
6. [API Service Layer](#api-service-layer)
7. [Real-Time Updates via WebSocket](#real-time-updates-via-websocket)
8. [Data Flow and Component Interaction](#data-flow-and-component-interaction)
9. [Error Handling and UI Consistency](#error-handling-and-ui-consistency)
10. [Conclusion](#conclusion)

## Introduction
This document provides a comprehensive architectural overview of the Vue.js frontend for the APIRecoater_Ethernet application. The frontend serves as a Human-Machine Interface (HMI) for controlling and monitoring a recoater system used in additive manufacturing. It features a modular structure with clear separation of concerns, leveraging Vue 3, Pinia for state management, Vue Router for navigation, and a service layer for API communication. The architecture supports real-time status updates through WebSocket connections and maintains consistent UI state across multiple control panels.

## Project Structure
The frontend directory follows a standard Vue.js project layout with feature-based organization. Key directories include:
- **components**: Reusable UI components such as controls and indicators
- **views**: Page-level components corresponding to different application views
- **stores**: Pinia stores for global state management
- **services**: API abstraction layer for backend communication
- **router**: Navigation configuration

```mermaid
graph TB
subgraph "Frontend"
A[main.js] --> B[App.vue]
B --> C[Router]
C --> D[StatusView]
C --> E[RecoaterView]
C --> F[PrintView]
C --> G[ConfigurationView]
B --> H[StatusIndicator]
D --> I[StatusStore]
E --> J[DrumControl]
E --> K[HopperControl]
F --> L[FileUploadColumn]
F --> M[JobProgressDisplay]
G --> N[ConfigurationForm]
I --> O[WebSocket]
P[api.js] --> Q[REST API]
P --> O
end
```

**Diagram sources**
- [main.js](file://frontend/src/main.js)
- [App.vue](file://frontend/src/App.vue)
- [router/index.js](file://frontend/src/router/index.js)

**Section sources**
- [main.js](file://frontend/src/main.js)
- [App.vue](file://frontend/src/App.vue)
- [router/index.js](file://frontend/src/router/index.js)

## Application Bootstrap Process
The application initialization begins in `main.js`, where the Vue application instance is created and configured with essential plugins.

```javascript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './style.css'

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.mount('#app')
```

This bootstrap process follows a standard Vue 3 pattern:
1. Create the Vue application instance using `createApp(App)`
2. Register Pinia for state management
3. Register Vue Router for navigation
4. Mount the application to the DOM element with ID `app`

The `App.vue` component serves as the root component, providing the overall layout including the header with status indicator and the main navigation sidebar.

**Section sources**
- [main.js](file://frontend/src/main.js#L1-L26)
- [App.vue](file://frontend/src/App.vue#L1-L137)

## Component Hierarchy and Navigation
The application implements a hierarchical component structure with `App.vue` as the root, managing navigation between different views through Vue Router.

### Navigation Structure
The navigation menu in `App.vue` provides access to four main views:
- **Status**: System status monitoring
- **Recoater**: Hardware component control
- **Print**: Print job management and layer configuration
- **Configuration**: System configuration settings

```mermaid
graph TD
A[App.vue] --> B[StatusView]
A --> C[RecoaterView]
A --> D[PrintView]
A --> E[ConfigurationView]
B --> F[StatusIndicator]
C --> G[DrumControl]
C --> H[HopperControl]
C --> I[LevelerControl]
D --> J[FileUploadColumn]
D --> K[JobProgressDisplay]
D --> L[ErrorDisplayPanel]
E --> M[ConfigurationForm]
```

### View Components
Each view component is responsible for a specific functional area:

**PrintView** manages print job operations, layer parameter configuration, and file management. It integrates with the print job store to display current job status and progress.

**ConfigurationView** provides a form-based interface for modifying system configuration parameters such as build space dimensions, resolution, and drum gap settings.

**RecoaterView** serves as a dashboard for monitoring and controlling all recoater hardware components, including drums, hoppers, and levelers.

**Section sources**
- [App.vue](file://frontend/src/App.vue#L1-L137)
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)
- [views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
- [router/index.js](file://frontend/src/router/index.js)

## State Management with Pinia
The application uses Pinia for centralized state management, with two primary stores: `status.js` and `printJobStore.js`.

### Status Store
The `statusStore` maintains real-time information about the connection state and hardware status:

```javascript
import { defineStore } from 'pinia'

export const useStatusStore = defineStore('status', {
  state: () => ({
    isConnected: false,
    lastError: null,
    drumData: {},
    levelerData: {},
    printData: null
  }),
  actions: {
    connectWebSocket() { /* WebSocket connection logic */ },
    disconnectWebSocket() { /* Cleanup connection */ },
    updateStatus(data) { this.$patch(data) }
  }
})
```

Key state properties:
- **isConnected**: Boolean indicating WebSocket connection status
- **lastError**: Stores the most recent error message
- **drumData**: Object containing status information for each drum
- **levelerData**: Leveler pressure and sensor data
- **printData**: Current print job status information

### Print Job Store
The `printJobStore` manages state related to print jobs and file operations:

```javascript
export const usePrintJobStore = defineStore('printJob', {
  state: () => ({
    isJobActive: false,
    lastUploadedFiles: {},
    jobProgress: 0
  }),
  actions: {
    setJobActive(active) { this.isJobActive = active },
    setLastUploadedFile(drumId, fileName, source) {
      this.lastUploadedFiles[drumId] = { name: fileName, source, timestamp: Date.now() }
    }
  }
})
```

The stores provide a reactive data layer that components can access through composition API functions like `useStatusStore()` and `usePrintJobStore()`.

**Section sources**
- [stores/status.js](file://frontend/src/stores/status.js)
- [stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)

## API Service Layer
The `api.js` service module abstracts communication with the backend through both REST API and WebSocket protocols.

### REST API Endpoints
The service provides methods for various backend interactions:

```javascript
export const apiService = {
  // Configuration endpoints
  getConfiguration: () => axios.get('/api/configuration'),
  setConfiguration: (config) => axios.post('/api/configuration', config),
  
  // Print-related endpoints
  getLayerParameters: () => axios.get('/api/print/layer-parameters'),
  setLayerParameters: (params) => axios.post('/api/print/layer-parameters', params),
  getLayerPreview: () => axios.get('/api/print/layer-preview', { responseType: 'blob' }),
  
  // File management
  uploadDrumGeometry: (drumId, file) => {
    const formData = new FormData()
    formData.append('file', file)
    return axios.post(`/api/recoater/drum/${drumId}/geometry`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  // CLI file operations
  uploadCliFile: (file) => {
    const formData = new FormData()
    formData.append('cli_file', file)
    return axios.post('/api/print/cli', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}
```

### Error Handling
The service layer implements consistent error handling with appropriate response parsing:

```javascript
// Axios interceptor for error handling
axios.interceptors.response.use(
  response => response,
  error => {
    console.error('API request failed:', error)
    return Promise.reject(error)
  }
)
```

Components consume these services directly, with error messages propagated to the UI through reactive state variables.

**Section sources**
- [services/api.js](file://frontend/src/services/api.js)
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)

## Real-Time Updates via WebSocket
The application maintains a persistent WebSocket connection for real-time status updates from the backend.

### Connection Management
The `statusStore` manages the WebSocket lifecycle:

```javascript
actions: {
  connectWebSocket() {
    if (this.socket) this.socket.close()
    
    this.socket = new WebSocket(`ws://${window.location.host}/ws/status`)
    
    this.socket.onopen = () => {
      this.isConnected = true
      this.lastError = null
    }
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.updateStatus(data)
    }
    
    this.socket.onclose = () => {
      this.isConnected = false
      this.lastError = 'Connection closed'
    }
    
    this.socket.onerror = (error) => {
      this.lastError = 'WebSocket error occurred'
    }
  },
  
  disconnectWebSocket() {
    if (this.socket) {
      this.socket.close()
      this.socket = null
    }
  }
}
```

### Reactive UI Updates
Components react to state changes automatically through Vue's reactivity system. For example, the `StatusIndicator` component updates its appearance based on `statusStore.isConnected`:

```vue
<template>
  <div class="status-indicator">
    <div :class="[
      'status-dot',
      statusStore.isConnected ? 'status-connected' : 'status-disconnected'
    ]"></div>
    <span class="status-text">
      {{ statusStore.isConnected ? 'Connected' : 'Disconnected' }}
    </span>
  </div>
</template>
```

The WebSocket connection is established when the application mounts and disconnected when the component is destroyed, ensuring proper resource management.

**Section sources**
- [stores/status.js](file://frontend/src/stores/status.js)
- [App.vue](file://frontend/src/App.vue)
- [components/StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)
- [views/RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)

## Data Flow and Component Interaction
The application follows a unidirectional data flow pattern, with state changes initiated by user actions and propagated through the store to update the UI.

### Component-to-Store Communication
Components dispatch actions to update state, which then triggers reactive UI updates. For example, in `PrintView.vue`:

```javascript
const saveParameters = async () => {
  if (!statusStore.isConnected || !isParametersValid.value) return
  
  isLoading.value = true
  try {
    await apiService.setLayerParameters(layerParams.value)
    showMessage('Layer parameters saved successfully')
  } catch (error) {
    showMessage('Failed to save layer parameters', true)
  } finally {
    isLoading.value = false
  }
}
```

### Data Flow Diagram
```mermaid
sequenceDiagram
participant User as "User"
participant UI as "UI Component"
participant Store as "Pinia Store"
participant Service as "API Service"
participant Backend as "Backend API"
User->>UI : Click "Save Parameters"
UI->>Service : Call apiService.setLayerParameters()
Service->>Backend : POST /api/print/layer-parameters
Backend-->>Service : 200 OK
Service-->>UI : Resolve Promise
UI->>UI : Show success message
Service->>Backend : WebSocket update
Backend->>Store : Send status update
Store-->>UI : Reactive state change
UI->>User : Update UI display
```

**Diagram sources**
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [services/api.js](file://frontend/src/services/api.js)
- [stores/status.js](file://frontend/src/stores/status.js)

**Section sources**
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [services/api.js](file://frontend/src/services/api.js)
- [stores/status.js](file://frontend/src/stores/status.js)

## Error Handling and UI Consistency
The application implements comprehensive error handling to ensure UI consistency and provide meaningful feedback to users.

### Error States
Components display appropriate messages based on connection status and operation results:

```vue
<div v-if="!statusStore.isConnected" class="disabled-overlay">
  <p class="disabled-text">Connect to recoater to configure layer parameters</p>
</div>
```

### Message System
A centralized message system provides temporary success and error notifications:

```javascript
const showMessage = (message, isError = false) => {
  if (isError) {
    errorMessage.value = message
    successMessage.value = ''
  } else {
    successMessage.value = message
    errorMessage.value = ''
  }
  
  setTimeout(() => {
    successMessage.value = ''
    errorMessage.value = ''
  }, 5000)
}
```

### Loading States
Operations that involve API calls show loading indicators to provide feedback:

```vue
<button :disabled="isLoading" class="btn btn-primary">
  <span v-if="isLoading">Saving...</span>
  <span v-else>Save Parameters</span>
</button>
```

The UI maintains consistency by disabling controls when operations are in progress or when the system is disconnected, preventing invalid operations.

**Section sources**
- [views/PrintView.vue](file://frontend/src/views/PrintView.vue)
- [views/ConfigurationView.vue](file://frontend/src/views/ConfigurationView.vue)
- [components/CriticalErrorModal.vue](file://frontend/src/components/CriticalErrorModal.vue)

## Conclusion
The Vue.js frontend for APIRecoater_Ethernet demonstrates a well-structured architecture with clear separation of concerns. The application bootstrap process in `main.js` initializes the Vue app with Pinia and Vue Router, while `App.vue` provides the root layout and navigation. The component hierarchy follows a logical structure with specialized views for different functional areas. Pinia stores provide centralized state management for both real-time status data and application state. The `api.js` service layer abstracts REST and WebSocket communication with the backend, enabling consistent error handling and data retrieval. Real-time updates are achieved through WebSocket subscriptions that keep the UI synchronized with the system state. The architecture supports responsive design across different control panels and maintains UI consistency through proper state management and error handling.