# OPC UA Client Coordinator

<cite>
**Referenced Files in This Document**   
- [coordination_engine.py](file://backend\app\services\coordination_engine.py) - *Updated in recent commit*
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py) - *Updated in recent commit*
- [opcua_server.py](file://backend\app\services\opcua_server.py) - *Updated in recent commit*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Updated in recent commit*
- [client.py](file://backend\infrastructure\recoater_client\client.py) - *Refactored into modular package*
- [blade_controls.py](file://backend\infrastructure\recoater_client\blade_controls.py) - *New modular component*
- [mock_recoater_client.py](file://backend\infrastructure\mock_recoater_client.py) - *Updated path*
- [dependencies.py](file://backend\app\dependencies.py) - *Updated in recent commit*
</cite>

## Update Summary
**Changes Made**   
- Updated file paths and structure to reflect the refactoring of recoater_client.py into a modular package under infrastructure/recoater_client
- Added documentation for the new mixin-based architecture of the RecoaterClient
- Updated class diagrams to reflect the modular design with BladeControlMixin, LevelerControlMixin, and other mixins
- Corrected outdated references to the old services/recoater_client.py location
- Added details about the asynchronous wrapper methods in AsyncClientMixin
- Enhanced error handling documentation with retry logic details from environment variables

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Command Dispatch Workflow](#command-dispatch-workflow)
7. [Connection Parameters and Configuration](#connection-parameters-and-configuration)
8. [Error Handling Strategies](#error-handling-strategies)
9. [OPC UA Variable Operations](#opc-ua-variable-operations)
10. [Conclusion](#conclusion)

## Introduction
The OPC UA Client Coordinator system provides a robust framework for managing communication between a backend application and physical recoater hardware in an additive manufacturing environment. This document details the architecture and functionality of the coordination layer, focusing on how the system manages OPC UA connections, dispatches commands, handles errors, and enables development through mocking capabilities. The system is designed to coordinate multi-material print jobs across multiple drums while maintaining reliable communication with both hardware devices and PLC systems.

## Project Structure
The project follows a layered architecture with clear separation between services, configuration, and business logic. The backend contains the core coordination logic, while the frontend provides a user interface for monitoring and control. The services layer implements the OPC UA coordination, hardware communication, and job management functionality.

``mermaid
graph TD
subgraph "Backend"
subgraph "Services"
OPCUACoordinator[OPCUACoordinator]
OPCUAServer[OPCUAServerManager]
CoordinationEngine[CoordinationEngine]
end
subgraph "Configuration"
OPCUAConfig[OPCUAConfig]
end
subgraph "API"
FastAPI[FastAPI Endpoints]
end
end
subgraph "Frontend"
UI[User Interface]
Websocket[WebSocket Manager]
end
FastAPI --> CoordinationEngine
CoordinationEngine --> OPCUACoordinator
OPCUACoordinator --> OPCUAServer
Websocket --> FastAPI
style OPCUACoordinator fill:#f9f,stroke:#333
style OPCUAServer fill:#f9f,stroke:#333
```

**Diagram sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)

**Section sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)

## Core Components
The system consists of several core components that work together to provide coordination functionality. The OPCUACoordinator serves as the primary interface for OPC UA communication, while the OPCUAServerManager handles the low-level server operations. The RecoaterClient provides an abstraction layer for hardware communication, and the CoordinationEngine manages the overall job coordination process. Configuration is centralized in the OPCUAConfig module, which defines connection parameters and coordination variables.

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

## Architecture Overview
The system architecture follows a layered approach with clear separation of concerns. The backend application hosts an OPC UA server that exposes coordination variables to the PLC, while acting as an OPC UA client to communicate with the physical recoater device. This dual role enables bidirectional communication between the backend, PLC, and hardware.

``mermaid
graph TB
subgraph "Backend Application"
subgraph "API Layer"
REST[REST API]
end
subgraph "Coordination Layer"
Coordinator[OPCUACoordinator]
Server[OPCUAServerManager]
end
subgraph "Hardware Interface"
Client[RecoaterClient]
end
end
subgraph "External Systems"
PLC[TwinCAT PLC]
Hardware[Physical Recoater]
end
REST --> Coordinator
Coordinator --> Server
Coordinator --> Client
Server --> PLC
Client --> Hardware
style Coordinator fill:#e6f3ff,stroke:#333
style Server fill:#e6f3ff,stroke:#333
style Client fill:#e6f3ff,stroke:#333
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py)
- [opcua_server.py](file://backend\app\services\opcua_server.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)

## Detailed Component Analysis

### OPC UA Coordinator Analysis
The OPCUACoordinator class provides a high-level interface for OPC UA communication and coordination with the PLC. It abstracts the complexity of direct OPC UA operations and provides convenience methods for common coordination tasks.

#### Class Diagram
``mermaid
classDiagram
class OPCUACoordinator {
+config : OPCUAServerConfig
+server_manager : OPCUAServerManager
+_connected : bool
+_monitoring_task : Optional[Task]
+_event_handlers : Dict[str, List]
+__init__(config)
+connect() bool
+disconnect() bool
+is_connected() bool
+get_server_status() Dict
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+_monitoring_loop() None
+_trigger_event_handlers(variable_name, value) None
}
class OPCUAServerManager {
+config : OPCUAServerConfig
+server : Optional[Server]
+namespace_idx : int
+variable_nodes : Dict[str, Any]
+_running : bool
+_restart_count : int
+_heartbeat_task : Optional[Task]
+__init__(config)
+start_server() bool
+stop_server() None
+is_running() bool
+write_variable(name, value) bool
+read_variable(name) Any
+get_variable_names() List[str]
+_create_coordination_variables() None
+_get_ua_data_type(data_type) ua.VariantType
+_heartbeat_loop() None
+_cleanup() None
+_handle_server_error(error) None
}
OPCUACoordinator --> OPCUAServerManager : "uses"
```

**Diagram sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)

### Recoater Client Analysis
The RecoaterClient class provides an abstraction layer for communicating with the physical recoater device. It implements methods for all hardware operations and includes robust error handling and retry logic. The client has been refactored into a modular package under infrastructure/recoater_client, using a mixin-based architecture to organize functionality.

#### Class Diagram
``mermaid
classDiagram
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
+__init__(base_url, timeout)
+_make_request(method, endpoint, return_raw, **kwargs) Any
+get_state() Dict
+set_state(action) Dict
+get_config() Dict
+set_config(config) Dict
+get_drums() Dict
+get_drum(drum_id) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class RecoaterConnectionError {
+__init__(message)
}
class RecoaterAPIError {
+__init__(message)
}
RecoaterClient --> RecoaterConnectionError : "throws"
RecoaterClient --> RecoaterAPIError : "throws"
```

**Diagram sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L335)

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L335)
- [blade_controls.py](file://backend\infrastructure\recoater_client\blade_controls.py#L1-L127)
- [print_controls.py](file://backend\infrastructure\recoater_client\print_controls.py#L1-L88)
- [file_management.py](file://backend\infrastructure\recoater_client\file_management.py#L1-L54)
- [async_client.py](file://backend\infrastructure\recoater_client\async_client.py#L1-L164)

### Mock Recoater Client Analysis
The MockRecoaterClient enables development and testing without requiring access to physical hardware. It simulates the behavior of the real recoater client and provides predictable responses for testing purposes.

``mermaid
classDiagram
class MockRecoaterClient {
+base_url : str
+__init__(base_url)
+get_state() Dict
+set_state(action) Dict
+get_config() Dict
+set_config(config) Dict
+get_drums() Dict
+get_drum(drum_id) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
}
MockRecoaterClient --|> RecoaterClient : "inherits"
```

**Section sources**
- [mock_recoater_client.py](file://backend\infrastructure\mock_recoater_client.py)

## Command Dispatch Workflow
The command dispatch workflow follows a clear path from API request to hardware execution. This sequence diagram illustrates the flow of commands through the system.

``mermaid
sequenceDiagram
participant API as "API Request"
participant Engine as "CoordinationEngine"
participant Coordinator as "OPCUACoordinator"
participant Client as "RecoaterClient"
participant Hardware as "Physical Device"
API->>Engine : start_multimaterial_job()
Engine->>Coordinator : set_job_active()
Coordinator->>Coordinator : write_variable("job_active", True)
Coordinator->>Coordinator : write_variable("total_layers", 150)
Coordinator->>Coordinator : write_variable("current_layer", 0)
Coordinator->>Coordinator : clear_error_flags()
Engine->>Client : upload_cli_data()
Client->>Client : _make_request()
Client->>Hardware : HTTP PUT /drums/{id}/geometry
Hardware-->>Client : 200 OK
Client-->>Engine : Success Response
Engine->>Client : get_drum_status()
Client->>Hardware : HTTP GET /drums/{id}
Hardware-->>Client : Status Response
Client-->>Engine : Drum Status
Engine->>Coordinator : update_layer_progress()
Coordinator->>Coordinator : write_variable("current_layer", 1)
Note over Engine,Hardware : Command execution continues for each layer
```

**Diagram sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L335)

**Section sources**
- [coordination_engine.py](file://backend\app\services\coordination_engine.py#L1-L474)

## Connection Parameters and Configuration
The system's connection parameters and configuration are centralized in the opcua_config.py file, which defines the settings for the OPC UA server and coordination variables.

### Configuration Structure
``mermaid
erDiagram
OPCUAServerConfig {
string endpoint PK
string server_name
string namespace_uri
int namespace_idx
string security_policy
string security_mode
string certificate_path
string private_key_path
float connection_timeout
float session_timeout
boolean auto_restart
float restart_delay
int max_restart_attempts
}
CoordinationVariable {
string name PK
string node_id
string data_type
any initial_value
boolean writable
string description
}
OPCUAServerConfig ||--o{ CoordinationVariable : "contains"
```

### Configuration Parameters
**OPC UA Server Configuration**
- **endpoint**: "opc.tcp://0.0.0.0:4843/recoater/server/"
- **server_name**: "Recoater Multi-Material Coordination Server"
- **namespace_uri**: "http://recoater.backend.server"
- **namespace_idx**: 2
- **security_policy**: "None"
- **security_mode**: "None"
- **connection_timeout**: 5.0 seconds
- **session_timeout**: 60.0 seconds
- **auto_restart**: true
- **restart_delay**: 5.0 seconds
- **max_restart_attempts**: 3

**Coordination Variables**
- **job_active**: Boolean, initial_value=False, writable=True
- **total_layers**: Int32, initial_value=0, writable=True
- **current_layer**: Int32, initial_value=0, writable=True
- **recoater_ready_to_print**: Boolean, initial_value=False, writable=True
- **recoater_layer_complete**: Boolean, initial_value=False, writable=True
- **backend_error**: Boolean, initial_value=False, writable=True
- **plc_error**: Boolean, initial_value=False, writable=True

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L294)

## Error Handling Strategies
The system implements comprehensive error handling strategies at multiple levels to ensure reliability and fault tolerance.

### Error Handling Flowchart
``mermaid
flowchart TD
Start([Request]) --> ValidateInput["Validate Input Parameters"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Error Response"]
InputValid --> |Yes| ExecuteRequest["Execute Request"]
ExecuteRequest --> Success{"Success?"}
Success --> |Yes| ReturnSuccess["Return Success Response"]
Success --> |No| CheckErrorType{"Error Type?"}
CheckErrorType --> |Connection| HandleConnectionError["Handle Connection Error"]
CheckErrorType --> |API| HandleAPIError["Handle API Error"]
CheckErrorType --> |Coordination| HandleCoordinationError["Handle Coordination Error"]
HandleConnectionError --> Retry{"Retry?"}
Retry --> |Yes| Delay["Wait retry_delay"]
Delay --> ExecuteRequest
Retry --> |No| LogError["Log Error"]
LogError --> UpdateStatus["Update Error Status"]
UpdateStatus --> ReturnError
HandleAPIError --> LogError
HandleCoordinationError --> LogError
style Start fill:#f9f,stroke:#333
style ReturnSuccess fill:#cfc,stroke:#333
style ReturnError fill:#fcc,stroke:#333
```

### Error Types and Handling
**RecoaterConnectionError**
- Raised when connection to recoater hardware fails
- Handled with retry logic (2 attempts, 500ms delay) configurable via RECOATER_MAX_ATTEMPTS and RECOATER_RETRY_DELAY environment variables
- Logged with warning level for transient failures
- Logged with error level for final failures

**RecoaterAPIError**
- Raised when recoater API returns an error response
- Includes status code and response text
- No retry logic (indicates client or server error)
- Triggers error state in coordination engine

**CoordinationError**
- Raised when coordination operations fail
- Wraps lower-level exceptions
- Updates coordination engine state to ERROR
- Sets backend_error flag in OPC UA server

**OPC UA Server Errors**
- Handled with auto-restart capability
- Configurable restart attempts (default: 3)
- Configurable restart delay (default: 5.0 seconds)
- Heartbeat monitoring for server health

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L335)
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)

## OPC UA Variable Operations
The system provides methods for reading and writing OPC UA variables, which serve as the communication channel between the backend and PLC.

### Variable Operations Sequence
``mermaid
sequenceDiagram
participant App as "Application"
participant Coordinator as "OPCUACoordinator"
participant Server as "OPCUAServerManager"
participant PLC as "TwinCAT PLC"
App->>Coordinator : write_variable("job_active", True)
Coordinator->>Server : write_variable("job_active", True)
Server->>Server : node.write_value(True)
Server-->>Coordinator : Success
Coordinator-->>App : Success
PLC->>Server : Read job_active variable
Server-->>PLC : Returns True
PLC->>Server : Write plc_error = True
Server->>Server : node.write_value(True)
Server->>Coordinator : Variable changed event
Coordinator->>Coordinator : _trigger_event_handlers()
Coordinator->>App : Event handlers notified
```

### Example Variable Operations
**Writing Variables**
```python
# Set job as active with 150 layers
await coordinator.set_job_active(150)

# Update current layer progress
await coordinator.update_layer_progress(5)

# Signal that recoater is ready to print
await coordinator.set_recoater_ready_to_print(True)

# Set backend error flag
await coordinator.set_backend_error(True)
```

**Reading Variables**
```python
# Read current layer number
current_layer = await coordinator.read_variable("current_layer")

# Check if job is active
job_active = await coordinator.read_variable("job_active")

# Check for PLC errors
plc_error = await coordinator.read_variable("plc_error")
```

**Subscribing to Variable Changes**
```python
# Define event handler
async def on_error_change(variable_name, value):
    if variable_name == "backend_error" and value:
        print("Backend error detected!")
    elif variable_name == "plc_error" and value:
        print("PLC error detected!")

# Subscribe to error variables
await coordinator.subscribe_to_changes(
    ["backend_error", "plc_error"], 
    on_error_change
)
```

**Section sources**
- [opcua_coordinator.py](file://backend\app\services\opcua_coordinator.py#L1-L590)
- [opcua_server.py](file://backend\app\services\opcua_server.py#L1-L524)

## Conclusion
The OPC UA Client Coordinator system provides a robust and flexible framework for managing communication between a backend application and physical recoater hardware. By implementing a layered architecture with clear separation of concerns, the system enables reliable coordination of multi-material print jobs while maintaining compatibility with PLC systems through OPC UA. The use of configuration-driven parameters, comprehensive error handling, and mocking capabilities for development makes this system well-suited for industrial additive manufacturing applications. The design allows for easy extension and maintenance, ensuring long-term viability in production environments.