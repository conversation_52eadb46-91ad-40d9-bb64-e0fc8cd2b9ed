# OPC UA Integration Architecture

<cite>
**Referenced Files in This Document**   
- [opcua_server.py](file://backend/app/services/opcua_server.py) - *Updated in recent commit*
- [opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py) - *Updated in recent commit*
- [opcua_config.py](file://backend/app/config/opcua_config.py) - *Configuration schema for OPC UA integration*
- [client.py](file://backend/infrastructure/recoater_client/client.py) - *Refactored into package structure*
- [__init__.py](file://backend/infrastructure/recoater_client/__init__.py) - *Package initialization after refactoring*
- [mock_recoater_client.py](file://backend/infrastructure/mock_recoater_client.py) - *Development mock client*
- [opcua_simulator.py](file://opcua_simulator.py) - *Testing utility for OPC UA interactions*
</cite>

## Update Summary
**Changes Made**   
- Updated file paths and package structure for recoater_client after refactoring
- Corrected the location of recoater_client from services to infrastructure directory
- Updated class diagram to reflect modular package design with mixins
- Added details about package organization and import structure
- Removed outdated references to non-existent file paths
- Enhanced documentation of client architecture with mixin-based design

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Data Mapping and Type Conversion](#data-mapping-and-type-conversion)
7. [Connection Lifecycle and Fault Recovery](#connection-lifecycle-and-fault-recovery)
8. [Network Security and Session Management](#network-security-and-session-management)
9. [Testing and Development Utilities](#testing-and-development-utilities)
10. [Conclusion](#conclusion)

## Introduction

The OPC UA integration layer in APIRecoater_Ethernet implements a dual-role architecture where the backend serves as both an OPC UA server and client. This design enables bidirectional communication between the system's internal components and external industrial devices. As an OPC UA server, it exposes its internal state to SCADA systems and PLCs via `opcua_server.py`. Simultaneously, it acts as an OPC UA client through `opcua_coordinator.py` to connect to and control the recoater device. This document details the architecture, components, data flows, and operational patterns of this integration layer, providing comprehensive insight into its functionality and design principles.

## Project Structure

The project follows a modular structure with clear separation of concerns. The backend contains the core OPC UA integration components within the `app/services` directory, while client abstractions reside in the `infrastructure` directory. Configuration is centralized in `app/config`, and testing utilities are available in both backend and root-level test directories.

``mermaid
graph TD
subgraph "Backend"
subgraph "Services"
OPCUAServer[opcua_server.py]
OPCUACoordinator[opcua_coordinator.py]
end
subgraph "Configuration"
OPCUAConfig[opcua_config.py]
end
end
subgraph "Infrastructure"
subgraph "Recoater Client"
Client[client.py]
BladeControls[blade_controls.py]
LevelerControls[leveler_controls.py]
PrintControls[print_controls.py]
FileManagement[file_management.py]
AsyncClient[async_client.py]
end
end
subgraph "Utilities"
OPCUASimulator[opcua_simulator.py]
end
OPCUAServer --> OPCUAConfig
OPCUACoordinator --> OPCUAServer
OPCUACoordinator --> OPCUAConfig
OPCUASimulator -.-> OPCUAConfig
Client --> BladeControls
Client --> LevelerControls
Client --> PrintControls
Client --> FileManagement
Client --> AsyncClient
```

**Diagram sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/infrastructure/recoater_client/client.py](file://backend/infrastructure/recoater_client/client.py)
- [backend/infrastructure/recoater_client/blade_controls.py](file://backend/infrastructure/recoater_client/blade_controls.py)
- [backend/infrastructure/recoater_client/leveler_controls.py](file://backend/infrastructure/recoater_client/leveler_controls.py)
- [backend/infrastructure/recoater_client/print_controls.py](file://backend/infrastructure/recoater_client/print_controls.py)
- [backend/infrastructure/recoater_client/file_management.py](file://backend/infrastructure/recoater_client/file_management.py)
- [backend/infrastructure/recoater_client/async_client.py](file://backend/infrastructure/recoater_client/async_client.py)
- [opcua_simulator.py](file://opcua_simulator.py)

## Core Components

The OPC UA integration layer consists of several core components that work together to enable industrial communication. The `OPCUAServerManager` class in `opcua_server.py` implements the server functionality, exposing coordination variables to external SCADA systems. The `OPCUACoordinator` class in `opcua_coordinator.py` provides a high-level interface for managing these variables and serves as the primary integration point for application logic. Configuration is managed through `opcua_config.py`, which defines the server settings and coordination variables. The `RecoaterClient` abstraction enables communication with the physical recoater device, while `mock_recoater_client.py` provides a development-friendly alternative.

**Section sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/infrastructure/recoater_client/client.py](file://backend/infrastructure/recoater_client/client.py)
- [backend/infrastructure/mock_recoater_client.py](file://backend/infrastructure/mock_recoater_client.py)

## Architecture Overview

The OPC UA integration architecture implements a dual-role pattern where the backend acts as both server and client. As a server, it hosts coordination variables that external PLCs can read and write. As a client, it connects to the recoater device to send commands and receive status updates. This bidirectional communication enables tight integration between the backend system and industrial hardware.

``mermaid
graph TB
subgraph "Backend System"
subgraph "OPC UA Server"
ServerManager[OPCUAServerManager]
Coordinator[OPCUACoordinator]
end
subgraph "Application Logic"
API[REST API]
JobManager[Job Manager]
end
subgraph "Device Interface"
Client[RecoaterClient]
end
end
subgraph "External Systems"
PLC[TwinCAT PLC]
SCADA[SCADA System]
Recoater[Recoater Device]
end
API --> Coordinator
JobManager --> Coordinator
Coordinator --> ServerManager
ServerManager --> PLC
ServerManager --> SCADA
Coordinator --> Client
Client --> Recoater
style ServerManager fill:#f9f,stroke:#333
style Coordinator fill:#bbf,stroke:#333
style Client fill:#f96,stroke:#333
```

**Diagram sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L29-L43)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L32-L48)

## Detailed Component Analysis

### OPC UA Server Implementation

The `OPCUAServerManager` class provides the foundation for OPC UA server functionality, exposing coordination variables to external systems. It manages the lifecycle of the OPC UA server, including startup, shutdown, and error recovery.

``mermaid
classDiagram
class OPCUAServerManager {
-config : OPCUAServerConfig
-server : Optional[Server]
-namespace_idx : int
-variable_nodes : Dict[str, Any]
-_running : bool
-_restart_count : int
-_heartbeat_task : Optional[Task]
+start_server() bool
+stop_server() None
+write_variable(name, val) bool
+read_variable(name) Any
+is_running() bool
+get_variable_names() List[str]
-_create_coordination_variables() None
-_get_ua_data_type(str) ua.Type
-_heartbeat_loop() None
-_cleanup() None
-_handle_server_error(Exception) None
}
OPCUAServerManager --> OPCUAServerConfig : "uses"
OPCUAServerManager --> CoordinationVariable : "creates"
OPCUAServerManager --> Server : "manages"
```

**Diagram sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L64-L77)

**Section sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)

### OPC UA Coordinator Implementation

The `OPCUACoordinator` class provides a high-level interface for OPC UA communication, abstracting away the complexity of direct server management. It offers convenience methods for common operations and manages the connection state between the backend and external systems.

``mermaid
classDiagram
class OPCUACoordinator {
-config : OPCUAServerConfig
-server_manager : OPCUAServerManager
-_connected : bool
-_monitoring_task : Optional[Task]
-_event_handlers : Dict[str, List]
+connect() bool
+disconnect() bool
+is_connected() bool
+get_server_status() Dict
+write_variable(name, val) bool
+read_variable(name) Any
+subscribe_to_changes(...) bool
+set_job_active(id, layers) bool
+set_job_inactive() bool
+update_layer_progress(num) bool
+set_drums_ready(states) bool
+set_error_state(...) bool
+clear_error_flags() bool
-_monitoring_loop() None
-_trigger_event_handlers() None
}
OPCUACoordinator --> OPCUAServerManager : "depends on"
OPCUACoordinator --> OPCUAServerConfig : "uses"
```

**Diagram sources**
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L50-L59)

**Section sources**
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)

### Recoater Client Abstraction

The `RecoaterClient` class provides a clean interface for communicating with the recoater hardware API. It implements proper error handling and retry logic to ensure reliable communication with the physical device. The client has been refactored into a modular package under `infrastructure/recoater_client`, with functionality organized into mixins for blade controls, leveler controls, print controls, file management, and asynchronous operations.

``mermaid
classDiagram
class RecoaterClient {
-base_url : str
-timeout : float
-session : requests.Session
+__init__(base_url, timeout)
+_make_request(method, endpoint, ...) Any
+get_state() Dict[str, Any]
+set_state(action) Dict[str, Any]
+get_config() Dict[str, Any]
+set_config(config) Dict[str, Any]
+get_drums() Dict[str, Any]
+get_drum(drum_id) Dict[str, Any]
+health_check() bool
+get_drum_motion(drum_id) Dict[str, Any]
+set_drum_motion(drum_id, motion) Dict[str, Any]
+get_drum_ejection(drum_id) Dict[str, Any]
+set_drum_ejection(drum_id, ejection) Dict[str, Any]
+get_drum_suction(drum_id) Dict[str, Any]
+set_drum_suction(drum_id, suction) Dict[str, Any]
+get_leveler_position() Dict[str, Any]
+set_leveler_position(position) Dict[str, Any]
+get_leveler_pressure() Dict[str, Any]
+set_leveler_pressure(pressure) Dict[str, Any]
+get_blade_position() Dict[str, Any]
+set_blade_position(position) Dict[str, Any]
+get_blade_screw_info(screw_id) Dict[str, Any]
+set_blade_screw_info(screw_id, info) Dict[str, Any]
+get_blade_screws_info() Dict[str, Any]
+set_blade_screws_info(info) Dict[str, Any]
+get_layer_parameters() Dict[str, Any]
+set_layer_parameters(params) Dict[str, Any]
+upload_drum_geometry(drum_id, file_data, content_type) Dict[str, Any]
+download_drum_geometry(drum_id) Response
+start_print_job(job_data) Dict[str, Any]
+cancel_print_job() Dict[str, Any]
}
class RecoaterConnectionError {
+__init__(message)
}
class RecoaterAPIError {
+__init__(message)
}
RecoaterClient --> RecoaterConnectionError : "throws"
RecoaterClient --> RecoaterAPIError : "throws"
RecoaterClient --> BladeControlMixin : "inherits"
RecoaterClient --> LevelerControlMixin : "inherits"
RecoaterClient --> PrintControlMixin : "inherits"
RecoaterClient --> FileManagementMixin : "inherits"
RecoaterClient --> AsyncClientMixin : "inherits"
```

**Diagram sources**
- [backend/infrastructure/recoater_client/client.py](file://backend/infrastructure/recoater_client/client.py)
- [backend/infrastructure/recoater_client/blade_controls.py](file://backend/infrastructure/recoater_client/blade_controls.py)
- [backend/infrastructure/recoater_client/leveler_controls.py](file://backend/infrastructure/recoater_client/leveler_controls.py)
- [backend/infrastructure/recoater_client/print_controls.py](file://backend/infrastructure/recoater_client/print_controls.py)
- [backend/infrastructure/recoater_client/file_management.py](file://backend/infrastructure/recoater_client/file_management.py)
- [backend/infrastructure/recoater_client/async_client.py](file://backend/infrastructure/recoater_client/async_client.py)

**Section sources**
- [backend/infrastructure/recoater_client/client.py](file://backend/infrastructure/recoater_client/client.py)
- [backend/infrastructure/recoater_client/blade_controls.py](file://backend/infrastructure/recoater_client/blade_controls.py)
- [backend/infrastructure/recoater_client/leveler_controls.py](file://backend/infrastructure/recoater_client/leveler_controls.py)
- [backend/infrastructure/recoater_client/print_controls.py](file://backend/infrastructure/recoater_client/print_controls.py)
- [backend/infrastructure/recoater_client/file_management.py](file://backend/infrastructure/recoater_client/file_management.py)
- [backend/infrastructure/recoater_client/async_client.py](file://backend/infrastructure/recoater_client/async_client.py)

## Data Mapping and Type Conversion

The integration layer implements a robust data mapping system that translates between OPC UA node values and internal Python models. This mapping is defined in the configuration and ensures type safety across the system boundary.

### Configuration Schema

The `opcua_config.py` file defines the configuration schema for the OPC UA integration, including server settings and coordination variables.

```python
@dataclass
class OPCUAServerConfig:
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

### Variable Definition

Coordination variables are defined with their OPC UA metadata, enabling automatic creation and type mapping.

```python
@dataclass
class CoordinationVariable:
    name: str
    node_id: str
    data_type: str
    initial_value: Any
    writable: bool = True
    description: str = ""

COORDINATION_VARIABLES: List[CoordinationVariable] = [
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    # Additional variables...
]
```

### Type Conversion

The server implements type conversion to ensure data integrity when writing to OPC UA nodes.

``mermaid
flowchart TD
Start([Write Variable]) --> ValidateInput["Validate Input Parameters"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Error Response"]
InputValid --> |Yes| DetermineType["Determine Expected Type"]
DetermineType --> CoerceValue["Coerce Value to Expected Type"]
CoerceValue --> CreateVariant["Create UA Variant"]
CreateVariant --> WriteNode["Write to OPC UA Node"]
WriteNode --> ReturnSuccess["Return Success"]
ReturnError --> End([Function Exit])
ReturnSuccess --> End
```

**Diagram sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L400-L524)

**Section sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L326-L357)

## Connection Lifecycle and Fault Recovery

The OPC UA integration implements comprehensive connection lifecycle management and fault recovery mechanisms to ensure system reliability.

### Server Lifecycle

The OPC UA server follows a well-defined lifecycle with proper initialization, operation, and cleanup phases.

``mermaid
stateDiagram-v2
[*] --> Initialized
Initialized --> Starting : start_server()
Starting --> Running : Server started
Running --> Stopping : stop_server()
Stopping --> Stopped : Cleanup complete
Running --> Error : Exception
Error --> Restarting : auto_restart enabled
Restarting --> Starting : restart_delay elapsed
Error --> Stopped : auto_restart disabled
Stopped --> [*]
```

**Diagram sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L73-L87)

### Connection Sequence

The coordinator manages the connection sequence between the backend and OPC UA server.

``mermaid
sequenceDiagram
participant App as "Application"
participant Coordinator as "OPCUACoordinator"
participant Server as "OPCUAServerManager"
App->>Coordinator : connect()
Coordinator->>Server : is_running?
alt Server not running
Coordinator->>Server : start_server()
loop Wait for server
Coordinator->>Server : check is_running()
Server-->>Coordinator : status
end
end
Coordinator->>Coordinator : _connected = true
Coordinator->>Coordinator : Start _monitoring_loop()
Coordinator-->>App : Success
```

**Diagram sources**
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py#L200-L399)

### Fault Recovery

The system implements automatic fault recovery with configurable retry parameters.

``mermaid
sequenceDiagram
participant Server as "OPCUAServerManager"
participant Error as "Exception"
participant Restart as "Auto-Restart Logic"
Server->>Error : Exception occurs
Error->>Restart : _handle_server_error()
Restart->>Restart : Increment _restart_count
alt auto_restart enabled && attempts < max
Restart->>Restart : Wait restart_delay
Restart->>Server : start_server()
Server-->>Restart : Success/Failure
alt Restart failed
Restart->>Restart : Log error
Restart->>Restart : Check attempt count
end
else
Restart->>Restart : Log fatal error
end
```

**Section sources**
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py#L64-L77)

## Network Security and Session Management

The OPC UA integration provides configurable network security and session management options to meet industrial automation requirements.

### Security Configuration

Security settings are configurable through environment variables or defaults, allowing deployment flexibility.

```python
# Security settings from opcua_config.py
security_policy: str = "None"  # No security for internal network
security_mode: str = "None"
certificate_path: str = ""
private_key_path: str = ""
```

These settings can be overridden via environment variables:
- `OPCUA_SECURITY_POLICY`: Security policy (None, Basic256Sha256, etc.)
- `OPCUA_SECURITY_MODE`: Security mode (None, Sign, SignAndEncrypt)
- `OPCUA_CERTIFICATE_PATH`: Path to server certificate file
- `OPCUA_PRIVATE_KEY_PATH`: Path to private key file

### Session Management

The system implements proper session management with configurable timeouts.

```python
# Connection settings from opcua_config.py
connection_timeout: float = 5.0  # Client connection timeout (seconds)
session_timeout: float = 60.0    # OPC UA session timeout (seconds)
```

The server handles session cleanup through the `_cleanup()` method, which clears variable references and resets the server state.

**Section sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py#L235-L256)

## Testing and Development Utilities

The system includes several utilities to support testing and development without requiring physical hardware.

### Mock Recoater Client

The `MockRecoaterClient` provides a simulated interface for development and testing.

``mermaid
classDiagram
class MockRecoaterClient {
-base_url : str
-timeout : float
-_start_time : float
-_state : Dict[str, Any]
-_drum_geometries : Dict[int, Dict]
-_drum_states : Dict[int, str]
+__init__(base_url, timeout)
+get_state() Dict[str, Any]
+get_config() Dict[str, Any]
+set_config(config) Dict[str, Any]
+get_status() Dict[str, Any]
+start_job(job_data) Dict[str, Any]
+stop_job() Dict[str, Any]
+pause_job() Dict[str, Any]
+resume_job() Dict[str, Any]
+get_drum(drum_id) Dict[str, Any]
+set_drum_motion(drum_id, motion) Dict[str, Any]
+upload_drum_geometry(drum_id, file_data, content_type) Dict[str, Any]
+download_drum_geometry(drum_id) Response
+get_layer_parameters() Dict[str, Any]
+set_layer_parameters(params) Dict[str, Any]
}
MockRecoaterClient --|> RecoaterClient : "implements same interface"
```

**Section sources**
- [backend/infrastructure/mock_recoater_client.py](file://backend/infrastructure/mock_recoater_client.py)

### OPC UA Simulator

The `opcua_simulator.py` utility provides a standalone OPC UA server for testing client interactions.

```python
class OPCUAMachineSimulator:
    """
    Simulate an OPC UA server representing an industrial machine.
    
    Supports simulation of different machine types:
    - 3d_printer: Temperature, pressure, humidity, build progress
    - cnc_machine: Spindle speed, vibration, tool wear
    - power_meter: Voltage, current, power readings
    """
```

This simulator allows developers to test OPC UA client code without requiring access to real industrial equipment.

**Section sources**
- [opcua_simulator.py](file://opcua_simulator.py)

## Conclusion

The OPC UA integration layer in APIRecoater_Ethernet implements a sophisticated dual-role architecture that enables seamless communication between the backend system and industrial automation equipment. By acting as both an OPC UA server and client, the system facilitates bidirectional data exchange with external SCADA systems and the recoater device. The architecture emphasizes reliability through comprehensive error handling, automatic fault recovery, and configurable retry logic. Data mapping between OPC UA nodes and internal Python models is handled through a well-defined configuration schema that ensures type safety and consistency. The inclusion of mock clients and simulation utilities enables development and testing without requiring physical hardware, accelerating the development cycle. This integration layer successfully bridges the gap between modern web-based applications and industrial automation standards, providing a robust foundation for industrial IoT applications.