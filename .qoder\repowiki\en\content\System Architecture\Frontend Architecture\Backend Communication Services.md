# Backend Communication Services

<cite>
**Referenced Files in This Document**   
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [status.js](file://frontend/src/stores/status.js#L1-L260)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L100)
- [main.py](file://backend/app/main.py#L1-L162)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the communication architecture between the frontend and backend in the RecoaterSearch system. It details how REST API calls and WebSocket connections are abstracted through the `api.js` service module, enabling seamless interaction between the Vue.js frontend and FastAPI backend. The system supports real-time status monitoring, configuration management, motion control, and print job orchestration. Special focus is given to the reactive state management via Pinia stores and the propagation of real-time updates from hardware through WebSocket broadcasting.

## Project Structure
The project follows a modular structure with clear separation between frontend and backend components. The frontend is built using Vue 3 with Vite, organized into components, views, services, and stores. The backend uses FastAPI with a layered architecture including API routers, services, models, and utilities.

```mermaid
graph TD
subgraph "Frontend"
A[Vue Components]
B[Views]
C[Services]
D[Stores]
end
subgraph "Backend"
E[API Routers]
F[Services]
G[Models]
H[Utilities]
end
C --> E
D --> C
A --> D
F --> H
E --> F
C < --> F
style A fill:#f9f,stroke:#333
style B fill:#f9f,stroke:#333
style C fill:#f9f,stroke:#333
style D fill:#f9f,stroke:#333
style E fill:#bbf,stroke:#333
style F fill:#bbf,stroke:#333
style G fill:#bbf,stroke:#333
style H fill:#bbf,stroke:#333
```

**Diagram sources**
- [frontend/src](file://frontend/src)
- [backend/app](file://backend/app)

**Section sources**
- [frontend/src](file://frontend/src)
- [backend/app](file://backend/app)

## Core Components
The core communication components include the `apiService` for REST interactions, the `useStatusStore` for state management, and the WebSocket infrastructure for real-time updates. These components work together to provide a responsive interface that reflects the actual state of the recoater hardware.

**Section sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

## Architecture Overview
The system employs a hybrid communication model combining REST APIs for command-and-control operations and WebSockets for real-time status updates. The backend acts as a proxy between the frontend and hardware, abstracting OPC UA and direct hardware protocols.

```mermaid
graph LR
subgraph Frontend
UI[User Interface]
Store[Pinia Store]
ApiService[API Service]
end
subgraph Backend
Router[API Router]
Service[Business Logic]
WebSocketManager[WebSocket Manager]
StatusPoller[Status Poller]
Hardware[Hardware Interface]
end
UI --> Store
Store --> ApiService
ApiService --> Router
Router --> Service
Service --> Hardware
StatusPoller --> Hardware
StatusPoller --> WebSocketManager
WebSocketManager --> ApiService
ApiService --> Store
style UI fill:#f9f,stroke:#333
style Store fill:#f9f,stroke:#333
style ApiService fill:#f9f,stroke:#333
style Router fill:#bbf,stroke:#333
style Service fill:#bbf,stroke:#333
style WebSocketManager fill:#bbf,stroke:#333
style StatusPoller fill:#bbf,stroke:#333
style Hardware fill:#bbf,stroke:#333
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L1-L162)
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

## Detailed Component Analysis

### API Service Analysis
The `apiService` module abstracts all HTTP communication with the backend, providing a consistent interface for REST operations. It uses Axios with interceptors for logging and error handling.

#### For API/Service Components:
```mermaid
sequenceDiagram
participant Component as "Vue Component"
participant Store as "Pinia Store"
participant ApiService as "apiService"
participant Backend as "FastAPI Backend"
Component->>Store : Call action
Store->>ApiService : fetchStatus()
ApiService->>Backend : GET /api/v1/status
Backend-->>ApiService : 200 OK + data
ApiService-->>Store : Resolve promise
Store-->>Component : Update state
```

**Diagram sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)

**Section sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)

### Status Store Analysis
The `useStatusStore` manages application state and WebSocket connections. It implements page-aware data subscription to optimize bandwidth usage.

#### For Object-Oriented Components:
```mermaid
classDiagram
class useStatusStore {
+isConnected : boolean
+recoaterStatus : object
+axisData : object
+drumData : object
+levelerData : object
+printData : object
+lastError : string
+lastUpdate : Date
+currentPage : string
+subscribedDataTypes : Set
+isHealthy : boolean
+connected : boolean
+backendHealthy : boolean
+updateStatus(statusData)
+updateAxisData(newAxisData)
+updateDrumData(data)
+updateLevelerData(data)
+updatePrintData(data)
+setError(error)
+setConnectionStatus(connected)
+setCurrentPage(page)
+updateDataSubscriptions()
+setManualSubscriptions(dataTypes)
+fetchStatus()
+connectWebSocket()
+disconnectWebSocket()
}
```

**Diagram sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)

### Status Indicator Component Analysis
The `StatusIndicator` component provides visual feedback on system connectivity and health, using the status store for reactive updates.

#### For API/Service Components:
```mermaid
sequenceDiagram
participant StatusIndicator as "StatusIndicator.vue"
participant StatusStore as "useStatusStore"
participant WebSocket as "WebSocket"
StatusIndicator->>StatusStore : onMounted()
StatusStore->>WebSocket : connectWebSocket()
WebSocket->>StatusStore : onopen
StatusStore->>StatusStore : setConnectionStatus(true)
StatusIndicator->>StatusStore : Read computed properties
StatusStore-->>StatusIndicator : statusClass, statusText, statusTooltip
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L100)
- [status.js](file://frontend/src/stores/status.js#L1-L260)

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L100)

### WebSocket Infrastructure Analysis
The backend WebSocket system consists of a manager for connection handling and a poller for periodic status updates from hardware.

#### For Object-Oriented Components:
```mermaid
classDiagram
class WebSocketConnectionManager {
-active_connections : List[WebSocket]
-connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket)
+disconnect(websocket)
+update_subscription(websocket, data_types)
+broadcast(message)
+_filter_message_for_connection(websocket, message)
+get_required_data_types()
+connection_count
+has_connections
}
class StatusPollingService {
-websocket_manager : WebSocketConnectionManager
-data_gatherer : RecoaterDataGatherer
-poll_interval : float
-polling_task : Task
-_running : bool
+start()
+stop()
+_polling_loop()
+_poll_and_broadcast()
+_broadcast_connection_error(error)
+is_running
+update_poll_interval(interval)
}
StatusPollingService --> WebSocketConnectionManager : "uses"
```

**Diagram sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

## Dependency Analysis
The communication system has well-defined dependencies between frontend and backend components, with clear separation of concerns.

```mermaid
graph TD
api.js --> axios
status.js --> api.js
status.js --> pinia
StatusIndicator.vue --> status.js
main.py --> fastapi
main.py --> websocket_manager.py
main.py --> status_poller.py
status_poller.py --> websocket_manager.py
status_poller.py --> data_gatherer.py
style api.js fill:#f9f,stroke:#333
style status.js fill:#f9f,stroke:#333
style StatusIndicator.vue fill:#f9f,stroke:#333
style main.py fill:#bbf,stroke:#333
style websocket_manager.py fill:#bbf,stroke:#333
style status_poller.py fill:#bbf,stroke:#333
```

**Diagram sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [status.js](file://frontend/src/stores/status.js#L1-L260)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L1-L100)
- [main.py](file://backend/app/main.py#L1-L162)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

**Section sources**
- [api.js](file://frontend/src/services/api.js#L1-L587)
- [status.js](file://frontend/src/stores/status.js#L1-L260)
- [main.py](file://backend/app/main.py#L1-L162)

## Performance Considerations
The system implements several performance optimizations:
- **Page-aware subscriptions**: The status store only requests data relevant to the current view
- **Configurable polling interval**: The status poller interval can be adjusted via environment variables
- **Message filtering**: WebSocket messages are filtered based on client subscriptions to reduce bandwidth
- **Connection reuse**: WebSocket connections are maintained for real-time updates instead of repeated HTTP polling

The default polling interval is 1 second, configurable via the `WEBSOCKET_POLL_INTERVAL` environment variable. The system balances responsiveness with resource usage, ensuring timely updates without overwhelming the network or hardware.

## Troubleshooting Guide
Common issues and their solutions:

**WebSocket Connection Failures**
- **Symptom**: Status indicator shows "Disconnected"
- **Cause**: Backend not running or network issues
- **Solution**: Verify backend is running on port 8000; check CORS configuration

**Stale Status Data**
- **Symptom**: UI not reflecting current hardware state
- **Cause**: WebSocket connection dropped or polling stopped
- **Solution**: Check status poller logs; verify hardware connectivity

**API Request Timeouts**
- **Symptom**: Operations fail with timeout errors
- **Cause**: Network latency or backend overload
- **Solution**: Increase timeout values; check hardware response times

**Missing Data Types**
- **Symptom**: Certain data not appearing in UI
- **Cause**: Subscription not properly updated
- **Solution**: Verify `updateDataSubscriptions()` is called on page changes

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L1-L260)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L147)
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L125)

## Conclusion
The communication architecture effectively bridges the frontend and backend systems, providing a responsive and reliable interface for controlling the recoater hardware. The combination of REST APIs for discrete operations and WebSockets for real-time updates creates a seamless user experience. The modular design allows for easy maintenance and extension, while the reactive state management ensures the UI always reflects the current system state. Future improvements could include more granular error handling and enhanced retry mechanisms for transient failures.