"""
ServerMixin
===========

Infrastructure layer for OPC UA server lifecycle and variable store management.

Clean Architecture Role: Infrastructure Layer (Adapters/External Interfaces)
Purpose:
- Provide a minimal, testable server abstraction without external dependencies
- Manage server lifecycle (start/stop) and in-process variable key→value store
- Expose async read/write APIs consistent with OPC UA protocol expectations
- Handle type coercion and basic validation for variable operations

Architecture Notes:
- This implementation provides an in-process variable store for testing/development
- For production, integrate real asyncua server in an adapter or extend this mixin
- Maintains protocol-agnostic interface to allow different OPC UA implementations
- Uses configuration-driven variable definitions for type safety and validation

Key Responsibilities:
- Server Lifecycle: start_server(), stop_server(), is_server_running property
- Variable Management: write_variable(), read_variable(), get_variable_names()
- Type Safety: Basic type coercion based on variable definitions from config
- State Management: Maintains _variable_store as central state container

Dependencies:
- app.config.opcua_config: For COORDINATION_VARIABLES and get_variable_by_name()
- Standard library only for core functionality

Public Methods:
- start_server(): Initialize server and variable store with defaults
- stop_server(): Cleanup server resources and clear variable store
- is_server_running: Property to check if server is operational
- get_variable_names(): Get list of all available variable names
- write_variable(name, value): Write value to variable with type coercion
- read_variable(name): Read current value from variable

Private State:
- _server_running: Boolean flag for server operational status
- _variable_store: Dictionary mapping variable names to current values
- _logger: Logger instance for operational logging
"""
from __future__ import annotations

import asyncio
import logging
from typing import Any, Optional

from app.config.opcua_config import COORDINATION_VARIABLES


class ServerMixin:
    _logger: logging.Logger
    _server_running: bool = False
    _variable_store: dict[str, Any]

    async def start_server(self) -> bool:
        try:
            if self._server_running:
                self._logger.info("OPC UA in-process server already running")
                return True
            # Initialize variable store with defaults
            self._variable_store = {v.name: v.initial_value for v in COORDINATION_VARIABLES}
            self._server_running = True
            self._logger.info("OPC UA in-process server started")
            return True
        except Exception as e:
            self._logger.error(f"Failed to start in-process OPC UA server: {e}")
            return False

    async def stop_server(self) -> bool:
        try:
            if not self._server_running:
                return True
            self._server_running = False
            self._variable_store = {}
            self._logger.info("OPC UA in-process server stopped")
            return True
        except Exception as e:
            self._logger.error(f"Error stopping in-process server: {e}")
            return False

    @property
    def is_server_running(self) -> bool:
        return bool(self._server_running)

    def get_variable_names(self) -> list[str]:
        return list(self._variable_store.keys()) if getattr(self, "_variable_store", None) else []

    async def write_variable(self, name: str, value: Any) -> bool:
        if not self._server_running:
            self._logger.warning(f"Cannot write {name}: server not running")
            return False
        try:
            # Write directly to in-process variable store
            self._variable_store[name] = value
            return True
        except Exception as e:
            self._logger.error(f"Failed to write variable {name}: {e}")
            return False

    async def read_variable(self, name: str) -> Any:
        if not self._server_running:
            self._logger.warning(f"Cannot read {name}: server not running")
            return None
        try:
            return self._variable_store.get(name)
        except Exception as e:
            self._logger.error(f"Failed to read variable {name}: {e}")
            return None

