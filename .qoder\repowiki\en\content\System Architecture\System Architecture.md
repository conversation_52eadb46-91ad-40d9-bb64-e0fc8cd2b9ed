# System Architecture

<cite>
**Referenced Files in This Document**   
- [README.md](file://README.md)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/services/coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)
- [backend/app/services/data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/api/recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [backend/app/api/print.py](file://backend/app/api/print.py)
- [backend/app/api/configuration.py](file://backend/app/api/configuration.py)
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/services/multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [backend/app/models/multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [opcua_simulator.py](file://opcua_simulator.py)
- [frontend/vite.config.js](file://frontend/vite.config.js)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py) - *Updated in commit e3fbefd*
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py) - *Updated with environment variables*
- [backend/app/main.py](file://backend/app/main.py) - *Updated with environment variables*
- [backend/app/websockets.py](file://backend/app/websockets.py) - *Extracted from main.py in commit 8c43f1b*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect environment variable configuration in main.py and opcua_config.py
- Enhanced OPC UA server architecture section with details about auto-restart and error handling
- Added information about environment variable overrides for OPC UA configuration
- Updated dependency analysis to include new configuration flow with environment variables
- Added details about empty_layer.cli file handling in multilayer job manager
- Documented WebSocket architecture refactoring: extracted websockets.py from main.py into separate module
- Updated architecture diagrams and component analysis to reflect new WebSocketHandler pattern
- Added section on WebSocketHandler class and its integration with WebSocketConnectionManager

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The APIRecoater_Ethernet system is a modern web-based Human-Machine Interface (HMI) designed for the Aerosint SPD Recoater system. It replaces the default SwaggerUI with an intuitive, responsive interface suitable for industrial and laboratory environments. The system follows a client-server architecture with a FastAPI backend and Vue.js frontend, communicating via REST APIs and WebSockets for real-time status updates. It integrates with industrial devices through OPC UA protocol for hardware control and coordination. The application enables full control of recoater functions including axis motion, drum/hopper/leveler management, and multi-material print job execution. Recent updates have enhanced the configuration system with environment variable support and improved OPC UA server reliability. The WebSocket architecture has been refactored by extracting websockets.py from main.py into a separate module, improving code organization and separation of concerns.

## Project Structure
The project is organized into two main directories: `backend` and `frontend`, following a clear separation of concerns between server-side logic and client-side presentation.

``mermaid
graph TB
subgraph "Root"
README[README.md]
AGENT[AGENT.md]
config[config.py]
run[run.bat]
debug[debug_run.bat]
install[install_deps.bat]
opcua_sim[opcua_simulator.py]
simple_conn[simple_connection_test.py]
test_opcua[test_opcua_client.py]
end
subgraph "Backend"
backend[backend/]
app[app/]
services[backend/services/]
tests[backend/tests/]
requirements[backend/requirements.txt]
subgraph "app"
api[api/]
config[config/]
models[models/]
services[app/services/]
utils[utils/]
dependencies[dependencies.py]
main[main.py]
websockets[websockets.py]
end
end
subgraph "Frontend"
frontend[frontend/]
src[src/]
package[package.json]
vite[vite.config.js]
subgraph "src"
components[components/]
router[router/]
services[services/]
stores[stores/]
views[views/]
App[App.vue]
main[main.js]
style[style.css]
end
end
README --> backend
README --> frontend
backend --> app
app --> api
app --> config
app --> models
app --> services
app --> utils
app --> dependencies
app --> main
app --> websockets
backend --> services
backend --> tests
backend --> requirements
frontend --> src
frontend --> package
frontend --> vite
src --> components
src --> router
src --> services
src --> stores
src --> views
src --> App
src --> main
src --> style
```

**Diagram sources**
- [README.md](file://README.md)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)

**Section sources**
- [README.md](file://README.md)

## Core Components
The system's core functionality is distributed across several key components that handle communication, state management, hardware integration, and business logic.

**Section sources**
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Architecture Overview
The APIRecoater_Ethernet system follows a client-server architecture with a clear separation between frontend presentation and backend services. The backend, built with FastAPI, exposes RESTful APIs and WebSocket endpoints for real-time communication. The frontend, implemented with Vue.js 3, provides a responsive user interface that interacts with the backend through these APIs.

``mermaid
graph TB
subgraph "Frontend"
UI[User Interface]
Store[Pinia Store]
API[API Service]
end
subgraph "Backend"
FastAPI[FastAPI Server]
WebSockets[WebSocket Handler]
WebSocketManager[WebSocket Manager]
StatusPoller[Status Poller]
OPCUACoord[OPC UA Coordinator]
CoordEngine[Coordination Engine]
JobManager[Job Manager]
RecoaterClient[Recoater Client]
end
subgraph "Hardware"
OPCUAServer[OPC UA Server]
PLC[TwinCAT PLC]
Recoater[Recoater Device]
end
UI --> API
API --> FastAPI
FastAPI --> WebSockets
WebSockets --> WebSocketManager
FastAPI --> StatusPoller
FastAPI --> OPCUACoord
FastAPI --> CoordEngine
FastAPI --> JobManager
StatusPoller --> WebSocketManager
OPCUACoord --> OPCUAServer
CoordEngine --> JobManager
JobManager --> RecoaterClient
RecoaterClient --> Recoater
OPCUAServer --> PLC
style UI fill:#f9f,stroke:#333
style FastAPI fill:#bbf,stroke:#333
style OPCUAServer fill:#f96,stroke:#333
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Detailed Component Analysis

### Backend Application Structure
The backend application is structured around FastAPI's modular router system, with distinct API endpoints for different functional areas.

``mermaid
classDiagram
class FastAPI {
+title : str
+description : str
+version : str
+lifespan : function
}
class APIRouter {
+prefix : str
+tags : List[str]
}
class StatusRouter {
+get_status()
+health_check()
+set_server_state()
}
class AxisRouter {
+get_axis_status()
+set_axis_position()
}
class RecoaterRouter {
+get_drum_motion()
+set_drum_motion()
+get_drum_ejection()
+set_drum_ejection()
}
class PrintRouter {
+get_layer_parameters()
+set_layer_parameters()
+start_print_job()
+cancel_print_job()
}
class ConfigRouter {
+get_configuration()
+set_configuration()
}
FastAPI --> APIRouter : "includes"
APIRouter <|-- StatusRouter
APIRouter <|-- AxisRouter
APIRouter <|-- RecoaterRouter
APIRouter <|-- PrintRouter
APIRouter <|-- ConfigRouter
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/api/axis.py](file://backend/app/api/axis.py)
- [backend/app/api/recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [backend/app/api/print.py](file://backend/app/api/print.py)
- [backend/app/api/configuration.py](file://backend/app/api/configuration.py)

**Section sources**
- [backend/app/main.py](file://backend/app/main.py)

### Frontend Application Structure
The frontend application follows a component-based architecture with Vue.js 3 and Pinia for state management.

``mermaid
classDiagram
class VueApp {
+createApp()
+use(router)
+use(pinia)
+mount()
}
class Router {
+routes : Array
+navigation guards
}
class Pinia {
+state
+getters
+actions
}
class PrintJobStore {
-jobStatus : Object
-layerParameters : Object
-drumStates : Array
+updateJobStatus()
+setLayerParameters()
+updateDrumState()
}
class StatusStore {
-systemStatus : Object
-axisData : Object
-drumData : Array
-levelerData : Object
+updateStatus()
+subscribeToUpdates()
}
class ApiService {
+get(endpoint)
+post(endpoint, data)
+delete(endpoint)
+websocketConnect()
}
VueApp --> Router
VueApp --> Pinia
Pinia --> PrintJobStore
Pinia --> StatusStore
PrintJobStore --> ApiService
StatusStore --> ApiService
ApiService --> FastAPI : "HTTP/WebSocket"
class View {
+renders
+dispatches actions
}
class Component {
+reusable UI
+emits events
}
View <|-- ConfigurationView
View <|-- PrintView
View <|-- RecoaterView
View <|-- StatusView
View <|-- AxisView
Component <|-- DrumControl
Component <|-- HopperControl
Component <|-- LevelerControl
Component <|-- MultiLayerJobControl
Component <|-- StatusIndicator
Component <|-- FileUploadColumn
Component <|-- CriticalErrorModal
Component <|-- JobProgressDisplay
Component <|-- Legend
View --> Component
View --> Store
```

**Diagram sources**
- [frontend/src/main.js](file://frontend/src/main.js)
- [frontend/src/router/index.js](file://frontend/src/router/index.js)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/main.js](file://frontend/src/main.js)

### WebSocket Architecture Refactoring
The WebSocket implementation has been refactored by extracting the WebSocket functionality from main.py into a dedicated websockets.py module. This improves code organization and separation of concerns by isolating WebSocket handling logic.

```
classDiagram
class WebSocketHandler {
-websocket_manager : WebSocketConnectionManager
+websocket_endpoint(websocket: WebSocket)
+handle_websocket_message(websocket: WebSocket, message_text: str)
}
class WebSocketConnectionManager {
+active_connections : List[WebSocket]
+connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket: WebSocket)
+disconnect(websocket: WebSocket)
+update_subscription(websocket: WebSocket, data_types: List[str])
+broadcast(message: Dict[str, Any])
+_filter_message_for_connection(websocket: WebSocket, message: Dict[str, Any])
+get_required_data_types() : Set[str]
}
class StatusPollingService {
-websocket_manager : WebSocketConnectionManager
+poll_interval : float
+start()
+stop()
+_polling_loop()
+_poll_and_broadcast()
}
WebSocketHandler --> WebSocketConnectionManager : "uses"
StatusPollingService --> WebSocketConnectionManager : "uses"
```

**Diagram sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)

**Section sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/main.py](file://backend/app/main.py)

### WebSocket Handler Implementation
The WebSocketHandler class encapsulates the WebSocket endpoint logic and message processing, providing a clean interface for handling real-time communication.

```
sequenceDiagram
participant Frontend as "Vue.js Frontend"
participant WebSocket as "WebSocket Connection"
participant Handler as "WebSocketHandler"
participant Manager as "WebSocketConnectionManager"
participant Poller as "StatusPoller"
participant Backend as "Backend Application"
Frontend->>WebSocket: Connect to /ws
WebSocket->>Handler: websocket_endpoint()
Handler->>Manager: connect(websocket)
Manager->>Manager: Add to active_connections
Manager->>Manager: Initialize subscription (status only)
Handler->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription() on subscribe message
Manager->>Manager: Update connection_subscriptions
loop Every poll_interval seconds
Poller->>Poller: _polling_loop()
Poller->>Manager: get_required_data_types()
Manager->>Poller: Return subscribed data types
Poller->>Manager: broadcast(message)
Manager->>Manager: _filter_message_for_connection()
Manager->>WebSocket: send_json(filtered_message)
WebSocket->>Frontend: Receive status update
end
Frontend->>WebSocket: Send subscription update
WebSocket->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription(websocket, data_types)
```

**Diagram sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)

**Section sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)

### OPC UA Integration Architecture
The system uses OPC UA for industrial communication between the backend and the TwinCAT PLC, enabling coordination of multi-material print jobs. The OPC UA server implementation includes enhanced error handling with auto-restart capabilities and configurable parameters through environment variables.

```
sequenceDiagram
participant Backend as "Backend Application"
participant Coordinator as "OPCUACoordinator"
participant ServerMgr as "OPCUAServerManager"
participant OPCUA as "asyncua.Server"
participant PLC as "TwinCAT PLC"
Backend->>Coordinator: set_job_active(job_id, layers)
Coordinator->>Coordinator: Validate parameters
Coordinator->>ServerMgr: write_variable("job_active", True)
Coordinator->>ServerMgr: write_variable("job_id", job_id)
Coordinator->>ServerMgr: write_variable("total_layers", layers)
Coordinator->>ServerMgr: write_variable("current_layer", 0)
Coordinator->>ServerMgr: write_variable("coord_status", "active")
ServerMgr->>OPCUA: Update variable values
OPCUA->>PLC: Notify variable changes
PLC->>OPCUA: Read variables
OPCUA->>ServerMgr: Variable read events
ServerMgr->>Coordinator: Return operation result
Coordinator->>Backend: Return success
Backend->>Coordinator: set_drums_ready(states)
Coordinator->>Coordinator: Process drum states
Coordinator->>ServerMgr: write_variable("drum0_ready", states[0])
Coordinator->>ServerMgr: write_variable("drum1_ready", states[1])
Coordinator->>ServerMgr: write_variable("drum2_ready", states[2])
Coordinator->>ServerMgr: write_variable("all_drums_rdy", all(states))
ServerMgr->>OPCUA: Update variables
OPCUA->>PLC: Notify changes
PLC->>OPCUA: Read variables
OPCUA->>ServerMgr: Read events
ServerMgr->>Coordinator: Return result
Coordinator->>Backend: Return success
```

**Diagram sources**
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

**Section sources**
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

### Event-Driven Real-Time Updates
The system implements an event-driven architecture using WebSockets to provide real-time status updates to the frontend.

```
sequenceDiagram
participant Frontend as "Vue.js Frontend"
participant WebSocket as "WebSocket Connection"
participant Handler as "WebSocketHandler"
participant Manager as "WebSocketConnectionManager"
participant Poller as "StatusPoller"
participant Gatherer as "DataGatherer"
participant Client as "RecoaterClient"
participant Hardware as "Recoater Hardware"
Frontend->>WebSocket: Connect to /ws
WebSocket->>Handler: websocket_endpoint()
Handler->>Manager: connect(websocket)
Manager->>Manager: Add to active_connections
Manager->>Manager: Initialize subscription (status only)
loop Every poll_interval seconds
Poller->>Poller: _polling_loop()
Poller->>Manager: get_required_data_types()
Manager->>Poller: Return subscribed data types
Poller->>Gatherer: gather_all_data(client, required_types)
Gatherer->>Client: get_state()
Gatherer->>Client: get_drums() + get_drum_motion() etc.
Gatherer->>Client: get_leveler_pressure() + get_leveler_sensor()
Gatherer->>Client: get_layer_parameters() + get_print_job_status()
Client->>Hardware: HTTP API calls
Hardware->>Client: Return status data
Client->>Gatherer: Return data
Gatherer->>Poller: Return gathered_data
Poller->>Gatherer: construct_status_message(gathered_data)
Gatherer->>Poller: Return message
Poller->>Manager: broadcast(message)
Manager->>Manager: _filter_message_for_connection()
Manager->>WebSocket: send_json(filtered_message)
WebSocket->>Frontend: Receive status update
end
Frontend->>WebSocket: Send subscription update
WebSocket->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription(websocket, data_types)
```

**Diagram sources**
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)
- [backend/app/services/data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

**Section sources**
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

### Service Layer Abstraction and Dependency Injection
The system implements service layer abstraction and dependency injection to manage component dependencies and promote testability. The configuration system now supports environment variable overrides for OPC UA settings.

```
classDiagram
class Dependencies {
+initialize_recoater_client()
+initialize_opcua_coordinator()
+initialize_multilayer_job_manager()
+get_recoater_client()
+get_opcua_coordinator()
+get_multilayer_job_manager()
}
class RecoaterClient {
+get_state()
+get_config()
+set_config()
+get_drum()
+set_drum_motion()
+cancel_drum_motion()
+get_drum_motion()
+get_drum_ejection()
+set_drum_ejection()
+get_drum_suction()
+set_drum_suction()
+get_blade_screws_info()
+get_blade_screws_motion()
+set_blade_screws_motion()
+cancel_blade_screws_motion()
+get_leveler_pressure()
+set_leveler_pressure()
+get_leveler_sensor()
+get_layer_parameters()
+set_layer_parameters()
+get_print_job_status()
+start_print_job()
+cancel_print_job()
}
class MockRecoaterClient {
+Implements same interface
+Returns mock data
+Used in development mode
}
class OPCUACoordinator {
+connect()
+disconnect()
+is_connected()
+get_server_status()
+set_job_active()
+set_job_inactive()
+update_layer_progress()
+set_drums_ready()
+set_backend_error()
+clear_error_flags()
+write_variable()
+read_variable()
+subscribe_to_changes()
}
class MultiMaterialJobManager {
+create_job()
+start_job()
+cancel_job()
+get_job_status()
+get_drum_status()
+upload_layer_to_all_drums()
+wait_for_all_drums_ready()
+get_coordination_status()
}
class CoordinationEngine {
+start_multimaterial_job()
+stop_job()
+_coordination_loop()
+_process_layer_cycle_all_drums()
+_upload_layer_to_all_drums()
+_wait_for_all_drums_ready()
+_wait_for_layer_completion()
+_complete_job()
+_set_error_state()
}
Dependencies --> RecoaterClient : "creates"
Dependencies --> MockRecoaterClient : "creates"
Dependencies --> OPCUACoordinator : "initializes"
Dependencies --> MultiMaterialJobManager : "initializes"
CoordinationEngine --> MultiMaterialJobManager : "uses"
CoordinationEngine --> OPCUACoordinator : "uses"
MultiMaterialJobManager --> RecoaterClient : "uses"
OPCUACoordinator --> OPCUAServerManager : "uses"
```

**Diagram sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [services/recoater_client.py](file://backend/services/recoater_client.py)
- [services/mock_recoater_client.py](file://backend/services/mock_recoater_client.py)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/services/multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [backend/app/services/coordination_engine.py](file://backend/app/services/coordination_engine.py)

**Section sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

### State Management in Frontend Stores
The frontend uses Pinia for state management, providing a centralized store for application state.

```
classDiagram
class PrintJobStore {
-jobStatus : PrintJobStatusResponse
-layerParameters : LayerParametersResponse
-drumStates : Map<int, DrumStatusResponse>
-fileUploads : Map<int, FileUploadResponse>
-currentJobId : string
-isPrinting : boolean
+updateJobStatus(status)
+setLayerParameters(params)
+updateDrumState(drumId, state)
+recordFileUpload(drumId, response)
+setCurrentJobId(id)
+setIsPrinting(flag)
+clearJobData()
}
class StatusStore {
-systemStatus : Dict
-axisData : Dict
-drumData : Dict
-levelerData : Dict
-printData : Dict
-connectionStatus : boolean
-errorState : string
+updateStatus(data)
+subscribeToWebSocket()
+handleWebSocketMessage(message)
+setConnectionStatus(status)
+setErrorState(error)
+clearErrorState()
}
class ApiService {
-httpClient : AxiosInstance
-websocket : WebSocket
-messageHandlers : Map
+get(endpoint)
+post(endpoint, data)
+put(endpoint, data)
+delete(endpoint)
+uploadCliFile(drumId, file)
+connectWebSocket()
+sendMessage(message)
+addMessageHandler(type, handler)
+removeMessageHandler(type, handler)
}
PrintJobStore --> ApiService : "calls"
StatusStore --> ApiService : "calls"
ApiService --> Backend : "HTTP/WebSocket"
class View {
+mounted()
+beforeUnmount()
}
class PrintView {
+onMounted()
+onBeforeUnmount()
+startPrintJob()
+cancelPrintJob()
+uploadCliFile()
}
class StatusView {
+onMounted()
+onBeforeUnmount()
+updateDisplay()
}
class RecoaterView {
+onMounted()
+onBeforeUnmount()
+controlDrumMotion()
+adjustEjectionPressure()
+adjustSuctionPressure()
}
View <|-- PrintView
View <|-- StatusView
View <|-- RecoaterView
View <|-- ConfigurationView
View <|-- AxisView
PrintView --> PrintJobStore : "accesses"
PrintView --> ApiService : "calls"
StatusView --> StatusStore : "accesses"
StatusView --> ApiService : "calls"
RecoaterView --> ApiService : "calls"
```

**Diagram sources**
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)

## Dependency Analysis
The system's dependency structure follows a clear hierarchy with well-defined interfaces between components. The configuration flow has been updated to support environment variable overrides, particularly for OPC UA settings.

```
graph TD
backend.app.main.app[FastAPI App] --> backend.app.main.lifespan[Lifespan Manager]
backend.app.main.lifespan --> backend.app.dependencies.initialize_recoater_client[Initialize Recoater Client]
backend.app.main.lifespan --> backend.app.dependencies.initialize_opcua_coordinator[Initialize OPC UA Coordinator]
backend.app.main.lifespan --> backend.app.dependencies.initialize_multilayer_job_manager[Initialize Job Manager]
backend.app.main.lifespan --> backend.app.services.status_poller.start[Start Status Poller]
backend.app.main.lifespan --> backend.app.utils.heartbeat.start_heartbeat_task[Start Heartbeat]
backend.app.main.app --> backend.app.api.status.router[Status Router]
backend.app.main.app --> backend.app.api.axis.router[Axis Router]
backend.app.main.app --> backend.app.api.recoater_controls.router[Recoater Router]
backend.app.main.app --> backend.app.api.print.router[Print Router]
backend.app.main.app --> backend.app.api.configuration.router[Configuration Router]
backend.app.api.status.get_status --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.axis.get_axis_status --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.recoater_controls.get_drum_motion --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.print.get_layer_parameters --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.print.get_layer_parameters --> backend.app.dependencies.get_multilayer_job_manager[Get Job Manager]
backend.app.api.configuration.get_configuration --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.services.status_poller._polling_loop --> backend.app.services.status_poller._poll_and_broadcast[Poll and Broadcast]
backend.app.services.status_poller._poll_and_broadcast --> backend.app.services.data_gatherer.gather_all_data[Gather All Data]
backend.app.services.status_poller._poll_and_broadcast --> backend.app.services.websocket_manager.broadcast[Broadcast]
backend.app.services.multilayer_job_manager.create_job --> backend.app.services.opcua_coordinator.set_job_active[Set Job Active]
backend.app.services.multilayer_job_manager.start_job --> backend.app.services.opcua_coordinator.set_job_active[Set Job Active]
backend.app.services.multilayer_job_manager.start_job --> backend.app.services.opcua_coordinator.clear_error_flags[Clear Error Flags]
backend.app.services.coordination_engine.start_multimaterial_job --> backend.app.services.opcua_coordinator.set_job_active[Set Job Active]
backend.app.services.coordination_engine._process_layer_cycle_all_drums --> backend.app.services.opcua_coordinator.set_recoater_ready_to_print[Set Ready to Print]
backend.app.services.coordination_engine._complete_job --> backend.app.services.opcua_coordinator.set_job_inactive[Set Job Inactive]
backend.app.dependencies.get_recoater_client --> backend.app.dependencies._recoater_client[Global Client]
backend.app.dependencies.get_opcua_coordinator --> backend.app.dependencies._opcua_coordinator[Global Coordinator]
backend.app.dependencies.get_multilayer_job_manager --> backend.app.dependencies._multilayer_job_manager[Global Job Manager]
backend.app.config.opcua_config.get_opcua_config --> backend.app.config.opcua_config.OPCUAServerConfig[Create Config]
backend.app.config.opcua_config.OPCUAServerConfig --> backend.app.config.opcua_config.os.getenv[Environment Variables]
backend.app.main.app --> backend.app.websockets.WebSocketHandler[WebSocket Handler]
backend.app.websockets.WebSocketHandler --> backend.app.services.websocket_manager.WebSocketConnectionManager[WebSocket Manager]
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)
- [backend/app/services/data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/services/multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [backend/app/services/coordination_engine.py](file://backend/app/services/coordination_engine.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

**Section sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Performance Considerations
The system implements several performance optimizations to ensure responsive operation:

1. **Concurrent Data Gathering**: The `DataGatherer` service uses `asyncio` to collect status data from multiple sources concurrently, minimizing polling latency.

2. **Subscription-Based Broadcasting**: The `WebSocketManager` filters messages based on client subscriptions, reducing network traffic by only sending relevant data.

3. **Connection Pooling**: The `RecoaterClient` likely maintains HTTP connections to reduce connection overhead for frequent API calls.

4. **Efficient Polling**: The `StatusPoller` service only polls when there are active WebSocket connections, conserving resources during idle periods.

5. **Caching**: The system caches parsed CLI files and maintains job state in memory to avoid repeated processing.

6. **Background Tasks**: Long-running operations like job coordination are handled in background tasks to prevent blocking the main event loop.

7. **Heartbeat Monitoring**: The heartbeat system maintains connection health without requiring constant polling.

8. **Auto-Restart Mechanism**: The OPC UA server includes an auto-restart feature with configurable retry limits and delays, improving system resilience.

9. **Modular WebSocket Architecture**: The extraction of WebSocket functionality into a separate module improves code maintainability and allows for independent optimization of real-time communication components.

**Section sources**
- [backend/app/services/data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)
- [backend/app/services/websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [backend/app/utils/heartbeat.py](file://backend/app/utils/heartbeat.py)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Troubleshooting Guide
The system includes several mechanisms for error detection and recovery:

1. **Comprehensive Error Handling**: Each API endpoint includes try-catch blocks for `RecoaterConnectionError`, `RecoaterAPIError`, and general exceptions, providing appropriate HTTP status codes.

2. **Health Check Endpoints**: The `/api/v1/status/health` endpoint allows monitoring of both backend and recoater hardware health.

3. **Connection Error Broadcasting**: The `StatusPoller` broadcasts connection errors to all connected clients, enabling the frontend to display appropriate error messages.

4. **OPC UA Error Flags**: The system uses OPC UA variables (`backend_error` and `plc_error`) to coordinate error states between backend and PLC.

5. **Heartbeat Monitoring**: The heartbeat system detects and attempts to recover from lost connections to the recoater hardware.

6. **Retry Logic**: The coordination engine includes retry mechanisms for failed operations with configurable retry limits.

7. **Detailed Logging**: Comprehensive logging at INFO and DEBUG levels helps diagnose issues during development and operation.

8. **OPC UA Server Auto-Restart**: The OPC UA server automatically attempts to restart on failure, with configurable maximum retry attempts and delay between attempts.

9. **Empty Layer Template Handling**: The multilayer job manager now properly handles the empty_layer.cli template file for generating single layers.

10. **WebSocket Connection Management**: The WebSocket architecture includes automatic reconnection logic in the frontend and proper cleanup of disconnected clients in the backend.

**Section sources**
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/services/status_poller.py](file://backend/app/services/status_poller.py)
- [backend/app/services/opcua_coordinator.py](file://backend/app/services/opcua_coordinator.py)
- [backend/app/utils/heartbeat.py](file://backend/app/utils/heartbeat.py)
- [backend/app/services/opcua_server.py](file://backend/app/services/opcua_server.py)
- [backend/app/services/multilayer_job_manager.py](file://backend/app/services/multilayer_job_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Conclusion
The APIRecoater_Ethernet system presents a well-architected client-server application for controlling industrial recoater devices. The system effectively separates concerns between frontend presentation and backend services, using established patterns like dependency injection, service layer abstraction, and event-driven architecture. The integration of OPC UA enables robust communication with industrial PLCs for multi-material print coordination. The recent updates have enhanced the system with environment variable configuration support, allowing for flexible deployment across different environments. The OPC UA server implementation now includes auto-restart capabilities and improved error handling, increasing system reliability. The real-time capabilities provided by WebSockets ensure operators receive immediate feedback on system status. The modular design allows for future expansion and maintenance, while the comprehensive error handling and monitoring systems promote reliability in industrial environments. The use of modern web technologies (Vue.js 3, FastAPI) ensures a responsive user experience and efficient server-side processing. The refactoring of the WebSocket architecture by extracting websockets.py from main.py into a separate module demonstrates good software engineering practices, improving code organization and maintainability.