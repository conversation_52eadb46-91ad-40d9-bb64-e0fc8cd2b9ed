# Leveler Control Component

<cite>
**Referenced Files in This Document**   
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue)
- [api.js](file://frontend/src/services/api.js)
- [LevelerControl.test.js](file://frontend/src/components/__tests__/LevelerControl.test.js)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Component Overview](#component-overview)
3. [User Interface Structure](#user-interface-structure)
4. [Core Functionality](#core-functionality)
5. [Backend Integration](#backend-integration)
6. [Testing Coverage](#testing-coverage)
7. [Error Handling](#error-handling)
8. [Performance Considerations](#performance-considerations)
9. [Common Issues and Solutions](#common-issues-and-solutions)

## Introduction
The Leveler Control Component provides a user interface for managing the leveler mechanism in the APIRecoater_Ethernet system. This component enables operators to control pressure settings, monitor sensor states, and detect faults in the leveler system. The component is designed to provide real-time feedback and ensure safe operation of the leveler mechanism through comprehensive validation and error handling.

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

## Component Overview
The LevelerControl.vue component is a Vue.js single-file component that manages the leveler mechanism through a clean, intuitive interface. It provides controls for pressure adjustment and displays the status of the magnetic sensor. The component follows Vue 3's Composition API pattern and integrates with the application's state management and API services.

The component receives three main props:
- **pressureData**: Object containing current, target, and maximum pressure values
- **sensorData**: Object containing the state of the magnetic sensor
- **connected**: Boolean indicating connection status to the recoater system

It emits three events:
- **error**: When an API call fails
- **success**: When a pressure setting operation succeeds
- **pressure-set**: When pressure is successfully set

```mermaid
flowchart TD
A[LevelerControl Component] --> B[Template]
A --> C[Script]
A --> D[Style]
B --> E[Card Header with Status]
B --> F[Pressure Control Section]
B --> G[Sensor Status Section]
B --> H[Error Message Display]
C --> I[Props: pressureData, sensorData, connected]
C --> J[Emits: error, success, pressure-set]
C --> K[Reactive State: targetPressure, isSettingPressure, errorMessage]
C --> L[Computed: isValidPressure]
C --> M[Methods: setPressure, showError]
D --> N[Scoped Styles for UI Elements]
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

## User Interface Structure
The component's user interface is organized into a card-based layout with clear sections for different functionality. The UI follows consistent styling patterns with other control components in the application.

### Card Header
The header displays the component title and connection status:
- **Title**: "Leveler Control" in a prominent font
- **Status Indicator**: Color-coded dot and text showing "Ready" (green) or "Disconnected" (gray)

### Pressure Control Section
This section provides the primary interface for pressure management:
- **Current Pressure**: Display of actual measured pressure
- **Target Pressure**: Display of desired pressure setting
- **Maximum Pressure**: Display of system maximum limit
- **Pressure Input**: Number input field for entering target pressure
- **Set Button**: Action button to apply the pressure setting

### Sensor Status Section
Displays the state of the magnetic sensor:
- **Sensor State Label**: "Sensor State"
- **Status Indicator**: Color-coded dot showing "Active (Field Detected)" (green) or "Inactive" (gray)

### Error Display
A dedicated error message area that appears when operations fail:
- **Red Background**: Visual indication of error state
- **Auto-dismiss**: Messages disappear after 5 seconds
- **Clear Text**: Specific error details from the backend

```mermaid
flowchart TD
A[Leveler Control Card] --> B[Card Header]
A --> C[Pressure Control]
A --> D[Sensor Status]
A --> E[Error Display]
B --> B1[Title: Leveler Control]
B --> B2[Status Indicator]
B2 --> B2a[Connected: Green Dot + Ready]
B2 --> B2b[Disconnected: Gray Dot + Disconnected]
C --> C1[Current Pressure Display]
C --> C2[Target Pressure Display]
C --> C3[Maximum Pressure Display]
C --> C4[Pressure Input Field]
C --> C5[Set Button]
D --> D1[Sensor State Label]
D --> D2[Sensor Status Indicator]
D2 --> D2a[Active: Green Dot]
D2 --> D2b[Inactive: Gray Dot]
E --> E1[Error Message Container]
E --> E2[Error Text Display]
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

## Core Functionality
The LevelerControl component implements several key functions for managing the leveler mechanism.

### Pressure Setting Workflow
The component enables users to set the target pressure for the leveler mechanism through a validated workflow:

1. User enters a target pressure value in the input field
2. Real-time validation ensures the value is:
   - A valid number
   - Non-negative
   - Within the maximum pressure limit
3. When valid, the "Set" button becomes enabled
4. Clicking the button triggers the pressure setting process
5. During processing, the UI shows "Setting..." and disables controls
6. On success, the pressure is updated and UI resets
7. On failure, an error message is displayed

### Reactive State Management
The component uses Vue's reactivity system to manage its state:

```javascript
const targetPressure = ref('');
const isSettingPressure = ref(false);
const errorMessage = ref('');
const errorTimeout = ref(null);
```

These reactive references ensure the UI automatically updates when state changes.

### Input Validation
The component implements client-side validation through a computed property:

```javascript
const isValidPressure = computed(() => {
  const p = parseFloat(targetPressure.value);
  return !isNaN(p) && p >= 0 && p <= (props.pressureData?.maximum || 1000);
});
```

This validation prevents invalid pressure values from being sent to the backend.

```mermaid
sequenceDiagram
participant User as "User"
participant UI as "LevelerControl UI"
participant Component as "LevelerControl Component"
participant API as "apiService"
User->>UI : Enter pressure value
UI->>Component : Update targetPressure
Component->>Component : Validate input via isValidPressure
User->>UI : Click Set button
alt Valid pressure and connected
UI->>Component : Call setPressure()
Component->>Component : Set isSettingPressure = true
Component->>API : setLevelerPressure(target)
API-->>Component : Promise
alt Success
Component->>Component : Emit pressure-set event
Component->>Component : Emit success event
Component->>Component : Clear targetPressure
Component->>Component : Set isSettingPressure = false
Component->>UI : Update UI state
else Error
Component->>Component : Call showError()
Component->>Component : Set errorMessage
Component->>Component : Emit error event
Component->>Component : Set isSettingPressure = false
end
else Invalid or disconnected
UI->>Component : Button disabled
end
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

## Backend Integration
The LevelerControl component integrates with the backend system through the api.js service, which handles all HTTP communication with the server.

### API Service Integration
The component imports and uses the apiService from the centralized API service:

```javascript
import apiService from '../services/api';
```

This service provides a consistent interface for all API calls in the application.

### Leveler-Specific API Methods
The api.js service implements three methods for leveler control:

#### getLevelerPressure()
Retrieves current pressure information from the backend:
```javascript
getLevelerPressure() {
  return apiClient.get('/recoater/leveler/pressure')
}
```

#### setLevelerPressure(target)
Sets the target pressure for the leveler:
```javascript
setLevelerPressure(target) {
  return apiClient.put('/recoater/leveler/pressure', { target })
}
```

#### getLevelerSensor()
Retrieves the state of the magnetic sensor:
```javascript
getLevelerSensor() {
  return apiClient.get('/recoater/leveler/sensor')
}
```

### Backend Endpoint Implementation
The backend implements these API endpoints in the recoater_controls.py file:

#### GET /recoater/leveler/pressure
```python
@router.get("/leveler/pressure")
async def get_leveler_pressure(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    try:
        logger.info("Getting leveler pressure")
        pressure_data = client.get_leveler_pressure()
        response = {
            "leveler_pressure": pressure_data,
            "connected": True
        }
        return response
    except RecoaterConnectionError as e:
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
```

#### PUT /recoater/leveler/pressure
```python
@router.put("/leveler/pressure")
async def set_leveler_pressure(
    pressure_request: LevelerPressureRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    try:
        logger.info(f"Setting leveler pressure: {pressure_request}")
        response_data = client.set_leveler_pressure(
            target=pressure_request.target
        )
        response = {
            "leveler_command": pressure_request.model_dump(),
            "response": response_data,
            "connected": True
        }
        return response
    except RecoaterConnectionError as e:
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
```

### Request Validation
The backend validates pressure requests using Pydantic:

```python
class LevelerPressureRequest(BaseModel):
    """Request model for leveler pressure commands."""
    target: float = Field(..., ge=0, description="Target leveler pressure [Pa]")
```

This ensures that:
- The target value is provided
- The target value is non-negative (ge=0)
- The value is properly typed as a float

### Client Implementation
The RecoaterClient class implements the actual hardware communication:

```python
def get_leveler_pressure(self) -> Dict[str, Any]:
    """Get the leveler pressure information."""
    return self._make_request("GET", "/leveler/pressure")

def set_leveler_pressure(self, target: float) -> Dict[str, Any]:
    """Set the target pressure for the leveler."""
    payload = {"target": target}
    return self._make_request("PUT", "/leveler/pressure", json=payload)
```

```mermaid
sequenceDiagram
participant Frontend as "LevelerControl.vue"
participant ApiService as "api.js"
participant Backend as "FastAPI Backend"
participant Client as "RecoaterClient"
participant Hardware as "Recoater Hardware"
Frontend->>ApiService : setLevelerPressure(target)
ApiService->>Backend : PUT /recoater/leveler/pressure {target}
Backend->>Client : set_leveler_pressure(target)
Client->>Hardware : HTTP PUT /leveler/pressure {target}
Hardware-->>Client : 200 OK {response}
Client-->>Backend : response data
Backend-->>ApiService : JSON response
ApiService-->>Frontend : Promise resolution
Frontend->>Frontend : Emit success events
```

**Diagram sources**
- [api.js](file://frontend/src/services/api.js#L322-L371)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L732-L811)
- [recoater_client.py](file://backend/services/recoater_client.py#L700-L720)

**Section sources**
- [api.js](file://frontend/src/services/api.js#L322-L371)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L732-L811)
- [recoater_client.py](file://backend/services/recoater_client.py#L700-L720)

## Testing Coverage
The LevelerControl component is thoroughly tested with unit tests that validate its functionality, user interactions, and error handling.

### Test Suite Overview
The LevelerControl.test.js file contains comprehensive tests that cover:

- Component rendering with data
- State management
- User interactions
- API integration
- Error handling
- UI state transitions

### Key Test Cases
The test suite includes the following critical test cases:

#### Rendering Tests
```javascript
it('renders correctly with pressure and sensor data', () => {
  expect(wrapper.find('h3').text()).toBe('Leveler Control')
  expect(wrapper.text()).toContain('4.80 Pa')
  expect(wrapper.text()).toContain('5.00 Pa')
  expect(wrapper.text()).toContain('10.00 Pa')
})
```

#### State Management Tests
```javascript
it('shows disconnected state when not connected', () => {
  expect(wrapper.text()).toContain('Disconnected')
  expect(wrapper.find('button').attributes('disabled')).toBeDefined()
})
```

#### Input Validation Tests
```javascript
it('validates pressure input correctly', async () => {
  await input.setValue('7.5')
  expect(button.attributes('disabled')).toBeUndefined()
  
  await input.setValue('-1')
  expect(button.attributes('disabled')).toBeDefined()
})
```

#### API Integration Tests
```javascript
it('emits pressure-set event when pressure is set successfully', async () => {
  await input.setValue('7.5')
  await button.trigger('click')
  
  expect(mockApiService.default.setLevelerPressure).toHaveBeenCalledWith(7.5)
  expect(wrapper.emitted('pressure-set')).toBeTruthy()
})
```

#### Error Handling Tests
```javascript
it('handles API errors gracefully', async () => {
  mockApiService.default.setLevelerPressure.mockRejectedValue(mockError)
  await button.trigger('click')
  
  expect(wrapper.emitted('error')).toBeTruthy()
  expect(wrapper.text()).toContain('Connection failed')
})
```

#### UI State Transition Tests
```javascript
it('disables controls when setting pressure', async () => {
  await button.trigger('click')
  expect(input.attributes('disabled')).toBeDefined()
  expect(button.text()).toContain('Setting...')
})
```

```mermaid
flowchart TD
A[LevelerControl Test Suite] --> B[Rendering Tests]
A --> C[State Tests]
A --> D[Validation Tests]
A --> E[API Integration Tests]
A --> F[Error Handling Tests]
A --> G[UI State Tests]
B --> B1[Correct title display]
B --> B2[Pressure data rendering]
B --> B3[Sensor state display]
C --> C1[Connected state]
C --> C2[Disconnected state]
C --> C3[Empty data handling]
D --> D1[Valid pressure input]
D --> D2[Invalid negative input]
D --> D3[Exceeds maximum input]
E --> E1[API call with correct parameter]
E --> E2[Emits pressure-set event]
E --> E3[Emits success event]
F --> F1[Displays error message]
F --> F2[Emits error event]
F --> F3[Auto-dismiss after 5s]
G --> G1[Disables controls during operation]
G --> G2[Shows "Setting..." text]
G --> G3[Re-enables controls on completion]
```

**Diagram sources**
- [LevelerControl.test.js](file://frontend/src/components/__tests__/LevelerControl.test.js#L1-L195)

**Section sources**
- [LevelerControl.test.js](file://frontend/src/components/__tests__/LevelerControl.test.js#L1-L195)

## Error Handling
The LevelerControl component implements comprehensive error handling at multiple levels to ensure robust operation.

### Client-Side Error Management
The component uses a dedicated error handling function:

```javascript
const showError = (error) => {
  const message = error?.response?.data?.detail || error?.message || 'An error occurred';
  errorMessage.value = message;
  if (errorTimeout.value) {
    clearTimeout(errorTimeout.value);
  }
  errorTimeout.value = setTimeout(() => {
    errorMessage.value = '';
  }, 5000);
};
```

Key features:
- Extracts error details from API responses
- Provides fallback messages for unexpected errors
- Automatically clears messages after 5 seconds
- Prevents multiple timeout conflicts

### Backend Error Responses
The backend returns standardized error responses:

#### Connection Errors (503)
```json
{
  "detail": "Connection to recoater failed: [error details]"
}
```

#### Validation Errors (422)
```json
{
  "detail": [
    {
      "loc": ["body", "target"],
      "msg": "ensure this value is greater than or equal to 0",
      "type": "value_error.number.not_ge"
    }
  ]
}
```

#### API Errors (400)
```json
{
  "detail": "Recoater API error: [error details]"
}
```

### Error Propagation
Errors are propagated through the system:

1. Hardware/Network failure → ConnectionError
2. Invalid API response → APIError 
3. Server processing error → HTTPException
4. Frontend receives error → showError() → UI display

### User Experience Considerations
The error handling prioritizes user experience:

- **Immediate Feedback**: Errors appear directly in the UI
- **Specific Messages**: Users see the actual error cause
- **Non-blocking**: Other functionality remains available
- **Automatic Cleanup**: Messages don't clutter the interface
- **Event Emission**: Parent components can respond to errors

```mermaid
sequenceDiagram
participant Hardware as "Recoater Hardware"
participant Backend as "FastAPI Backend"
participant Frontend as "LevelerControl"
participant User as "User"
Hardware->>Backend : Connection failure
Backend->>Backend : Raise RecoaterConnectionError
Backend->>Frontend : 503 Response with detail
Frontend->>Frontend : Call showError()
Frontend->>Frontend : Set errorMessage
Frontend->>Frontend : Start 5-second timer
Frontend->>User : Display error message
Frontend->>Frontend : Clear message after 5s
User->>Frontend : Sees transient error notification
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L732-L811)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)
- [recoater_controls.py](file://backend/app/api/recoater_controls.py#L732-L811)

## Performance Considerations
The LevelerControl component is designed with performance in mind, particularly for handling rapid status updates without UI lag.

### Efficient Reactivity
The component uses Vue 3's reactivity system efficiently:

- **Minimal Reactive State**: Only essential variables are reactive
- **Computed Properties**: Validation is cached and only recalculated when inputs change
- **Event-Driven Updates**: UI only updates when necessary

### API Call Optimization
The component minimizes unnecessary API calls:

- **Validation Before Call**: Checks input validity before making API requests
- **Connection Check**: Only attempts calls when connected
- **Loading State**: Prevents multiple concurrent calls

### UI Rendering Performance
The component's template is optimized for fast rendering:

- **Simple Structure**: Minimal nested elements
- **Efficient Directives**: Uses v-if and v-show appropriately
- **Class Binding**: Uses object syntax for dynamic classes

### Best Practices for Status Updates
For optimal performance with rapid status updates:

#### Use Throttling for Frequent Updates
```javascript
// In parent component or store
import { throttle } from 'lodash';

const throttledUpdate = throttle((status) => {
  // Update component props
}, 100); // Update at most every 100ms
```

#### Implement Status Change Detection
```javascript
// Only update when values actually change
watch(pressureData, (newData, oldData) => {
  if (newData.value !== oldData?.value || 
      newData.target !== oldData?.target) {
    // Update UI
  }
}, { deep: true });
```

#### Use Virtual Scrolling for Large Data Sets
When displaying historical pressure data, implement virtual scrolling to maintain performance.

#### Optimize CSS Transitions
```css
/* Use transform and opacity for smooth animations */
.card-content {
  transition: opacity 0.2s ease-in-out;
}

.status-dot {
  transition: background-color 0.3s ease;
}
```

#### Minimize Layout Thrashing
Batch DOM updates and avoid forced synchronous layouts.

```mermaid
flowchart TD
A[Performance Optimization] --> B[Reactivity]
A --> C[API Calls]
A --> D[UI Rendering]
A --> E[Status Updates]
B --> B1[Minimal reactive state]
B --> B2[Computed properties]
B --> B3[Event-driven updates]
C --> C1[Validate before calling]
C --> C2[Check connection status]
C --> C3[Prevent concurrent calls]
D --> D1[Simple template structure]
D --> D2[Efficient directives]
D --> D3[Optimized class binding]
E --> E1[Throttle frequent updates]
E --> E2[Detect actual changes]
E --> E3[Avoid unnecessary re-renders]
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)

## Common Issues and Solutions
This section addresses common problems encountered with the LevelerControl component and provides solutions.

### Mechanical Delay Feedback
**Problem**: The leveler mechanism has mechanical delays, causing a lag between command issuance and actual pressure change.

**Solutions**:
1. **Implement Polling**: Continuously poll for pressure updates after command
```javascript
const pollPressure = async () => {
  const startTime = Date.now();
  const timeout = 10000; // 10 second timeout
  
  while (Date.now() - startTime < timeout) {
    const response = await apiService.getLevelerPressure();
    if (Math.abs(response.data.leveler_pressure.value - target) < 0.1) {
      return; // Target reached
    }
    await new Promise(resolve => setTimeout(resolve, 500));
  }
};
```

2. **Show Progress Indicators**: Provide visual feedback during mechanical movement
3. **Implement Command Queuing**: Prevent conflicting commands during movement

### Command Queuing Conflicts
**Problem**: Rapid successive commands can conflict with each other, causing unpredictable behavior.

**Solutions**:
1. **Disable Controls During Operation**: As implemented in the current component
2. **Implement Command Queue**: Process commands sequentially
```javascript
class CommandQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
  }
  
  async add(command) {
    this.queue.push(command);
    if (!this.processing) {
      await this.processQueue();
    }
  }
  
  async processQueue() {
    this.processing = true;
    while (this.queue.length > 0) {
      const command = this.queue.shift();
      try {
        await command.execute();
      } catch (error) {
        console.error('Command failed:', error);
      }
    }
    this.processing = false;
  }
}
```

3. **Debounce Rapid Inputs**: Prevent multiple rapid commands
```javascript
import { debounce } from 'lodash';
const debouncedSetPressure = debounce(setPressure, 1000);
```

### Connection Instability
**Problem**: Network connections to the recoater can be unstable, causing intermittent failures.

**Solutions**:
1. **Implement Retry Logic**: Automatically retry failed commands
```javascript
const setPressureWithRetry = async (target, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiService.setLevelerPressure(target);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};
```

2. **Provide Clear Connection Status**: As implemented with the status indicator
3. **Graceful Degradation**: Allow offline operation with local validation

### Pressure Validation Issues
**Problem**: Pressure values may be rejected by the backend despite appearing valid.

**Solutions**:
1. **Implement Range Validation**: Ensure values are within acceptable ranges
2. **Check Data Types**: Ensure numbers are properly parsed
3. **Handle Edge Cases**: Test boundary values (0, maximum, etc.)

### UI Responsiveness
**Problem**: The UI may become unresponsive during long operations.

**Solutions**:
1. **Use Web Workers**: Offload intensive processing
2. **Implement Time Slicing**: Break long operations into chunks
3. **Show Progress**: Keep users informed with loading indicators

```mermaid
flowchart TD
A[Common Issues] --> B[Mechanical Delay]
A --> C[Command Conflicts]
A --> D[Connection Issues]
A --> E[Validation Problems]
A --> F[UI Responsiveness]
B --> B1[Implement polling]
B --> B2[Show progress indicators]
B --> B3[Add completion detection]
C --> C1[Disable controls during operation]
C --> C2[Implement command queue]
C --> C3[Debounce rapid inputs]
D --> D1[Add retry logic]
D --> D2[Improve connection monitoring]
D --> D3[Implement offline mode]
E --> E1[Enhance client validation]
E --> E2[Check data types]
E --> E3[Test edge cases]
F --> F1[Use web workers]
F --> F2[Implement time slicing]
F --> F3[Add loading states]
```

**Diagram sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)
- [api.js](file://frontend/src/services/api.js#L322-L371)

**Section sources**
- [LevelerControl.vue](file://frontend/src/components/LevelerControl.vue#L1-L182)
- [api.js](file://frontend/src/services/api.js#L322-L371)