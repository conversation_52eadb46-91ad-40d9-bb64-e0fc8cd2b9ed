# Status Indicator

<cite>
**Referenced Files in This Document**   
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue)
- [status.js](file://frontend/src/stores/status.js)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [status_poller.py](file://backend/app/services/status_poller.py)
- [main.py](file://backend/app/main.py)
- [heartbeat.py](file://backend/app/utils/heartbeat.py)
- [StatusView.vue](file://frontend/src/views/StatusView.vue)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Functionality](#core-functionality)
3. [Architecture Overview](#architecture-overview)
4. [Component Implementation](#component-implementation)
5. [Status Store Integration](#status-store-integration)
6. [WebSocket Communication Flow](#websocket-communication-flow)
7. [Usage Across Views](#usage-across-views)
8. [Heartbeat Validation and Reconnection](#heartbeat-validation-and-reconnection)
9. [Performance Optimization](#performance-optimization)
10. [Troubleshooting Common Issues](#troubleshooting-common-issues)

## Introduction
The StatusIndicator.vue component serves as a universal visual indicator for system and subsystem health states in the APIRecoater_Ethernet application. It provides real-time feedback on the operational status of critical components including the recoater, drum, and leveler systems. The component uses dynamic color coding, iconography, and tooltip descriptions to communicate status information derived from backend status codes. This documentation details its implementation, integration with reactive data stores, WebSocket-based real-time updates, and usage patterns across different views in the application.

## Core Functionality
The StatusIndicator component provides visual feedback for three primary system states:
- **Online/Connected**: System is operational and communicating normally
- **Offline/Disconnected**: No connection to backend services
- **Warning/Error**: System connected but experiencing issues

The component displays a colored dot indicator with accompanying text and hover tooltips that provide detailed status information. It automatically updates in response to changes in system status received via WebSocket messages from the backend.

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)

## Architecture Overview

```mermaid
graph TD
subgraph "Frontend"
A[StatusIndicator.vue]
B[status.js Store]
C[WebSocket Connection]
end
subgraph "Backend"
D[WebSocket Manager]
E[Status Poller]
F[Heartbeat Service]
G[Recoater Hardware]
end
A --> B
B --> C
C --> D
D --> E
E --> G
F --> G
E --> D
D --> B
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)
- [status.js](file://frontend/src/stores/status.js#L0-L45)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L131)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)
- [heartbeat.py](file://backend/app/utils/heartbeat.py#L0-L50)

## Component Implementation

```mermaid
classDiagram
class StatusIndicator {
+statusClass : computed
+statusText : computed
+statusTooltip : computed
+onMounted() : void
+onUnmounted() : void
}
class StatusStore {
+isConnected : ref
+isHealthy : computed
+lastError : ref
+connectWebSocket() : Promise
+disconnectWebSocket() : void
+updateStatus() : void
}
StatusIndicator --> StatusStore : "uses"
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)
- [status.js](file://frontend/src/stores/status.js#L0-L45)

### Visual Design and Styling
The StatusIndicator component implements a simple yet effective visual design using CSS classes to represent different status states:

- **Connected (Green)**: `#27ae60` with glow effect indicating healthy operation
- **Disconnected (Red)**: `#e74c3c` indicating lost connection
- **Error (Orange)**: `#f39c12` indicating operational issues

The component uses Vue's computed properties to dynamically determine the appropriate CSS class based on the current status store state.

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)

## Status Store Integration

```mermaid
sequenceDiagram
participant UI as StatusIndicator.vue
participant Store as status.js
participant WS as WebSocket
participant Backend as status_poller.py
UI->>Store : useStatusStore()
Store->>WS : connectWebSocket() on mount
WS->>Backend : Establish connection
loop Polling Interval
Backend->>Gatherer : poll recoater status
Gatherer->>Backend : return status data
Backend->>WS : broadcast status update
WS->>Store : receive message
Store->>Store : update state (isConnected, isHealthy, lastError)
Store->>UI : reactive update
UI->>UI : recompute statusClass, statusText, statusTooltip
end
UI->>Store : disconnectWebSocket() on unmount
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)
- [status.js](file://frontend/src/stores/status.js#L0-L45)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)

The StatusIndicator component integrates with the Pinia status store to access reactive status data. The store provides several key properties:
- `isConnected`: Boolean indicating WebSocket connection status
- `isHealthy`: Computed property indicating overall system health
- `lastError`: String containing the most recent error message
- `lastUpdate`: Timestamp of the last status update

The component uses Vue's composition API to access the store and create computed properties that transform the raw status data into user-friendly display values.

**Section sources**
- [status.js](file://frontend/src/stores/status.js#L0-L45)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)

## WebSocket Communication Flow

```mermaid
flowchart TD
Start([Application Start]) --> Initialize["Initialize WebSocket Manager"]
Initialize --> StartPoller["Start Status Poller"]
StartPoller --> Poll["Poll Interval Reached?"]
Poll --> |Yes| Gather["Gather Data from Recoater"]
Gather --> Check["Any Active Connections?"]
Check --> |Yes| Construct["Construct Status Message"]
Construct --> Broadcast["Broadcast to All Subscribed Clients"]
Broadcast --> Wait["Wait for Next Interval"]
Wait --> Poll
Check --> |No| Wait
Poll --> |No| Wait
ClientConnect([Client Connects]) --> Accept["Accept WebSocket Connection"]
Accept --> InitializeSub["Initialize Subscription (status only)"]
InitializeSub --> Listen["Listen for Messages"]
Listen --> MessageReceived{"Message Received?"}
MessageReceived --> |Yes| IsSubscribe{"Type: subscribe?"}
IsSubscribe --> |Yes| UpdateSub["Update Connection Subscription"]
UpdateSub --> Listen
IsSubscribe --> |No| HandleOther["Handle Other Message Types"]
HandleOther --> Listen
MessageReceived --> |No| CheckDisconnect{"Disconnected?"}
CheckDisconnect --> |Yes| Cleanup["Remove Connection"]
Cleanup --> End([Connection Closed])
CheckDisconnect --> |No| Listen
```

**Diagram sources**
- [main.py](file://backend/app/main.py#L0-L153)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L131)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)

The WebSocket communication system follows a publish-subscribe pattern where the backend periodically polls the recoater hardware and broadcasts status updates to all connected clients. The WebSocket manager handles connection lifecycle management, subscription tracking, and message broadcasting with filtering based on client subscriptions.

**Section sources**
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L0-L131)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)
- [main.py](file://backend/app/main.py#L0-L153)

## Usage Across Views

### StatusView Integration
The StatusIndicator pattern is implemented in the StatusView to display system-wide status information:

```mermaid
flowchart TD
A[StatusView] --> B[Connection Status Card]
B --> C[Backend Status]
C --> D[StatusIndicator Pattern]
B --> E[Recoater Status]
E --> D
A --> F[System Information Card]
F --> G[State Information]
F --> H[Version Information]
```

**Diagram sources**
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L0-L268)

### RecoaterView Integration
In the RecoaterView, the status indicator is used to monitor the recoater subsystem specifically:

```mermaid
flowchart TD
A[RecoaterView] --> B[Status Card]
B --> C[Status Header]
C --> D[Status Title]
C --> E[Status Indicator]
E --> F[status-dot]
F --> G[status-connected/disconnected]
B --> H[Control Section]
H --> I[Recoater Controls]
```

**Diagram sources**
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue#L195-L262)

The component is reused across multiple views with consistent styling and behavior, providing a unified user experience for status monitoring throughout the application.

**Section sources**
- [StatusView.vue](file://frontend/src/views/StatusView.vue#L0-L268)
- [RecoaterView.vue](file://frontend/src/views/RecoaterView.vue#L195-L262)

## Heartbeat Validation and Reconnection

```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant Heartbeat
participant Hardware
Frontend->>Backend : WebSocket Connect
Backend->>Heartbeat : start_heartbeat_task()
Heartbeat->>Hardware : Send heartbeat signal
loop Every 5 seconds
Hardware-->>Heartbeat : Respond to heartbeat
Heartbeat->>Backend : Connection confirmed
end
Hardware--x Heartbeat : No response
Heartbeat->>Backend : Mark as disconnected
Backend->>Frontend : Broadcast connection error
Frontend->>Frontend : Update status to disconnected
Backend->>Backend : Attempt reconnection
Backend->>Hardware : Reconnect sequence
Hardware-->>Backend : Connection restored
Backend->>Frontend : Broadcast reconnection
Frontend->>Frontend : Update status to connected
```

**Diagram sources**
- [heartbeat.py](file://backend/app/utils/heartbeat.py#L0-L50)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)
- [status.js](file://frontend/src/stores/status.js#L0-L45)

The heartbeat mechanism addresses the issue of stale status display after reconnection by implementing periodic validation of the hardware connection. The backend's heartbeat service sends regular signals to the recoater hardware and monitors for responses. If no response is received within a timeout period, the system marks the connection as disconnected and broadcasts this status to all clients, ensuring that the StatusIndicator accurately reflects the true connection state even after network interruptions.

**Section sources**
- [heartbeat.py](file://backend/app/utils/heartbeat.py#L0-L50)
- [status_poller.py](file://backend/app/services/status_poller.py#L0-L124)

## Performance Optimization

### DOM Update Minimization Strategy
To minimize DOM updates when status changes frequently, the following optimizations are implemented:

```mermaid
flowchart TD
A[Status Change Detected] --> B{Significant Change?}
B --> |Yes| C[Update DOM]
C --> D[Apply CSS Transition]
D --> E[Render Update]
B --> |No| F[Skip DOM Update]
F --> G[Update Internal State Only]
G --> H[Batch Updates if Needed]
```

**Diagram sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)

Key performance optimizations include:
- **Debounced updates**: Status changes are batched to avoid excessive DOM manipulation
- **CSS transitions**: Visual changes use CSS transitions rather than JavaScript animations
- **Selective rendering**: Only components that need to update actually re-render
- **Efficient computed properties**: Computed properties are used to cache derived state

The component leverages Vue's reactivity system efficiently by using computed properties that only recalculate when their dependencies change, reducing unnecessary computations.

**Section sources**
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)

## Troubleshooting Common Issues

### Stale Status Display
**Issue**: Status indicator shows "Connected" even when the recoater is physically disconnected.

**Root Cause**: The WebSocket connection between frontend and backend remains active even when the backend loses connection to the recoater hardware.

**Solution**: Implement heartbeat validation as described in the heartbeat.py module. The backend should:
1. Send periodic heartbeat signals to the recoater hardware
2. Track response times and timeouts
3. Broadcast disconnection status when heartbeats fail
4. Update the status store accordingly

### WebSocket Reconnection Problems
**Issue**: Status indicator remains "Disconnected" after network recovery.

**Solution**: Implement automatic reconnection logic:
- Frontend: Attempt WebSocket reconnection with exponential backoff
- Backend: Validate hardware connection before marking as connected
- Use the heartbeat mechanism to confirm end-to-end connectivity

### Performance Degradation with Frequent Updates
**Issue**: UI becomes sluggish when status updates occur rapidly.

**Solution**: Implement the following optimizations:
- Limit polling frequency to necessary minimum
- Debounce status updates to batch rapid changes
- Use CSS transitions instead of JavaScript animations
- Ensure computed properties are efficient and don't perform heavy calculations

**Section sources**
- [heartbeat.py](file://backend/app/utils/heartbeat.py#L0-L50)
- [status.js](file://frontend/src/stores/status.js#L0-L45)
- [StatusIndicator.vue](file://frontend/src/components/StatusIndicator.vue#L0-L98)