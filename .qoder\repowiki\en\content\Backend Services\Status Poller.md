# Status Poller

<cite>
**Referenced Files in This Document**   
- [status_poller.py](file://backend/app/services/status_poller.py)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py)
- [recoater_client.py](file://backend/services/recoater_client.py)
- [status.py](file://backend/app/api/status.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)
- [opcua_server.py](file://backend/app/services/opcua_server.py)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Polling Architecture](#polling-architecture)
4. [Data Gathering and Aggregation](#data-gathering-and-aggregation)
5. [Status API Integration](#status-api-integration)
6. [Configuration Options](#configuration-options)
7. [Error Handling and Reliability](#error-handling-and-reliability)
8. [Performance Considerations](#performance-considerations)
9. [Extensibility and Integration](#extensibility-and-integration)

## Introduction

The StatusPoller service in APIRecoater_Ethernet is responsible for continuously monitoring the health and operational state of the recoater system. It implements a background polling mechanism that retrieves status information from the recoater hardware at configurable intervals and broadcasts updates to connected clients via WebSocket. The service plays a critical role in providing real-time system monitoring for the Human-Machine Interface (HMI) and other components that require up-to-date status information.

Contrary to the initial documentation objective mentioning OPC UA node polling, the StatusPoller service does not directly poll OPC UA nodes. Instead, it retrieves system status through direct HTTP API calls to the recoater hardware using the RecoaterClient. The OPC UA server in this system serves a different purpose—facilitating coordination between the backend and PLC (Programmable Logic Controller) for multi-material print jobs—rather than being a source for general system health monitoring.

The service is designed with efficiency in mind, minimizing network load by only gathering data types that are currently subscribed to by connected clients. It also implements robust error handling to maintain stability during temporary connectivity issues with the recoater hardware.

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L1-L124)
- [recoater_client.py](file://backend/services/recoater_client.py#L1-L724)

## Architecture Overview

The StatusPoller service operates as part of a larger architecture that enables real-time monitoring of the recoater system. It sits between the recoater hardware API and the frontend HMI, acting as a bridge that transforms hardware status into real-time updates for user interfaces.

```mermaid
graph TD
subgraph "Frontend"
HMI[Human-Machine Interface]
WSClient[WebSocket Client]
end
subgraph "Backend"
StatusPoller[StatusPollingService]
DataGatherer[RecoaterDataGatherer]
WebsocketManager[WebSocketConnectionManager]
RecoaterClient[RecoaterClient]
end
subgraph "Hardware"
RecoaterAPI[Recoater Hardware API]
end
HMI --> WSClient
WSClient --> |WebSocket Connection| WebsocketManager
WebsocketManager --> StatusPoller
StatusPoller --> DataGatherer
DataGatherer --> RecoaterClient
RecoaterClient --> |HTTP Requests| RecoaterAPI
RecoaterAPI --> |Status Data| RecoaterClient
RecoaterClient --> DataGatherer
DataGatherer --> StatusPoller
StatusPoller --> |Broadcast Updates| WebsocketManager
WebsocketManager --> |WebSocket Messages| WSClient
WSClient --> HMI
style StatusPoller fill:#f9f,stroke:#333
style DataGatherer fill:#bbf,stroke:#333
style WebsocketManager fill:#f96,stroke:#333
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L146)
- [recoater_client.py](file://backend/services/recoater_client.py#L1-L724)

## Polling Architecture

The StatusPoller service implements a continuous polling loop that runs in a background task, retrieving status information from the recoater system at regular intervals. The core component is the `StatusPollingService` class, which manages the lifecycle of the polling task and coordinates data collection and broadcasting.

The polling architecture follows a producer-consumer pattern where the StatusPollingService acts as the producer of status updates, and connected WebSocket clients act as consumers. The service uses asyncio to handle asynchronous operations, allowing it to perform non-blocking HTTP requests to the recoater API while maintaining responsiveness.

The main polling loop is implemented in the `_polling_loop` method, which runs continuously while the service is active. During each iteration, the service attempts to gather status data and broadcast it to connected clients. If a connection error occurs, it broadcasts an error message instead of regular status updates.

```mermaid
sequenceDiagram
participant Poller as StatusPollingService
participant Gatherer as RecoaterDataGatherer
participant Client as RecoaterClient
participant WSManager as WebSocketConnectionManager
participant Frontend as Frontend Client
loop Polling Interval
Poller->>Poller : Check if running
Poller->>Poller : Enter polling loop
Poller->>Poller : Try to poll and broadcast
Poller->>Gatherer : get_required_data_types()
Gatherer-->>Poller : Return subscribed data types
Poller->>Gatherer : gather_all_data(client, types)
Gatherer->>Client : get_state()
Client-->>Gatherer : Return system state
Gatherer->>Client : get_drums(), get_leveler_pressure(), etc.
Client-->>Gatherer : Return sensor data
Gatherer-->>Poller : Return gathered data
Poller->>Gatherer : construct_status_message(data)
Poller->>WSManager : broadcast(message)
WSManager->>Frontend : Send JSON message
Poller->>Poller : Sleep for poll_interval
end
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)

## Data Gathering and Aggregation

The StatusPoller service delegates data collection responsibilities to the `RecoaterDataGatherer` class, which is responsible for retrieving various types of status information from the recoater hardware. This separation of concerns allows the polling service to focus on scheduling and broadcasting, while the data gatherer handles the specifics of data retrieval.

The data gathering process is optimized for efficiency by only collecting data types that are currently subscribed to by connected clients. This is determined by the `get_required_data_types()` method of the WebSocketConnectionManager, which aggregates subscription preferences from all active connections.

The `gather_all_data` method in the `RecoaterDataGatherer` class coordinates the collection of different data types:
- **Status**: Basic system status retrieved via `get_state()`
- **Axis**: Axis position and motion data (currently placeholder)
- **Drum**: Comprehensive drum information including motion, ejection, suction, and blade screw data
- **Leveler**: Leveler pressure and sensor status
- **Print**: Layer parameters and print job status

```mermaid
flowchart TD
Start([Start gather_all_data]) --> GetMainStatus["Call client.get_state()"]
GetMainStatus --> CheckSubscriptions["Check required_data_types"]
CheckSubscriptions --> |axis in types| GatherAxis["gather_axis_data(client)"]
CheckSubscriptions --> |drum in types| GatherDrum["gather_all_drum_data(client)"]
CheckSubscriptions --> |leveler in types| GatherLeveler["gather_leveler_data(client)"]
CheckSubscriptions --> |print in types| GatherPrint["gather_print_data(client)"]
GatherAxis --> WaitAll["Wait for all tasks to complete"]
GatherDrum --> WaitAll
GatherLeveler --> WaitAll
GatherPrint --> WaitAll
WaitAll --> ConstructData["Construct result dictionary"]
ConstructData --> ReturnData["Return gathered data"]
ReturnData --> End([End])
style GatherAxis fill:#f96,stroke:#333
style GatherDrum fill:#6f9,stroke:#333
style GatherLeveler fill:#96f,stroke:#333
style GatherPrint fill:#69f,stroke:#333
```

**Diagram sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)

**Section sources**
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)

## Status API Integration

The StatusPoller service works in conjunction with the Status API endpoints defined in `status.py`, which provide HTTP-based access to system status information. While the StatusPoller focuses on real-time WebSocket updates, the Status API provides a RESTful interface for clients that prefer request-response patterns.

The `/status` endpoint returns comprehensive system status including connection health, recoater status, and backend status. This endpoint is used by clients for initial status retrieval or when WebSocket connectivity is not available. The `/status/health` endpoint provides a simple health check that verifies connectivity to the recoater hardware.

The integration between the StatusPoller and Status API ensures consistency in status reporting across different communication channels. Both components use the same `RecoaterClient` instance to retrieve hardware status, ensuring that the information provided through WebSocket and HTTP interfaces is synchronized.

```mermaid
sequenceDiagram
participant Frontend as Frontend Client
participant APIRouter as Status API Router
participant Poller as StatusPollingService
participant Client as RecoaterClient
Frontend->>APIRouter : GET /status
APIRouter->>Client : client.get_state()
Client-->>APIRouter : Return state data
APIRouter-->>Frontend : Return status response
Frontend->>APIRouter : GET /status/health
APIRouter->>Client : client.health_check()
Client-->>APIRouter : Return health status
APIRouter-->>Frontend : Return health response
Poller->>Client : client.get_state() (periodic)
Client-->>Poller : Return state data
Poller->>Frontend : Broadcast via WebSocket
```

**Diagram sources**
- [status.py](file://backend/app/api/status.py#L1-L154)
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [recoater_client.py](file://backend/services/recoater_client.py#L1-L724)

**Section sources**
- [status.py](file://backend/app/api/status.py#L1-L154)

## Configuration Options

The StatusPoller service provides several configuration options to customize its behavior according to system requirements and performance considerations. The primary configuration is the polling interval, which determines how frequently the service queries the recoater hardware for status updates.

The polling interval is controlled by the `WEBSOCKET_POLL_INTERVAL` environment variable, with a default value of 1.0 seconds. This can be dynamically adjusted at runtime using the `update_poll_interval()` method, allowing the system to adapt to different operational phases (e.g., more frequent polling during active printing).

```python
def update_poll_interval(self, interval: float) -> None:
    """
    Update the polling interval.
    
    Args:
        interval: New polling interval in seconds
    """
    self.poll_interval = interval
    logger.info(f"Updated polling interval to {interval} seconds")
```

The service also supports subscription-based data filtering, where connected clients can specify which types of data they are interested in receiving. This reduces network bandwidth and processing overhead by only gathering and transmitting relevant information.

Configuration is primarily managed through environment variables, following the 12-factor app methodology. The default polling interval of 1 second represents a balance between real-time responsiveness and network efficiency, but can be adjusted based on specific deployment requirements.

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)

## Error Handling and Reliability

The StatusPoller service implements comprehensive error handling to ensure reliable operation in the face of network issues, hardware failures, and other exceptional conditions. The service is designed to gracefully handle connection problems with the recoater hardware without crashing or requiring restart.

When a connection error occurs during polling, the service catches the `RecoaterConnectionError` or `RecoaterAPIError` exception and broadcasts a connection error message to all connected clients. This allows the frontend to display appropriate error states to users while the service continues attempting to reconnect.

```mermaid
flowchart TD
A[_polling_loop] --> B{self._running?}
B --> |Yes| C[Call _poll_and_broadcast]
B --> |No| D[Exit loop]
C --> E{Success?}
E --> |Yes| F[Sleep for poll_interval]
E --> |No| G{Connection Error?}
G --> |Yes| H[_broadcast_connection_error]
G --> |No| I[Log unexpected error]
H --> J[Sleep for poll_interval]
F --> A
J --> A
I --> J
style E fill:#f96,stroke:#333
style G fill:#f96,stroke:#333
```

The service also handles WebSocket disconnections by automatically cleaning up disconnected clients during the broadcast process. If a message cannot be sent to a client, the service logs a warning and removes the client from the active connections list.

For critical errors that are not specifically handled, the service logs the exception with full traceback information while continuing the polling loop. This fault-tolerant design ensures that transient issues do not disrupt the overall monitoring functionality.

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)

## Performance Considerations

The StatusPoller service is designed with performance and efficiency as key considerations, particularly in minimizing network load and system resource usage. The service implements several optimization strategies to balance real-time monitoring requirements with system performance.

The most significant performance optimization is the subscription-based data gathering approach. Instead of retrieving all possible status data on every poll, the service only collects data types that are currently subscribed to by connected clients. This reduces both the number of API calls to the recoater hardware and the amount of data transmitted over the network.

The service also uses concurrent execution for data retrieval operations. When multiple data types are requested, the `gather_all_data` method creates separate tasks for each data type and executes them concurrently using `asyncio.gather()`. This significantly reduces the total time required to collect comprehensive status information.

```python
# Execute only the required data gathering tasks
axis_data, drum_data, leveler_data, print_data = await asyncio.gather(*gather_tasks)
```

The polling interval provides a direct control over the frequency of hardware queries, allowing system administrators to tune the balance between real-time responsiveness and network load. The default 1-second interval represents a reasonable compromise for most use cases.

Additional performance considerations include:
- Using `asyncio.to_thread()` for synchronous client methods to prevent blocking the event loop
- Implementing connection pooling through the persistent `RecoaterClient` session
- Minimizing memory usage by reusing data structures where possible
- Efficient error handling that doesn't disrupt the polling loop

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)

## Extensibility and Integration

The StatusPoller service architecture supports extensibility and integration with external monitoring systems through its modular design and well-defined interfaces. The separation of concerns between polling, data gathering, and message broadcasting components makes it relatively straightforward to extend functionality.

To add custom metrics, developers can extend the `RecoaterDataGatherer` class with new methods for retrieving additional sensor data or system information. New data types can be integrated by adding corresponding methods and updating the `gather_all_data` method to conditionally include the new data type based on client subscriptions.

Integration with external monitoring systems can be achieved through several approaches:
1. **WebSocket Integration**: External systems can connect as WebSocket clients to receive real-time status updates
2. **HTTP API**: The Status API endpoints provide a standard RESTful interface for status retrieval
3. **OPC UA Server**: For PLC integration, the system provides an OPC UA server with coordination variables

The OPC UA server, implemented in `opcua_server.py`, hosts coordination variables that facilitate communication between the backend and PLC systems. These variables include job control flags, layer information, and error status indicators that enable synchronized operation in multi-material print jobs.

```mermaid
classDiagram
class StatusPollingService {
+websocket_manager : WebSocketConnectionManager
+data_gatherer : RecoaterDataGatherer
+poll_interval : float
+polling_task : Optional[Task]
+_running : bool
+start() void
+stop() void
+_polling_loop() void
+_poll_and_broadcast() void
+update_poll_interval(interval : float) void
+is_running() bool
}
class RecoaterDataGatherer {
+gather_all_data(client : Any, required_data_types : Set[str]) Dict[str,Any]
+construct_status_message(gathered_data : Dict[str,Any]) Dict[str,Any]
+gather_axis_data(client : Any) Optional[Dict[str,Any]]
+gather_all_drum_data(client : Any) Optional[Dict[str,Any]]
+gather_leveler_data(client : Any) Optional[Dict[str,Any]]
+gather_print_data(client : Any) Optional[Dict[str,Any]]
+_safe_api_call(method, *args) Optional[Any]
}
class WebSocketConnectionManager {
+active_connections : List[WebSocket]
+connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket : WebSocket) void
+disconnect(websocket : WebSocket) void
+update_subscription(websocket : WebSocket, data_types : List[str]) void
+broadcast(message : Dict[str,Any]) void
+get_required_data_types() Set[str]
+has_connections() bool
}
StatusPollingService --> RecoaterDataGatherer : "uses"
StatusPollingService --> WebSocketConnectionManager : "depends on"
RecoaterDataGatherer --> RecoaterClient : "calls"
WebSocketConnectionManager --> StatusPollingService : "notifies"
```

**Diagram sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L146)

**Section sources**
- [status_poller.py](file://backend/app/services/status_poller.py#L21-L123)
- [data_gatherer.py](file://backend/app/services/data_gatherer.py#L1-L229)
- [websocket_manager.py](file://backend/app/services/websocket_manager.py#L1-L146)
- [opcua_server.py](file://backend/app/services/opcua_server.py#L1-L524)