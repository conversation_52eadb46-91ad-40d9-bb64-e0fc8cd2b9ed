<template>
  <div class="error-display-panel">
    <!-- System Error Flags Status -->
    <div v-if="printJobStore.hasErrors" class="error-status-card critical">
      <div class="card-header">
        <h3 class="card-title">System Error Status</h3>
        <div class="error-indicator">
          <div class="status-dot status-error"></div>
          <span class="status-text">Errors Detected</span>
        </div>
      </div>
      
      <div class="card-content">
        <div class="error-flags-grid">
          <div 
            v-if="printJobStore.errorFlags.backendError"
            class="error-flag-item backend-error"
          >

            <div class="flag-content">
              <div class="flag-title">Backend Error</div>
              <div class="flag-status">Active</div>
            </div>
          </div>
          
          <div 
            v-if="printJobStore.errorFlags.plcError"
            class="error-flag-item plc-error"
          >

            <div class="flag-content">
              <div class="flag-title">PLC Error</div>
              <div class="flag-status">Active</div>
            </div>
          </div>
        </div>
        
        <div class="error-actions">
          <button
            @click="clearErrors"
            :disabled="printJobStore.isClearingErrors"
            class="btn btn-danger clear-errors-btn"
            data-testid="clear-error-flags-panel-btn"
          >
            <span v-if="printJobStore.isClearingErrors">Clearing...</span>
            <span v-else>Clear Errors</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Job Error Messages -->
    <div v-if="printJobStore.multiMaterialJob.errorMessage" class="error-status-card job-error">
      <div class="card-header">
        <h3 class="card-title">Job Error</h3>
        <div class="error-indicator">
          <div class="status-dot status-warning"></div>
          <span class="status-text">Job Issue</span>
        </div>
      </div>
      
      <div class="card-content">
        <div class="error-message-display">

          <div class="message-content">
            <div class="message-text">{{ printJobStore.multiMaterialJob.errorMessage }}</div>
            <div class="message-timestamp">{{ formatCurrentTime() }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Drum-Specific Errors -->
    <div v-if="hasDrumErrors" class="error-status-card drum-errors">
      <div class="card-header">
        <h3 class="card-title">Drum Errors</h3>
        <div class="error-indicator">
          <div class="status-dot status-warning"></div>
          <span class="status-text">{{ drumErrorCount }} Drum(s)</span>
        </div>
      </div>
      
      <div class="card-content">
        <div class="drum-errors-grid">
          <div 
            v-for="drumId in [0, 1, 2]" 
            :key="drumId"
            v-if="printJobStore.multiMaterialJob.drums[drumId].errorMessage"
            class="drum-error-item"
          >
            <div class="drum-error-header">
              <span class="drum-label">Drum {{ drumId }}</span>
              <div class="status-dot status-error"></div>
            </div>
            <div class="drum-error-message">
              {{ printJobStore.multiMaterialJob.drums[drumId].errorMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error History (Recent Errors) -->
    <div v-if="errorHistory.length > 0" class="error-status-card error-history">
      <div class="card-header">
        <h3 class="card-title">Recent Errors</h3>
        <button 
          @click="clearErrorHistory"
          class="btn btn-sm btn-tertiary"
          data-testid="clear-error-history-btn"
        >
          Clear History
        </button>
      </div>
      
      <div class="card-content">
        <div class="error-history-list">
          <div 
            v-for="(error, index) in errorHistory.slice(0, 5)" 
            :key="index"
            class="error-history-item"
          >
            <div class="error-time">{{ formatTime(error.timestamp) }}</div>
            <div class="error-type">{{ error.type }}</div>
            <div class="error-description">{{ error.message }}</div>
          </div>
        </div>
        
        <div v-if="errorHistory.length > 5" class="history-overflow">
          <span class="overflow-text">
            ... and {{ errorHistory.length - 5 }} more errors
          </span>
        </div>
      </div>
    </div>

    <!-- No Errors State -->
    <div v-if="!hasAnyErrors" class="error-status-card no-errors">
      <div class="card-header">
        <h3 class="card-title">System Status</h3>
        <div class="error-indicator">
          <div class="status-dot status-ok"></div>
          <span class="status-text">No Errors</span>
        </div>
      </div>
      
      <div class="card-content">
        <div class="no-errors-message">

          <div class="success-text">
            <div class="success-title">All Systems Operational</div>
            <div class="success-description">
              No errors detected. System is ready for operation.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { usePrintJobStore } from '../stores/printJobStore'

export default {
  name: 'ErrorDisplayPanel',
  setup() {
    const printJobStore = usePrintJobStore()
    
    // Local state for error history
    const errorHistory = ref([])
    
    // Computed properties
    const hasDrumErrors = computed(() => {
      return Object.values(printJobStore.multiMaterialJob.drums)
        .some(drum => drum.errorMessage)
    })
    
    const drumErrorCount = computed(() => {
      return Object.values(printJobStore.multiMaterialJob.drums)
        .filter(drum => drum.errorMessage).length
    })
    
    const hasAnyErrors = computed(() => {
      return printJobStore.hasErrors || 
             printJobStore.multiMaterialJob.errorMessage ||
             hasDrumErrors.value
    })
    
    // Methods
    const clearErrors = async () => {
      try {
        await printJobStore.clearErrorFlagsAPI()
        addToErrorHistory('System', 'Error flags cleared by operator')
      } catch (error) {
        console.error('Failed to clear error flags:', error)
        addToErrorHistory('System', `Failed to clear error flags: ${error.message}`)
      }
    }
    
    const addToErrorHistory = (type, message) => {
      errorHistory.value.unshift({
        timestamp: Date.now(),
        type,
        message
      })
      
      // Keep only last 20 errors
      if (errorHistory.value.length > 20) {
        errorHistory.value = errorHistory.value.slice(0, 20)
      }
    }
    
    const clearErrorHistory = () => {
      errorHistory.value = []
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }
    
    const formatCurrentTime = () => {
      return new Date().toLocaleTimeString()
    }
    
    // Watch for new errors and add to history
    watch(
      () => printJobStore.errorFlags.backendError,
      (newVal, oldVal) => {
        if (newVal && !oldVal) {
          addToErrorHistory('Backend', 'Backend error flag activated')
        }
      }
    )
    
    watch(
      () => printJobStore.errorFlags.plcError,
      (newVal, oldVal) => {
        if (newVal && !oldVal) {
          addToErrorHistory('PLC', 'PLC error flag activated')
        }
      }
    )
    
    watch(
      () => printJobStore.multiMaterialJob.errorMessage,
      (newVal, oldVal) => {
        if (newVal && newVal !== oldVal) {
          addToErrorHistory('Job', newVal)
        }
      }
    )
    
    // Watch for drum errors
    for (let drumId = 0; drumId <= 2; drumId++) {
      watch(
        () => printJobStore.multiMaterialJob.drums[drumId].errorMessage,
        (newVal, oldVal) => {
          if (newVal && newVal !== oldVal) {
            addToErrorHistory(`Drum ${drumId}`, newVal)
          }
        }
      )
    }
    
    return {
      printJobStore,
      errorHistory,
      hasDrumErrors,
      drumErrorCount,
      hasAnyErrors,
      clearErrors,
      clearErrorHistory,
      formatTime,
      formatCurrentTime
    }
  }
}
</script>

<style scoped>
.error-display-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-status-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-left: 4px solid;
}

.error-status-card.critical {
  border-left-color: #dc3545;
}

.error-status-card.job-error {
  border-left-color: #ffc107;
}

.error-status-card.drum-errors {
  border-left-color: #fd7e14;
}

.error-status-card.error-history {
  border-left-color: #6c757d;
}

.error-status-card.no-errors {
  border-left-color: #28a745;
}

.card-header {
  background: #f8f9fa;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.error-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-ok { background-color: #28a745; }
.status-error { background-color: #dc3545; }
.status-warning { background-color: #ffc107; }

.status-text {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 600;
}

.card-content {
  padding: 1.5rem;
}

.error-flags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.error-flag-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid;
}

.error-flag-item.backend-error {
  background-color: #fff3cd;
  border-color: #ffc107;
}

.error-flag-item.plc-error {
  background-color: #f8d7da;
  border-color: #dc3545;
}

.flag-icon {
  font-size: 1.5rem;
}

.flag-content {
  flex: 1;
}

.flag-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.flag-status {
  font-size: 0.875rem;
  color: #dc3545;
  font-weight: 600;
}

.error-actions {
  display: flex;
  justify-content: flex-end;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  background-color: #d1d5db; /* gray-300 */
  color: #6b7280; /* gray-500 */
  cursor: not-allowed;
  opacity: 1;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-tertiary {
  background-color: #6c757d;
  color: white;
}

.btn-tertiary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.clear-errors-btn {
  min-width: 140px;
}

.error-message-display {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.message-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-text {
  color: #495057;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.message-timestamp {
  font-size: 0.875rem;
  color: #6c757d;
}

.drum-errors-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.drum-error-item {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.drum-error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.drum-label {
  font-weight: 600;
  color: #495057;
}

.drum-error-message {
  color: #dc3545;
  font-size: 0.875rem;
  line-height: 1.4;
}

.error-history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.error-history-item {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  align-items: center;
}

.error-time {
  font-size: 0.875rem;
  color: #6c757d;
  font-family: monospace;
}

.error-type {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  padding: 0.25rem 0.5rem;
  background-color: #e9ecef;
  border-radius: 3px;
}

.error-description {
  font-size: 0.875rem;
  color: #495057;
}

.history-overflow {
  margin-top: 1rem;
  text-align: center;
}

.overflow-text {
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
}

.no-errors-message {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.success-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.success-text {
  flex: 1;
}

.success-title {
  font-weight: 600;
  color: #28a745;
  margin-bottom: 0.25rem;
}

.success-description {
  color: #6c757d;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .error-flags-grid {
    grid-template-columns: 1fr;
  }
  
  .error-history-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .error-time,
  .error-type {
    justify-self: start;
  }
  
  .no-errors-message {
    flex-direction: column;
    text-align: center;
  }
}
</style>
